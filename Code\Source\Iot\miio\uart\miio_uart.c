#include "miio_define.h"
#include "miio_uart.h"
#include "arch_uart.h"
//#include "cpu\inc\dbg_uart.h"
//#include "ModuleConfig.h"
#include"IotUsr.h"

//#define TRUE 1

int miio_uart_init(void)
{
    if( uart_init(UART_MIIO_COMM) == 0 )
        return MIIO_OK;
    return MIIO_ERROR;
}

int miio_uart_send_str(const char* str)
{
    int n_send = 0;
    int len = strlen(str);
    uint32_t timeout_ms = 200;
    
    if (len <= 0) { return 0; }

    APP_LOG_IOT( "wifi:send %s\r", str);
#ifdef RTT_LOG_DEBUG
    log_printf(0, "wifi send : %s", str);
#endif
    while( n_send<len )
    {
        if( uart_send_byte(UART_MIIO_COMM, str[n_send]) )
            n_send++;
        else
        {
            if( timeout_ms == 0 )
                break;
            udelay(1000);
            timeout_ms--;
        }
    }

    return n_send;
}


int miio_uart_recv_str(uint8_t* pbuf, int buf_len, uint32_t timeout_ms)
{
    int n_read = 0;
    uint32_t delay_count = 20*timeout_ms;
    
    while( buf_len>n_read )
    {
        if( uart_recv_byte(UART_MIIO_COMM, pbuf+n_read) )
        {
            n_read++;
            if( pbuf[n_read-1] == '\r' )
                break;
        }
        else
        {
            if( delay_count == 0 )
            {
                break;
            }
            udelay(1);
            delay_count--;
        }
    }
	//log_printf(0, "wifi rec  : %s\n", pbuf);
    return n_read;
}

int miio_uart_recv_str_aync(uint8_t** ppbuf)
{
    static uint8_t recv_buf[CMD_STR_MAX_LEN];
    static int recv_len=0;

    int n_read = 0;
    while( recv_len<CMD_STR_MAX_LEN )
    {
        if( uart_recv_byte(UART_MIIO_COMM, &recv_buf[recv_len]) )
        {
            recv_len++;
            if( recv_buf[recv_len-1] == '\r' )
            {
                *ppbuf = recv_buf;
                n_read = recv_len;
                recv_len = 0; //reset for next command
                return n_read;
            }
        }
        else{
            break;
        }
    }
#ifdef RTT_LOG_DEBUG
    if (*ppbuf != NULL)
        log_printf(0, "wifi rec  : %s\n", *ppbuf);
#endif
    if( recv_len == CMD_STR_MAX_LEN )
    {
        APP_LOG_IOT("Receive data is too long!");
        recv_len = 0;
    }
    return n_read;
}


int miio_uart_send_str_wait_ack(const char* str)
{
    static uint8_t ack_buf[ACK_BUF_SIZE] = { 0 };
    int n_send = miio_uart_send_str(str);


    memset(ack_buf, 0, ACK_BUF_SIZE);

    miio_uart_recv_str(ack_buf, ACK_BUF_SIZE, USER_UART_TIMEOUT_MS);
    if (0 != strncmp((const char*)ack_buf, "ok", strlen("ok"))) {
        APP_LOG_IOT("send string wait ack failed 2 str=%s\n", ack_buf);
        //log_printf(0, "send string wait ack failed 2 str=%s\n", ack_buf);
        return UART_RECV_ACK_ERROR;
    }

    return n_send;
}

int miio_uart_has_more_data(void)
{
	if (uart_recv_buffer_is_empty() != TRUE)
		return 1;

	return 0;
}

#if 0
char putchar(char c)
{
    //_uart_send_byte(NULL, c);
    return c;
}
#endif

