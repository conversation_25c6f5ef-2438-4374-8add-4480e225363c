/*!
 * @file
 * @brief Manages all the state variables of the cooling controller.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include <string.h>
#include "FirewareComm.h"
#include "Crc16_CCITT_FALSE.h"
#include "Driver_Flash.h"
#include "syslog.h"

uint32_t convert_from_bigendian32(uint32_t val)
{
    uint32_t temp = 0;
    uint8_t *p = (uint8_t *)&val;

    temp = (p[0] << 24) |
        (p[1] << 16) |
        (p[2] << 8) |
        p[3];
    return temp;
}

uint16_t convert_from_bigendian16(uint16_t val)
{
    uint16_t temp = 0;
    uint8_t *p = (uint8_t *)&val;

    temp = (p[0] << 8) | p[1];
    return temp;
}

uint32_t convert_to_bigendian32(uint32_t val)
{
    uint32_t temp = 0;
    uint8_t* p = (uint8_t *)&temp;

    p[0] = (val >> 24) & 0xFF;
    p[1] = (val >> 16) & 0xFF;
    p[2] = (val >> 8)  & 0xFF;
    p[3] = (val) & 0xFF;

    return temp;
}

uint16_t convert_to_bigendian16(uint16_t val)
{
    uint16_t temp = 0;
    uint8_t* p = (uint8_t*)&temp;

    p[0] = (val >> 8) & 0xFF;
    p[1] = (val) & 0xFF;

    return temp;
}

void Edit_OtaControlFrame(fireware_frame_st *frame, fireware_addr_e dest, 
                          fireware_ota_subfunc_e func)
{
    uint8_t *data = &frame->head;
    uint16_t sendCrcValue;
    uint8_t index;
    uint8_t len;

    frame->head = FIREWARE_FRAME_HEAD;
    frame->srcAddr = FIREWARE_MAIN_ADDR;
    frame->destAddr = dest;
    frame->fb1 = FIREWARE_OTA_FUNC;
    frame->fb2 = func;
    frame->fb3 = 0;
    frame->len = 0x00;
    frame->end = FIREWARE_FRAME_END;
    sendCrcValue = U16_CRC_INITIAL_VALUE;
    len = offsetof(fireware_frame_st, data);
    for(index = 0; index < len; index++)
    {
        sendCrcValue =
            Cal_CRC_SingleData(sendCrcValue, *data++);
    }

    frame->crch = GET_U16_HIGHBYTE(sendCrcValue);
    frame->crcl = GET_U16_LOWBYTE(sendCrcValue);
    frame->wait_response = true;
    return;
}

void Edit_ParamSnFrame(fireware_frame_st *frame, fireware_addr_e dest, bool rw)
{
    uint8_t *data = &frame->head;
    uint8_t boardsn[PRODUCT_SN_SIZE+1] = {0};
    uint16_t sendCrcValue;
    uint8_t index;
    uint8_t len;

    frame->head = FIREWARE_FRAME_HEAD;
    frame->srcAddr = FIREWARE_MAIN_ADDR;
    frame->destAddr = dest;
    frame->fb1 = FIREWARE_PARAM_FUNC;
    ReadProductSn(boardsn, PRODUCT_SN_SIZE);
    if(rw)
    {
        frame->fb2 = PARAM_SUBFUNC_WRITE_SN;
    }
    else
    {
        frame->fb2 = PARAM_SUBFUNC_READ_SN;
    }
    frame->fb3 = 0x00;
    frame->len = 0x00;
    if(rw)
    {
         frame->len = PRODUCT_SN_SIZE;
         memcpy(frame->data, boardsn, PRODUCT_SN_SIZE);
    }
    frame->end = FIREWARE_FRAME_END;
    sendCrcValue = U16_CRC_INITIAL_VALUE;
    len = offsetof(fireware_frame_st, data) + frame->len;
    for(index = 0; index < len; index++)
    {
        sendCrcValue =
            Cal_CRC_SingleData(sendCrcValue, *data++);
    }
    frame->crch = GET_U16_HIGHBYTE(sendCrcValue);
    frame->crcl = GET_U16_LOWBYTE(sendCrcValue);
    frame->wait_response = true;
    return;
}

void Edit_WriteParamFrame(fireware_frame_st *frame, fireware_addr_e dest,
    uint8_t packet_no, uint8_t *pdata)
{
    uint8_t *data = &frame->head;
    uint16_t sendCrcValue;
    uint8_t index;
    uint8_t len;

    frame->head = FIREWARE_FRAME_HEAD;
    frame->srcAddr = FIREWARE_MAIN_ADDR;
    frame->destAddr = dest;
    frame->fb1 = FIREWARE_PARAM_FUNC;
    frame->fb2 = PARAM_SUBFUNC_WRITE_PARAM;
    frame->fb3 = packet_no;
    frame->len = FIREWARE_PARAM_PACKET_LEN;
    memcpy(frame->data, pdata, FIREWARE_PARAM_PACKET_LEN);
    frame->end = FIREWARE_FRAME_END;
    sendCrcValue = U16_CRC_INITIAL_VALUE;
    len = offsetof(fireware_frame_st, data) + frame->len;
    for(index = 0; index < len; index++)
    {
        sendCrcValue =
            Cal_CRC_SingleData(sendCrcValue, *data++);
    }
    frame->crch = GET_U16_HIGHBYTE(sendCrcValue);
    frame->crcl = GET_U16_LOWBYTE(sendCrcValue);
    return;
}

void Edit_ReadParamFrame(fireware_frame_st *frame, fireware_addr_e dest, uint8_t packet_no)
{
    uint8_t *data = &frame->head;
    uint16_t sendCrcValue;
    uint8_t index;
    uint8_t len;

    frame->head = FIREWARE_FRAME_HEAD;
    frame->srcAddr = FIREWARE_MAIN_ADDR;
    frame->destAddr = dest;
    frame->fb1 = FIREWARE_PARAM_FUNC;
    frame->fb2 = PARAM_SUBFUNC_READ_PARAM;
    frame->fb3 = packet_no;
    frame->len = 0;
    frame->end = FIREWARE_FRAME_END;
    sendCrcValue = U16_CRC_INITIAL_VALUE;
    len = offsetof(fireware_frame_st, data) + frame->len;
    for(index = 0; index < len; index++)
    {
        sendCrcValue =
            Cal_CRC_SingleData(sendCrcValue, *data++);
    }
    frame->crch = GET_U16_HIGHBYTE(sendCrcValue);
    frame->crcl = GET_U16_LOWBYTE(sendCrcValue);
    return;
}

void Edit_CtrlParamCrc(fireware_frame_st *frame, fireware_addr_e dest, param_subfunc_e crc_cmd)
{
    uint8_t *data = &frame->head;
    uint16_t sendCrcValue;
    uint8_t index;
    uint8_t len;

    frame->head = FIREWARE_FRAME_HEAD;
    frame->srcAddr = FIREWARE_MAIN_ADDR;
    frame->destAddr = dest;
    frame->fb1 = FIREWARE_PARAM_FUNC;
    frame->fb2 = crc_cmd;
    frame->fb3 = 0;
    frame->len = 0;
    frame->end = FIREWARE_FRAME_END;
    sendCrcValue = U16_CRC_INITIAL_VALUE;
    len = offsetof(fireware_frame_st, data) + frame->len;
    for(index = 0; index < len; index++)
    {
        sendCrcValue =
            Cal_CRC_SingleData(sendCrcValue, *data++);
    }
    frame->crch = GET_U16_HIGHBYTE(sendCrcValue);
    frame->crcl = GET_U16_LOWBYTE(sendCrcValue);
    return;
}

void get_Property_Val(fireware_property_st *prop, uint8_t *data)
{
    switch(prop->val)
    {
        case PROPERTY_VAL_STRING:
        case PROPERTY_VAL_MAC:
        case PROPERTY_VAL_DID:
        case PROPERTY_VAL_MODEL:
            strncpy(prop->data, (char *)data, get_Property_Size(prop->val) - 1);
            break;
        case PROPERTY_VAL_UINT32:
            *((uint32_t *)prop->data) = data[0] << 24 | data[1] << 16 
                                        | data[2] << 8 | data[3];
            break;
        case PROPERTY_VAL_UINT8:
            *((uint8_t *)prop->data) = *((uint8_t *)data);
            break;
        case PROPERTY_VAL_INT8:
            *((int8_t *)prop->data) = *((int8_t *)data);
            break;
        case PROPERTY_VAL_UINT16:
            *((uint16_t *)prop->data) = data[0] << 8 | data[1];
            break;
        case PROPERTY_VAL_INT16:
            *((int16_t *)prop->data) = data[0] << 8 | data[1];
            break;
    }
    return;
}

void set_Property_Val(fireware_property_st *prop, uint8_t *data)
{
    uint16_t usdata;
    uint32_t wdata;
    int16_t  sdata;

    switch(prop->val)
    {
        case PROPERTY_VAL_STRING:
        case PROPERTY_VAL_MAC:
        case PROPERTY_VAL_DID:
        case PROPERTY_VAL_MODEL:
            strncpy((char *)data, prop->data, get_Property_Size(prop->val) - 1);
            break;
        case PROPERTY_VAL_UINT32:
            wdata = *((uint32_t *)prop->data);
            data[0] = (wdata >> 24) & 0xFF;
            data[1] = (wdata >> 16) & 0xFF;
            data[2] = (wdata >> 8)  & 0xFF;
            data[3] = (wdata) & 0xFF;
            break;
        case PROPERTY_VAL_UINT8:
            *((uint8_t *)data) = *((uint8_t *)prop->data);
            break;
        case PROPERTY_VAL_INT8:
            *((int8_t *)data) = *((int8_t *)prop->data);
            break;
        case PROPERTY_VAL_UINT16:
            usdata = *((uint16_t *)prop->data);
            data[0] = (usdata >> 8) & 0xFF;
            data[1] = (usdata) & 0xFF;
            break;
        case PROPERTY_VAL_INT16:
            sdata =*((int16_t *)prop->data);
            data[0] = (sdata >> 8) & 0xFF;
            data[1] = (sdata) & 0xFF;
            break;
    }
    return;
}

bool is_String_Property(property_val_e val)
{
    switch(val)
    {
        case PROPERTY_VAL_STRING:
            return true;
        case PROPERTY_VAL_UINT32:
        case PROPERTY_VAL_UINT8:
        case PROPERTY_VAL_INT8:
        case PROPERTY_VAL_UINT16:
        case PROPERTY_VAL_INT16:
            return false;
        case PROPERTY_VAL_MAC:
        case PROPERTY_VAL_DID:
        case PROPERTY_VAL_MODEL:
            return true;
        default:
            return false;
    }
}

uint8_t get_Property_Size(property_val_e val)
{
    switch(val)
    {
        case PROPERTY_VAL_STRING:
            return FIREWARE_PROPERTY_STRING_LEN;
        case PROPERTY_VAL_UINT32:
            return 4;
        case PROPERTY_VAL_UINT8:
            return 1;
        case PROPERTY_VAL_INT8:
            return 1;
        case PROPERTY_VAL_UINT16:
        case PROPERTY_VAL_INT16:
            return 2;
        case PROPERTY_VAL_MAC:
            return FIREWARE_PROPERTY_MAC_LEN;
        case PROPERTY_VAL_DID:
            return FIREWARE_PROPERTY_DID_LEN;
        case PROPERTY_VAL_MODEL:
            return FIREWARE_PROPERTY_MODEL_LEN;
        default:
            return 0;
    }
}

void Edit_Property_QueryFrame(fireware_addr_e dest, fireware_frame_st *frame)
{
    uint8_t *data = &frame->head;
    uint16_t sendCrcValue;
    uint8_t index;
    uint8_t len;

    frame->head = FIREWARE_FRAME_HEAD;
    frame->srcAddr = FIREWARE_MAIN_ADDR;
    frame->destAddr = dest;
    frame->fb1 = FIREWARE_PROPERTY_FUNC;
    frame->fb2 = PROPERTY_SUBFUNC_QUERY;
    frame->fb3 = 0;
    frame->len = 0x00;
    frame->end = FIREWARE_FRAME_END;
    sendCrcValue = U16_CRC_INITIAL_VALUE;
    len = offsetof(fireware_frame_st, data);
    for(index = 0; index < len; index++)
    {
        sendCrcValue =
            Cal_CRC_SingleData(sendCrcValue, *data++);
    }

    frame->crch = GET_U16_HIGHBYTE(sendCrcValue);
    frame->crcl = GET_U16_LOWBYTE(sendCrcValue);
    frame->wait_response = true;
    return;
}

void Edit_Property_ResponseFrame(fireware_addr_e dest, fireware_frame_st *frame,
                                 uint8_t *rdata, uint8_t rlen)
{
    uint8_t *data = &frame->head;
    uint16_t sendCrcValue;
    uint8_t index;
    uint8_t len;

    frame->head = FIREWARE_FRAME_HEAD;
    frame->srcAddr = FIREWARE_MAIN_ADDR;
    frame->destAddr = dest;
    frame->fb1 = FIREWARE_PROPERTY_FUNC;
    frame->fb2 = PROPERTY_SUBFUNC_RESPONSE;
    frame->fb3 = 0;
    frame->len = rlen;
    memcpy(frame->data, rdata, rlen);
    frame->end = FIREWARE_FRAME_END;
    sendCrcValue = U16_CRC_INITIAL_VALUE;
    len = offsetof(fireware_frame_st, data) + rlen;
    for(index = 0; index < len; index++)
    {
        sendCrcValue =
            Cal_CRC_SingleData(sendCrcValue, *data++);
    }

    frame->crch = GET_U16_HIGHBYTE(sendCrcValue);
    frame->crcl = GET_U16_LOWBYTE(sendCrcValue);
    frame->wait_response = false;
    return;
}

void Edit_Property_Frame(fireware_addr_e dest, fireware_frame_st *frame,
                         fireware_property_st *propertys, uint8_t size)
{
    uint8_t *data = &frame->head;
    fireware_property_st *prop;
    uint8_t prop_cnt = 0;
    uint16_t sendCrcValue;
    uint8_t index;
    uint8_t dindex = 0;
    uint8_t len;

    frame->head = FIREWARE_FRAME_HEAD;
    frame->srcAddr = FIREWARE_MAIN_ADDR;
    frame->destAddr = dest;
    frame->fb1 = FIREWARE_PROPERTY_FUNC;
    frame->fb2 = PROPERTY_SUBFUNC_IOT;
    frame->fb3 = 0;
    for(index = 0; index < size; index++)
    {
        prop = &propertys[index];
        if(prop->dirty == false || ((prop->flags & PROPERTY_DIR_OUT) == 0))
        {
            continue;
        }
        len = get_Property_Size(prop->val);
        if(len <= 0)
        {
            continue;
        }

        if(is_String_Property(prop->val) && strlen(prop->data) > len - 1)
        {
            prop->dirty = false;
            continue;
        }
        else if(is_String_Property(prop->val))
        {
            len = strlen(prop->data) + 1;
        }

        if(dindex + len + 2 > FIREWARE_FRAME_DATA_MAX ||
           prop_cnt >= FIREWARE_PROPERTY_NUM_TRANSFER)
        {
            break;
        }

        frame->data[dindex++] = prop->sid;
        frame->data[dindex++] = prop->pid;
        if(is_String_Property(prop->val))
        {
            memset(&frame->data[dindex], 0, len);
        }
        set_Property_Val(prop, &frame->data[dindex]);
        dindex += len;
        prop_cnt++;
    }
    frame->len = dindex;
    frame->end = FIREWARE_FRAME_END;
    sendCrcValue = U16_CRC_INITIAL_VALUE;
    len = offsetof(fireware_frame_st, data) + dindex;
    for(index = 0; index < len; index++)
    {
        sendCrcValue =
            Cal_CRC_SingleData(sendCrcValue, *data++);
    }

    frame->crch = GET_U16_HIGHBYTE(sendCrcValue);
    frame->crcl = GET_U16_LOWBYTE(sendCrcValue);
    frame->wait_response = true;
    return;
}

fireware_property_st *get_Property_BySidPid(uint8_t sid, uint8_t pid,
                                            fireware_property_st *propertys, uint8_t size)

{
    uint8_t index;
    fireware_property_st *prop;

    for(index = 0; index < size; index++)
    {
        prop = &propertys[index];
        if(prop->sid == sid && prop->pid == pid)
        {
            return prop;
        }
    }
    return NULL;
}

uint8_t *Parse_PropertyData(uint8_t *data, uint16_t len, uint16_t *rlen,
                            fireware_property_st *propertys, uint8_t size)
{
    static uint8_t response[FIREWARE_FRAME_DATA_MAX];
    fireware_property_st *prop;
    uint16_t index = 0;
    uint16_t rindex = 0;
    uint8_t sid;
    uint8_t pid;
    uint8_t plen;

    while(len - index > 2)
    {
        sid = data[index++];
        pid = data[index++];
        response[rindex++] = sid;
        response[rindex++] = pid;
        prop = get_Property_BySidPid(sid, pid, propertys, size);
        if(NULL == prop)
        {
            response[rindex++] = PROPERTY_RESPONSE_UNEXSIT;
            *rlen = rindex;
            break;
        }

        plen = get_Property_Size(prop->val);
        if(is_String_Property(prop->val) && strlen((char *)&data[index]) > plen - 1)
        {
             response[rindex++] = PROPERTY_RESPONSE_PROPERTYERROR;
            *rlen = rindex;
            break;
        }
        else if(is_String_Property(prop->val))
        {
            plen = strlen((char *)&data[index]) + 1;
        }

        if(len - index < plen)
        {
            response[rindex++] = PROPERTY_RESPONSE_PROPERTYERROR;
            *rlen = rindex;
            break;
        }

        if(prop->flags & PROPERTY_DIR_IN)
        {
            if(is_String_Property(prop->val))
            {
                memset(prop->data, 0, plen);
            }
            get_Property_Val(prop, &data[index]);
            if(NULL != prop->dev_to_iot)
            {
                prop->dev_to_iot(prop->data);
                prop->dirty = false;
                prop->dirty1 = false;
            }
            response[rindex++] = PROPERTY_RESPONSE_SUCCESS;
        }
        else
        {
            response[rindex++] = PROPERTY_RESPONSE_UNWRITEABLE;
        }
        index += plen;
        *rlen = rindex;
    }
    
    return response;
}

void Parse_PropertyResponse(uint8_t *data, uint16_t len,
                            fireware_property_st *propertys, uint8_t size)
{
    fireware_property_st *prop;
    property_reponse_e result;
    uint16_t index = 0;
    uint8_t sid;
    uint8_t pid;

    while(len - index > 2)
    {
        sid = data[index++];
        pid = data[index++];
        prop = get_Property_BySidPid(sid, pid, propertys, size);
        if(prop)
        {
            result = data[index];
            if(result == PROPERTY_RESPONSE_SUCCESS ||
               result == PROPERTY_RESPONSE_UNEXSIT ||
               result == PROPERTY_RESPONSE_UNREADABLE ||
               result == PROPERTY_RESPONSE_UNWRITEABLE ||
               result == PROPERTY_RESPONSE_PROPERTYERROR
               )
            {
                if(NULL != prop->iot_to_dev)
                {
                    prop->iot_to_dev(result);
                }
                prop->dirty = false;
                prop->errcount = 0;
            }
            else
            {
                prop->errcount++;
                if(prop->errcount > FIREWARE_PROPERTY_ERROR_MAX)
                {
                    prop->dirty = false;
                    prop->errcount = 0;
                }
            }
        }
        index++;
    }
}
