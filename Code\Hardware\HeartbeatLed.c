/*!
 * @file
 * @brief This module is heartbeat led.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "HeartbeatLed.h"

static bool b_LedState = false;
static uint8_t u8_BlinkLedState;
static uint8_t u8_BlinkCount;

static void Set_LedBlinkState(void)
{
    if(true == b_LedState)
    {
        b_LedState = false;
        IO_HEARTBEAT_LED_ENABLE;
    }
    else
    {
        b_LedState = true;
        IO_HEARTBEAT_LED_DISABLE;
    }
}

void Update_HeartbeatLed(void)
{
    u8_BlinkCount++;
    switch(u8_BlinkLedState)
    {
        case(uint8_t)eBlinking_Normal:
            if(u8_BlinkCount % 2)
            {
                Set_LedBlinkState();
            }
            break;
        case(uint8_t)eBlinking_ResetWdt:
            Set_LedBlinkState();
            break;
        case(uint8_t)eBlinking_SpecialMode:
            if(u8_BlinkCount % 3)
            {
                Set_LedBlinkState();
            }
            break;
        default:
            break;
    }
}

void Set_HeartbeatState(uint8_t blink_state)
{
    u8_BlinkLedState = blink_state;
}
