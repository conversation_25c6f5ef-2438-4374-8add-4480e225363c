set UV=C:\Keil_v5\UV4\UV4.exe
set BOOTPATH= ..\Bootloader\MDK-ARM\HC32L18x.uvprojx
set BOOTBIN=..\Bootloader\MDK-ARM\output\release\HC32L18x.bin
set BOOTLOG= out\bootbuild.log
set BOOTVERSIONFILE=..\Bootloader\Debug\syslog.h
set APPPATH=..\Code\MDK-ARM\HC32L18x.uvprojx
set APPBIN=..\Code\MDK-ARM\output\release\HC32L18x.bin
set APPLOG= out\appbuild.log
set MODELFILE=..\Code\Source\Iot\user\user_config.h
set DEBUGSTARTUP=..\Code\Startup\startup_hc32l186.s
set DEBUGMODEFILE=..\Code\Source\Application\SystemManager.h

set FUNCSTACKPROTECT=0
set MAINAPPDEBUGMODE=0

choice /C 1234 /M "[1]bs52s  [2]bs52s1 [3]bf52s  [4]bf52s1"
set MACHINE=%ERRORLEVEL%
if %MACHINE% == 1 (
set MODEL=bs52s
set PID=25357
set OTA_VERSION=1
set APP_VERSION=0001
set DISPLAY_APP_VERSION=0001
)ELSE if %MACHINE% == 2 (
set MODEL=bs52s1
set PID=29726
set OTA_VERSION=1
set APP_VERSION=0001
set DISPLAY_APP_VERSION=0001
)ELSE if %MACHINE% == 3 (
set MODEL=bf52s
set PID=25348
set OTA_VERSION=7
set APP_VERSION=0007
set DISPLAY_APP_VERSION=0005
) ELSE if %MACHINE% == 4 (
set MODEL=bf52s1
set PID=29450
set OTA_VERSION=1
set APP_VERSION=0001
set DISPLAY_APP_VERSION=0001
)ElSE (
pause > nul
)
echo %MODEL%  %PID%

choice /C 12 /M "[1]VTF1116Y  [2]VNZ1116Y"
set COMPTYPE=%ERRORLEVEL%
if %COMPTYPE% == 1 (
set INVERTER_COMPRESSOR=VTF1116Y
set INVERTER_APP_VERSION=0002
) ELSE if %COMPTYPE% == 2 (
set INVERTER_COMPRESSOR=VNZ1116Y
set INVERTER_APP_VERSION=0001
) ElSE (
pause > nul
)
echo %INVERTER_COMPRESSOR%

set BOOT_VERSION=0001
set BOOT_MAX_SIZE=0x5C00
set APP_START_ADDR=0x6000
set APP_MAX_SIZE=0x30E00
set APP_HEADER_OFFSET=0x5FF0
set APP_FLASH_SIZE=0x40000

set INVERTER_BOOT_VERSION=0002
set INVERTER_HWVERSION=0001
set INVERTER_BOOT_MAX_SIZE=0x3000
set INVERTER__HEADER_OFFSET=0x3000
set INVERTER_MAX_SIZE=0x6000

set NFC_BOOT_VERSION=0002
set NFC_HWVERSION=0001
set NFC_APP_VERSION=0001
set NFC_BOOT_MAX_SIZE=0x3000
set NFC_HEADER_OFFSET=0x3200
set NFC_MAX_SIZE=0xCA00

set ICEMAKER_BOOT_VERSION=0001
set ICEMAKER_APP_VERSION=0003
set ICEMAKER_BOOT_MAX_SIZE=0x4C00
set ICEMAKER_APP_MAX_SIZE=0x15E00
set ICEMAKER_HEADER_OFFSET=0x4FF0
set ICEMAKER_APP_FLASH_SIZE=0x20000

set DISPLAY_BOOT_VERSION=0001
set DISPLAY_HWVERSION=0001
set DISPLAY_BOOT_MAX_SIZE=0x3000
set DISPLAY_HEADER_OFFSET=0x3200
set DISPLAY_MAX_SIZE=0xCA00

del out
del main_boot.bin
del main_app.bin
del inverter_boot.bin
del inverter_app.bin
del nfc_boot.bin
del nfc_app.bin
del display_boot.bin
del diaplay_app.bin
del icemaker_boot.bin
del icemaker_app.bin
del %BOOTBIN%
del %APPBIN%
mkdir out
copy  sed.exe out\
copy  xml.exe out\
copy merge_ota.exe out\
copy  crc.exe out\
copy *.dll out\
copy  ota.ini out\

cd out

if  %MAINAPPDEBUGMODE% == 1 (
sed -i  "/#define BuildMode ReleaseMode/c \//#define BuildMode ReleaseMode"  ..\%DEBUGMODEFILE%
sed -i  "/#define BuildMode DebugMode/c \#define BuildMode DebugMode"  ..\%DEBUGMODEFILE%
sed -i  "/app start addr/c \                LDR     R2, =0 ;app start addr"  ..\%DEBUGSTARTUP%
set APP_START_ADDR=0
) ELSE (
sed -i  "/#define BuildMode ReleaseMode/c \#define BuildMode ReleaseMode"  ..\%DEBUGMODEFILE%
sed -i  "/#define BuildMode DebugMode/c \//#define BuildMode DebugMode"  ..\%DEBUGMODEFILE%
sed -i  "/app start addr/c \                LDR     R2, =%APP_START_ADDR% ;app start addr"  ..\%DEBUGSTARTUP%
)

if  %FUNCSTACKPROTECT% == 1 (
xml ed  -u  "/Project/Targets/Target/TargetOption/TargetArmAds/Cads/VariousControls/MiscControls"  -v  "-fstack-protector-strong"  ^
             -u  "/Project/Targets/Target/TargetOption/TargetArmAds/ArmAdsMisc/OnChipMemories/OCR_RVCT4/StartAddress"  -v  "%APP_START_ADDR%"  ..\%APPPATH%  > uvprojxfile
) ELSE (
xml ed  -u  "/Project/Targets/Target/TargetOption/TargetArmAds/Cads/VariousControls/MiscControls"  -v  " "  ^
             -u  "/Project/Targets/Target/TargetOption/TargetArmAds/ArmAdsMisc/OnChipMemories/OCR_RVCT4/StartAddress"  -v  "%APP_START_ADDR%" ..\%APPPATH% > uvprojxfile
)

if  %ERRORLEVEL% == 0 (
echo xml parse success
copy uvprojxfile ..\%APPPATH%
) ELSE (
echo xml parse fail
pause > nul
)



sed -i  "/BOOT_VERSION/c \#define BOOT_VERSION \"%BOOT_VERSION%\""  ..\%BOOTVERSIONFILE%
sed -i "/USER_MODEL/c \#define USER_MODEL \"midjd.fridge.%MODEL%\"" ..\%MODELFILE%
sed -i "/BLE_PID/c \#define BLE_PID \"%PID%\"" ..\%MODELFILE%
sed -i "/OTA_FACTORY_VERSION/c \#define OTA_FACTORY_VERSION \(%OTA_VERSION%)" ..\%MODELFILE%

cd ..
%UV% -j0 -b %BOOTPATH% -l %BOOTLOG%
%UV% -j0 -b %APPPATH% -l %APPLOG%
copy %BOOTBIN% .\main_boot.bin
copy %APPBIN%  .\main_app.bin
copy inverter_bin\boot\%INVERTER_BOOT_VERSION%\boot.bin  .\inverter_boot.bin
copy inverter_bin\app\%INVERTER_COMPRESSOR%\%INVERTER_APP_VERSION%\app.bin  .\inverter_app.bin
copy nfc_bin\boot\%NFC_BOOT_VERSION%\boot.bin  .\nfc_boot.bin
copy nfc_bin\app\%NFC_APP_VERSION%\app.bin  .\nfc_app.bin
copy display_bin\%MODEL%\boot\%DISPLAY_BOOT_VERSION%\boot.bin  .\display_boot.bin
copy display_bin\%MODEL%\app\%DISPLAY_APP_VERSION%\app.bin  .\display_app.bin
copy icemaker_bin\boot\%ICEMAKER_BOOT_VERSION%\IceMaker_boot.bin  .\icemaker_boot.bin
copy icemaker_bin\app\%ICEMAKER_APP_VERSION%\IceMaker.bin  .\icemaker_app.bin
ImageHeader.exe %APP_VERSION% main_app.bin  %APP_MAX_SIZE%  > out/temp
setlocal enabledelayedexpansion
for /f "usebackq tokens=*" %%a in ("out/temp") do (
set "strs=%%a"
if not "!strs!" == "!strs:CRC:=!" (
set crc=!strs:CRC:=!
set APP_CRC=0x!crc!
echo !crc! 
echo !APP_CRC!
)
)

ImageHeader.exe %ICEMAKER_APP_VERSION% icemaker_app.bin  %ICEMAKER_APP_MAX_SIZE%  > out/temp
setlocal enabledelayedexpansion
for /f "usebackq tokens=*" %%a in ("out/temp") do (
set "strs=%%a"
if not "!strs!" == "!strs:CRC:=!" (
set crc=!strs:CRC:=!
set ICEMAKER_CRC=0x!crc!
echo !crc! 
echo !ICEMAKER_CRC!
)
)

ImageHeader_inverter.exe %INVERTER_HWVERSION% %INVERTER_APP_VERSION% inverter_app.bin  %INVERTER_MAX_SIZE%  > out/temp
setlocal enabledelayedexpansion
for /f "usebackq tokens=*" %%a in ("out/temp") do (
set "strs=%%a"
if not "!strs!" == "!strs:CRC:=!" (
set crc=!strs:CRC:=!
set INVERTER_CRC=0x!crc!
echo !crc! 
echo !INVERTER_CRC!
)
)

ImageHeader_nfc.exe %NFC_HWVERSION% %NFC_APP_VERSION% nfc_app.bin  %NFC_MAX_SIZE%  > out/temp
setlocal enabledelayedexpansion
for /f "usebackq tokens=*" %%a in ("out/temp") do (
set "strs=%%a"
if not "!strs!" == "!strs:CRC:=!" (
set crc=!strs:CRC:=!
set NFC_CRC=0x!crc!
echo !crc! 
echo !NFC_CRC!
)
)

ImageHeader_nfc.exe %DISPLAY_HWVERSION% %DISPLAY_APP_VERSION% display_app.bin  %DISPLAY_MAX_SIZE%  > out/temp
setlocal enabledelayedexpansion
for /f "usebackq tokens=*" %%a in ("out/temp") do (
set "strs=%%a"
if not "!strs!" == "!strs:CRC:=!" (
set crc=!strs:CRC:=!
set DISPLAY_CRC=0x!crc!
echo !crc! 
echo ! DISPLAY_CRC!
)
)

move main_app_header.bin  out\
move inverter_app_header.bin  out\
move nfc_app_header.bin  out\
move icemaker_app_header.bin  out\
move display_app_header.bin  out\
srec_cat -output out\%MODEL%_main_flash_factory -binary main_boot.bin -binary -fill 0xff 0x0 %APP_HEADER_OFFSET% out\main_app_header.bin -binary -offset %APP_HEADER_OFFSET% -fill 0xff %APP_HEADER_OFFSET% %APP_FLASH_SIZE% 
srec_cat -output out\%MODEL%_main_flash -binary main_boot.bin -binary -fill 0xff 0x0 %APP_HEADER_OFFSET% out\main_app_header.bin -binary -offset %APP_HEADER_OFFSET%
srec_cat -output out\%MODEL%_%INVERTER_COMPRESSOR%_inverter_flash -binary inverter_boot.bin -binary -fill 0xff 0x0 %INVERTER__HEADER_OFFSET% out\inverter_app_header.bin -binary -offset %INVERTER__HEADER_OFFSET% 
srec_cat -output out\%MODEL%_nfc_flash -binary nfc_boot.bin -binary -fill 0x0 0x0 %NFC_HEADER_OFFSET% out\nfc_app_header.bin -binary -offset %NFC_HEADER_OFFSET% 
srec_cat -output out\%MODEL%_display_flash -binary display_boot.bin -binary -fill 0x0 0x0 %DISPLAY_HEADER_OFFSET% out\display_app_header.bin -binary -offset %DISPLAY_HEADER_OFFSET%
srec_cat -output out\%MODEL%_icemaker_flash -binary icemaker_boot.bin -binary -fill 0xff 0x0 %ICEMAKER_HEADER_OFFSET% out\icemaker_app_header.bin -binary -offset %ICEMAKER_HEADER_OFFSET% 
ImageCrc16.exe out\%MODEL%_main_flash_factory %BOOT_VERSION% %BOOT_MAX_SIZE% %APP_VERSION%  %APP_HEADER_OFFSET%
ImageCrc16.exe out\%MODEL%_main_flash %BOOT_VERSION% %BOOT_MAX_SIZE% %APP_VERSION%  %APP_HEADER_OFFSET%
ImageCrc16.exe out\%MODEL%_icemaker_flash %ICEMAKER_BOOT_VERSION% %ICEMAKER_BOOT_MAX_SIZE% %ICEMAKER_APP_VERSION% %ICEMAKER_HEADER_OFFSET%
ImageCrc16_inverter.exe out\%MODEL%_%INVERTER_COMPRESSOR%_inverter_flash %INVERTER_BOOT_VERSION% %INVERTER_BOOT_MAX_SIZE% %INVERTER_APP_VERSION% %INVERTER__HEADER_OFFSET% 
ImageCrc16_nfc.exe out\%MODEL%_nfc_flash %NFC_BOOT_VERSION% %NFC_BOOT_MAX_SIZE% %NFC_APP_VERSION% %NFC_HEADER_OFFSET%
ImageCrc16_nfc.exe out\%MODEL%_display_flash %DISPLAY_BOOT_VERSION% %DISPLAY_BOOT_MAX_SIZE% %DISPLAY_APP_VERSION% %DISPLAY_HEADER_OFFSET%

copy out\main_app_header.bin out\main_ota.bin 
copy out\inverter_app_header.bin out\inverter_ota.bin
copy out\nfc_app_header.bin out\nfc_ota.bin
copy out\display_app_header.bin out\display_ota.bin
copy out\icemaker_app_header.bin out\icemaker_ota.bin

cd out 
sed -i "/hw_id/c \hw_id=%MODEL%" ota.ini
sed -i "/total_version/c \total_version=%OTA_VERSION%"  ota.ini
sed -i "/main_version/c \main_version=%APP_VERSION%"  ota.ini
sed -i "/main_crc/c \main_crc=!APP_CRC!" ota.ini
sed -i "/inverter_version/c \inverter_version=%INVERTER_APP_VERSION%" ota.ini
sed -i "/inverter_crc/c \inverter_crc=!INVERTER_CRC!" ota.ini
sed -i "/nfc_version/c \nfc_version=%NFC_APP_VERSION%" ota.ini
sed -i "/nfc_crc/c \nfc_crc=!NFC_CRC!" ota.ini
sed -i "/display_version/c \display_version=%DISPLAY_APP_VERSION%" ota.ini
sed -i "/display_crc/c \display_crc=!DISPLAY_CRC!" ota.ini
sed -i "/icemaker_version/c \icemaker_version=%ICEMAKER_APP_VERSION%" ota.ini
sed -i "/icemaker_crc/c \icemaker_crc=!ICEMAKER_CRC!" ota.ini
merge_ota.exe 
crc.exe
move fw_crc.bin %MODEL%_ota_%OTA_VERSION%.bin
del  *.exe
del  *.dll
del  sed*
endlocal
pause > nul
