/*!
 * @file
 * @brief Manages all the state variables of the showroom mode.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "ShowroomMode.h"
#include "ResolverDevice.h"
#include "Driver_SingleDamper.h"
#include "Driver_CompFrequency.h"
#include "SystemManager.h"
#include "Driver_Fan.h"
#include "Drive_Valve.h"

void ShowroomMode_Init(void)
{
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, 0);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, 0);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 0);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, 0);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DAMPER_AllClose);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DAMPER_AllClose);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_IonGenerator, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamperHeater, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzIonGenerator, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDefHeater, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, Valve_AllOpen);
}

void ShowroomMode_Exit(void)
{
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_IonGenerator, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamperHeater, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzIonGenerator, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDefHeater, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, DS_DontCare);
}
