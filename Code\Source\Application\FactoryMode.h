/*!
 * @file
 * @brief This file defines public constants, types and functions for the factory mode.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef FACTORY_MODE_H
#define FACTORY_MODE_H

#include <stdint.h>
#include <stdbool.h>

#define FACTORY_DATA_BUFFER 480

typedef enum
{
    FACTORY_ITEM_VAL_UINT32 = 0x0,
    FACTORY_ITEM_VAL_INT32 = 0x1,
    FACTORY_ITEM_VAL_UINT8 = 0x2,
    FACTORY_ITEM_VAL_INT8 = 0x3,
    FACTORY_ITEM_VAL_UINT16 = 0x4,
    FACTORY_ITEM_VAL_INT16 = 0x5
} factory_item_val_e;

typedef struct{
    char *key;
    factory_item_val_e type;
    void (*check_factory_item)(void *data);
}factory_item_st;

void FactoryMode_Init(void);
void FactoryMode_Exit(void);
void Get_FctUploadData(char *result, uint16_t len);
extern void str_n_cat(char *pDst, int n_str, ...);
uint16_t Get_FactoryRoomTemp(void);
bool Get_WifiFactoryModeResult(void);
bool Get_WifiFactoryConnected(void);
#endif
