/*!
 * @file
 * @brief This file defines public constants, types and functions for the Core Timer Library.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __CORE_TIMERLIBRARY_H__
#define __CORE_TIMERLIBRARY_H__

#include <stdint.h>
#include <stdbool.h>

#define CORE_TIMERLIBRARY_MAX_DURATION_SECONDS ((uint16_t)(60000))

#define CORE_TIMERLIBRARY_MAX_DURATION_MILLISECONDS ((uint16_t)(1000))

/*!
 * @brief This constant defines the value that represents a stopped timer.
 */
#define CORE_TIMERLIB_TIMER_STOPPED ((uint16_t)(0xFFFF))

/*!
 * @brief This define a time unit that is the basic unit of time measurement used by the timer library
 * @param uint16_t u16_Ticks : Number of ticks (1 tick generally represents 1 PWM cycle);Divide by
 * TicksPerMillisecond to get number of milliseconds
 * @param uint16_t u16_Seconds : The number of seconds
 */
typedef struct
{
    uint16_t u16_Ticks;

    uint16_t u16_Seconds;
} ST_Core_TimerLib_TimeUnit;

/*!
 * @brief This type defines a timer object.
 * @param uint16_t u16_StartTime : Holds system tick count at time timer was started
 * @param ST_Core_TimerLib_TimeUnit st_Duration : Duration of the timer
 */
typedef struct
{
    uint16_t u16_StartTime;

    ST_Core_TimerLib_TimeUnit st_Duration;
} st_CoreTimerLibTimer;

/*!
 * @brief This function initializes the specified timer to the stopped state.
 * @param st_CoreTimerLibTimer *const pst_Timer : pointer to a timer object
 */
extern void Core_TimerLib_TimerInit(st_CoreTimerLibTimer *const pst_Timer);

/*!
 * @brief This functions starts the timer that is passed to it with a duration that
 * is specified in seconds and milliseconds.  The current system tick is stored in
 * the u16StartTime property of the timer and is used each time the timer is checked
 * for expiration to determine how much time has elapsed (in ticks).
 * @param st_CoreTimerLibTimer *const pst_Timer : pointer to a timer object
 * @param const uint16_t u16_DurationSeconds : duration of the timer in seconds
 * @param const uint16_t u16_DurationMilliSeconds : duration of the timer in ms
 */
extern void Core_TimerLib_TimerStart(st_CoreTimerLibTimer *const pst_Timer,
    const uint16_t u16_DurationSeconds,
    const uint16_t u16_DurationMilliSeconds);

/*!
 * @brief This functions starts the timer that is passed to it with a duration
 * of MAX_DURATION_SECONDS.
 * @param st_CoreTimerLibTimer *const pst_Timer : pointer to a timer object
 */
extern void Core_TimerLib_TimerStartMeasurement(
    st_CoreTimerLibTimer *const pst_Timer);

/*!
 * @brief This library function stops the specified timer by writing the stopped
 * value to the duration seconds and ticks.
 * @param st_CoreTimerLibTimer *const pst_Timer : pointer to a timer object
 */
extern void Core_TimerLib_TimerStop(st_CoreTimerLibTimer *const pst_Timer);

/*!
 * @brief This library function checks the specified timer to see if it is currently running.
 * A timer is considered stopped if both of its duration values are set to the stopped value.
 * @param st_CoreTimerLibTimer *const pst_Timer : pointer to a timer object
 */
extern bool Core_TimerLib_IsTimerRunning(
    const st_CoreTimerLibTimer *const pst_Timer);

/*!
 * @brief This library function checks the specified timer object to see if it is currently stopped.
 * A timer is considered stopped if both of its duration values are set to the stopped value.
 * @param st_CoreTimerLibTimer *const pst_Timer : pointer to a timer object
 */
extern bool Core_TimerLib_IsTimerStopped(
    const st_CoreTimerLibTimer *const pst_Timer);

/*!
 * @brief This library function does all of the "work" for the timers. If the specified timer is running,
 * it uses the stored start time (system tick value at the time the timer was started) and the current
 * system tick value to calculate how much time has elapsed. If the elapsed time is >= to the duration,
 * then the timer is expired.
 * @param st_CoreTimerLibTimer *const pst_Timer : pointer to a timer object
 * @note Timers must be check frequently enough to prevent overflow of the 16-bit system tick count variable.
 * To get the longest amount of time between checks for expiration, multiply the Core tick update time by 65,535.
 * For example, if the core timebase is updated every 62.5us, then the tick counter will overflow every
 * 62.5us * 65,535 = 4.096s. If the tick is 500us, the tick counter will overflow at 32.76 seconds.
 * If the timer is not checked faster than every 4.096 seconds (or 32.76 seconds), the tick count will wrap,
 * and the timer will never expire.
 */
extern bool Core_TimerLib_IsTimerExpired(
    st_CoreTimerLibTimer *const pst_Timer);

/*!
 * @brief This function determines how much time has elapsed since the specified timer was started.
 * The result is stored in the destination TimeUnit structure -- specifying elapsed time as a
 * combination of ticks and seconds. The expected use is for the user to start a timer with
 * the MeasureTimerStart function.  This function should then be called regularly to update the
 * duration, as this is how the time is measured.
 * @param st_CoreTimerLibTimer *const pst_Timer : pointer to a timer object
 * @param ST_Core_TimerLib_TimeUnit *const pst_DestinationTimeUnit : a pointer to a time unit where
 * elapsed time will be copied.
 * @note Timers must be check frequently enough to prevent overflow of the 16-bit system tick count variable.
 * To get the longest amount of time between checks for expiration, multiply the Core tick update time by 65,535.
 * For example, if the core timebase is updated every 62.5us, then the tick counter will overflow every
 * 62.5us * 65,535 = 4.096s. If the tick is 500us, the tick counter will overflow at 32.76 seconds.
 * If the timer is not checked faster than every 4.096 seconds (or 32.76 seconds), the tick count will wrap,
 * and the timer will never expire.
 */
extern void Core_TimerLib_GetElapsedTime(
    st_CoreTimerLibTimer *const pst_Timer,
    ST_Core_TimerLib_TimeUnit *const pst_DestinationTimeUnit);

/*!
 * @brief This function determines how much time is left on the given timer object.
 * @param st_CoreTimerLibTimer *const pst_Timer : pointer to a timer object
 * @retval number of seconds remaining on timer object.
 */
extern uint16_t Core_TimerLib_GetRemainingTime_Seconds(
    st_CoreTimerLibTimer *const pst_Timer);

#endif
