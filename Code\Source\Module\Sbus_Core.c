/*!
 * @file
 * @brief Manages all the state variables of the cooling controller.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "Sbus_Core.h"
#include "syslog.h"
#include "Crc16_CCITT_FALSE.h"

sbus_master_st *sbus_masters[SBUS_TYPE_MAX];

static void Start_Rece(sbus_master_st *master)
{
    master->u8_ReceDataState = 0;
    master->u8_ReceCount = 0;
    master->u16_ReceCrcValue = U16_CRC_INITIAL_VALUE;
    master->f_LoopBackFrame = FALSE;
    master->f_BusReceIE = TRUE;
}

static void Clear_SendError(sbus_master_st *master)
{
    master->f_SendError = FALSE;
    master->u8_SendErrorCount = 0;
}

static void Add_ReceError(sbus_master_st *master)
{
    uint16_t u16_maskcode = 0x01;
    uint8_t u8_index = 0;

    u8_index = master->cur_slave;
    master->slaves[u8_index]->stats.recvPacketErrorStatistics++;
    if (master->ary_ReceErrorCount[u8_index] < U8_MAX_RECE_ERROR_TIMES)
    {
        master->ary_ReceErrorCount[u8_index]++;
    }
    else
    {
        u16_maskcode = u16_maskcode << u8_index;
        if (0x00 == (master->u16_ReceErrorFlag & u16_maskcode))
        {
            master->u16_ReceErrorFlag |= u16_maskcode;
        }
    }
}

static void Add_SendError(sbus_master_st *master)
{
    master->slaves[master->cur_slave]->stats.sendPacketErrorStatistics++;
    master->slaves[master->cur_slave]->stats.sendPacketStatistics--;
    if (master->u8_SendErrorCount < U8_MAX_SEND_ERROR_TIMES)
    {
        master->u8_SendErrorCount++;
    }
    else
    {
        master->f_SendError = TRUE;
    }
}

static void Clear_ReceError(sbus_master_st *master)
{
    uint16_t u16_maskcode = 0x01;
    uint8_t u8_index;

    u8_index = master->cur_slave;
    if (u8_index <  master->slave_num)
    {
        if (master->ary_ReceErrorCount[u8_index] > 0)
        {
            master->ary_ReceErrorCount[u8_index] = 0;
            u16_maskcode = u16_maskcode << u8_index;

            if (u16_maskcode == (master->u16_ReceErrorFlag & u16_maskcode))
            {
                u16_maskcode = ~u16_maskcode;
                master->u16_ReceErrorFlag &= u16_maskcode;
            }
        }
    }
}

static void Start_SendNextFrame(sbus_master_st *master)
{
    master->u8_ReSendCount = 0;
    master->u8_ReceDataState = 0;
    master->u8_ReceCount = 0;
    master->u16_ReceCrcValue = U16_CRC_INITIAL_VALUE;
    master->f_LoopBackFrame = FALSE;
    master->f_BusReceIE = FALSE;
    master->f_BusSendIE = TRUE;
    master->u8_OverTimeCount = U8_MAX_NEXT_FRAME_OVERTIME;
    master->f_SendCompleted = FALSE;
    master->f_SendTimeout = FALSE;
}

static void Send_OneData(sbus_master_st *master)
{
    switch(master->u8_SendDataState)
    {
        case(uint8_t)FIREWARE_FRAME_STATE_HEAD:
            master->u8_SendCount = 0;
            master->sendPos = &master->sendframe.head;
            master->u8_SendDataState = (uint8_t)FIREWARE_FRAME_STATE_SRC;
            break;
        case(uint8_t)FIREWARE_FRAME_STATE_SRC:
        case(uint8_t)FIREWARE_FRAME_STATE_DEST:
        case(uint8_t)FIREWARE_FRAME_STATE_FUNCBYTE1:
        case(uint8_t)FIREWARE_FRAME_STATE_FUNCBYTE2:
        case(uint8_t)FIREWARE_FRAME_STATE_FUNCBYTE3:
            master->u8_SendDataState++;
            break;
        case(uint8_t)FIREWARE_FRAME_STATE_LENGTH:
            master->u8_SendLength = *master->sendPos;
            master->u8_SendDataState = (uint8_t)FIREWARE_FRAME_STATE_DATA;
            break;
        case(uint8_t)FIREWARE_FRAME_STATE_DATA:
            master->u8_SendCount++;
            if(master->u8_SendCount == master->u8_SendLength + 1)
            {
                master->sendPos = &master->sendframe.crch;
            }

            if(master->u8_SendCount >= (master->u8_SendLength + 3))
            {
                master->u8_SendDataState = (uint8_t)FIREWARE_FRAME_STATE_OVER;
            }
            break;
        default:
            return;
    }

    master->u8_SendingData = *master->sendPos++;
    master->ops.sendOneData(master->u8_SendingData);
    master->u8_OverTimeCount = U8_MAX_DATA_OVERTIME;
}

static void Start_Send(sbus_master_st *master)
{
    if (0 == master->u8_ReSendCount)
    {
        master->f_SendCompleted = TRUE;
        master->f_SendTimeout = TRUE;
        master->f_BusReceIE = FALSE;
        master->f_LoopBackFrame = FALSE;
    }
    else
    {
        master->u8_ReSendCount--;
        master->f_SendCompleted = FALSE;
        master->f_SendTimeout = FALSE;
        master->f_LoopBackFrame = TRUE;
        master->u8_SendCount = 0;
        master->u8_SendDataState = 0;
        Send_OneData(master);
        master->slaves[master->cur_slave]->stats.sendPacketStatistics++;
    }
}

static void Start_ReSend(sbus_master_st *master)
{
    if (0 == master->u8_ReSendCount)
    {
        master->f_SendCompleted = TRUE;
        master->f_SendTimeout = TRUE;
        master->f_BusReceIE = FALSE;
        master->f_LoopBackFrame = FALSE;
    }
    else
    {
        master->u8_OverTimeCount = U8_MAX_DATA_OVERTIME;
        Start_Rece(master);
    }
}

static void Handle_NonLoopedFrame(sbus_master_st *master, uint8_t u8_rece_data)
{
    uint16_t crc16_value = 0;

    if (TRUE == master->f_BusReceIE)
    {
        master->u8_OverTimeCount = U8_MAX_DATA_OVERTIME;
        switch(master->u8_ReceDataState)
        {
            case(uint8_t)FIREWARE_FRAME_STATE_HEAD:
                if(FIREWARE_FRAME_HEAD == u8_rece_data)
                {
                    master->recvPos = &master->recvframe.head;
                    *master->recvPos++ = u8_rece_data;
                    master->u16_ReceCrcValue =
                        Cal_CRC_SingleData(master->u16_ReceCrcValue, u8_rece_data);
                    master->u8_ReceCount = 0;
                    master->u8_ReceDataState = (uint8_t)FIREWARE_FRAME_STATE_SRC;
                }
                break;
            case(uint8_t)FIREWARE_FRAME_STATE_SRC:
            case(uint8_t)FIREWARE_FRAME_STATE_DEST:
            case(uint8_t)FIREWARE_FRAME_STATE_FUNCBYTE1:
            case(uint8_t)FIREWARE_FRAME_STATE_FUNCBYTE2:
            case(uint8_t)FIREWARE_FRAME_STATE_FUNCBYTE3:
                *master->recvPos++ = u8_rece_data;
                master->u16_ReceCrcValue =
                    Cal_CRC_SingleData(master->u16_ReceCrcValue, u8_rece_data);
                master->u8_ReceDataState++;
                break;
            case(uint8_t)FIREWARE_FRAME_STATE_LENGTH:
                *master->recvPos++ = u8_rece_data;
                master->u16_ReceCrcValue =
                    Cal_CRC_SingleData(master->u16_ReceCrcValue, u8_rece_data);
                master->u8_ReceLength = u8_rece_data;
                master->u8_ReceDataState = (uint8_t)FIREWARE_FRAME_STATE_DATA;
                break;
            case(uint8_t)FIREWARE_FRAME_STATE_DATA:
                if(master->u8_ReceLength > FIREWARE_FRAME_DATA_MAX)
                {
                    return;
                }
                if(master->u8_ReceCount < master->u8_ReceLength)
                {
                    master->u16_ReceCrcValue =
                        Cal_CRC_SingleData(master->u16_ReceCrcValue, u8_rece_data);
                }
                if(master->u8_ReceCount == master->u8_ReceLength)
                {
                    master->recvPos = &master->recvframe.crch;
                }
                master->u8_ReceCount++;
                *master->recvPos++ = u8_rece_data;
                if(master->u8_ReceCount == (master->u8_ReceLength + 3))
                {
                    crc16_value = master->recvframe.crch << 8 | master->recvframe.crcl;

                    if(crc16_value == master->u16_ReceCrcValue &&
                        master->recvframe.end == FIREWARE_FRAME_END)
                    {
                        master->u8_ReceDataState = (uint8_t)FIREWARE_FRAME_STATE_OVER;
                        master->f_SendCompleted = TRUE;
                        master->f_SendTimeout = FALSE;
                        master->f_BusReceIE = FALSE;
                        Clear_ReceError(master);
                    }
                    else
                    {
                        master->slaves[master->cur_slave]->stats.recvPacketCrcErrorStatistics++;
                        Start_ReSend(master);
                    }
                }
                break;
            default:
                break;
        }
    }
}

static void Handle_LoopBackFrame(sbus_master_st *master, uint8_t u8_recedata)
{
    if (u8_recedata != master->u8_SendingData)
    {
        Add_SendError(master);
        Start_ReSend(master);
    }
    else
    {
        if ( master->u8_SendDataState != FIREWARE_FRAME_STATE_OVER)
        {
            Send_OneData(master);
        }
        else
        {
            Clear_SendError(master);
            if(master->sendframe.wait_response == false)
            {
                master->f_SendCompleted = TRUE;
                master->f_SendTimeout = TRUE;
                master->f_BusReceIE = FALSE;
                master->f_LoopBackFrame = FALSE;
            }
            else
            {
                Start_Rece(master);
                master->u8_OverTimeCount = master->u8_WaitResponseOverTime;
            }
        }
    }
}

static void Sbus_Arbitrate(sbus_master_st *master)
{
    uint8_t pri;
    uint8_t index;
    sbus_slave_st *slave;
    fireware_frame_st *frame;

    if (TRUE == master->f_BusSendIE && master->u8_OverTimeCount == 0)
    {
        for(index = 0; index < master->slave_num; index++)
        {
            slave = master->slaves[index];
            if(slave != NULL &&
               (slave->pri_mask & (1 << SBUS_PKT_PRI_LEVEL7)))
            {
                frame = slave->ops.sendFrame(SBUS_PKT_PRI_LEVEL7);
                slave->pri_mask &= ~(1 << SBUS_PKT_PRI_LEVEL7);
                if(frame != NULL)
                {
                    memcpy(&master->sendframe, frame, sizeof(fireware_frame_st));
                    master->u8_ReSendCount = U8_MAX_RESEND_TIMES;
                    master->u8_WaitResponseOverTime = U8_MAX_WAIT_RESPONSE_OVERTIME;
                    master->cur_slave = index;
                    master->f_BusSendIE = FALSE;
                    Start_Send(master);
                    return;
                }
            }
        }

        if(master->poll_slave >= master->slave_num)
        {
            master->poll_slave = 0;
        }

        index = master->poll_slave;
        do
        {
            slave = master->slaves[master->poll_slave];
            if(slave != NULL &&
               (slave->pri_mask & (~(1 << SBUS_PKT_PRI_LEVEL7))))
            {
                if(slave->poll_pri > SBUS_PKT_PRI_LEVEL6)
                {
                    slave->poll_pri = SBUS_PKT_PRI_LEVEL6;
                }

                pri = slave->poll_pri;
                do
                {
                    if(slave->pri_mask & (1 <<  slave->poll_pri))
                    {
                        frame = slave->ops.sendFrame(slave->poll_pri);
                        slave->pri_mask &= ~(1 << slave->poll_pri);
                        if(frame != NULL)
                        {
                            memcpy(&master->sendframe, frame, sizeof(fireware_frame_st));
                            master->u8_ReSendCount = U8_MAX_RESEND_TIMES;
                            if(Is_Sbus_Slave_Err(slave) == true)
                            {
                                master->u8_ReSendCount = U8_ERROR_RESEND_TIMES;
                            }
                            master->u8_WaitResponseOverTime = U8_MAX_WAIT_RESPONSE_OVERTIME;
                            master->cur_slave = master->poll_slave;
                            master->f_BusSendIE = FALSE;
                            slave->poll_pri--;
                            master->poll_slave++;
                            Start_Send(master);
                            return;
                        }
                    }

                    if(--slave->poll_pri > SBUS_PKT_PRI_LEVEL6)
                    {
                        slave->poll_pri = SBUS_PKT_PRI_LEVEL6;
                    }
                }while(pri != slave->poll_pri);
            }
            master->poll_slave = (++master->poll_slave) % master->slave_num;
        }while(index != master->poll_slave);
    }
    else if (TRUE == master->f_SendCompleted)
    {
        index = master->cur_slave;
        slave = master->slaves[index];
        if (TRUE == master->f_SendTimeout)
        {
            slave->ops.recvFrame(NULL);
        }
        else
        {
            slave->stats.recvPacketStatistics++;
            slave->ops.recvFrame(&master->recvframe);
        }
        Start_SendNextFrame(master);
    }
}

static void SBus_OverTime(sbus_master_st *master)
{
    uint8_t index;
    sbus_slave_st *slave;

    if (master->u8_OverTimeCount > 0)
    {
        master->u8_OverTimeCount--;

        if (0 == master->u8_OverTimeCount && master->f_BusSendIE == FALSE)
        {
            if (TRUE != master->f_SendCompleted)
            {
                if (TRUE == master->f_LoopBackFrame)
                {
                    Add_SendError(master);
                }
                else
                {
                    Add_ReceError(master);
                }
                Start_Send(master);
            }
        }
    }
}

void Handle_SBusOverTime(void)
{
    sbus_type_e index;

    for(index = 0; index < SBUS_TYPE_MAX; index++)
    {
        if(sbus_masters[index] != NULL)
        {
            SBus_OverTime(sbus_masters[index]);
        }
    }
}

void Sbus_ReceData(sbus_master_st *master, uint8_t u8_recedata)
{
    if (TRUE == master->f_LoopBackFrame)
    {
        Handle_LoopBackFrame(master, u8_recedata);
    }
    else
    {
        Handle_NonLoopedFrame(master, u8_recedata);
    }
}

void Sbus_Master_Register(sbus_master_st *master)
{
    uint8_t u8_index = 0;

    if(master->sbus_id >= SBUS_TYPE_MAX ||
        sbus_masters[master->sbus_id] != NULL)
    {
        err("sbus id error %d\n", master->sbus_id);
        return;
    }

    master->u8_ReceCount = 0;
    master->f_LoopBackFrame = FALSE;
    master->f_BusReceIE = FALSE;
    master->f_SendError = FALSE;
    master->u16_ReceErrorFlag = 0;
    master->u8_SendErrorCount = 0;
    master->slave_num = 0;
    for (u8_index = 0; u8_index < SBUS_SLAVE_MAX; u8_index++)
    {
        master->ary_ReceErrorCount[u8_index] = 0;
        master->slaves[u8_index] = 0;
    }
    
    master->u8_OverTimeCount = 0;
    master->f_BusSendIE = TRUE;
    master->f_SendCompleted = FALSE;
    sbus_masters[master->sbus_id] = master;
}

void Sbus_Slave_Register(sbus_type_e busId, sbus_slave_st *slave)
{
    sbus_master_st *master;

    if(busId >= SBUS_TYPE_MAX || sbus_masters[busId] == NULL)
    {
        err("sbus id not existed %d\n", busId);
        return;
    }

    master = sbus_masters[busId];
    slave->master = master;
    slave->poll_pri = SBUS_PKT_PRI_LEVEL6;
    master->slaves[master->slave_num++] = slave;
}

void Handle_Sbus_Frame(void)
{
    sbus_type_e index;

    for(index = 0; index < SBUS_TYPE_MAX; index++)
    {
        Sbus_Arbitrate(sbus_masters[index]);
    }
}

void Sbus_Slave_Request(sbus_slave_st *slave, sbus_pkt_pri_e pri)
{
    if(pri < SBUS_PKT_PRI_LEVEL0 || pri > SBUS_PKT_PRI_LEVEL7)
    {
        err("slave pri is out of range\n");
        return;
    }

    slave->pri_mask |= 1 << pri;
    return;
}

bool Is_Sbus_Slave_Err(sbus_slave_st *slave)
{
    uint8_t u8_index = 0;
    sbus_master_st *master = slave->master;

    for (u8_index = 0; u8_index < master->slave_num; u8_index++)
    {
        if(master->slaves[u8_index] == slave)
        {
            if(master->u16_ReceErrorFlag & (1 << u8_index))
            {
                return true;
            }
            break;
        }
    }
    return false;
}

bool Is_Sbus_Master_Err(sbus_slave_st *slave)
{
    sbus_master_st *master = slave->master;

    if(master->f_SendError == TRUE)
    {
        return true;
    }
    return false;
}

