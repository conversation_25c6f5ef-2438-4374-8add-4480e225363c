/*!
 * @file
 * @brief Manages all the state variables of the fridge runner.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "FridgeRunner.h"
#include "Core_CallBackTimer.h"
#include "CoolingCycle.h"
#include "Parameter_TemperatureZone.h"
#include "ParameterManager.h"
#include "Driver_AdSample.h"
#include "Driver_DoorSwitch.h"
#include "Driver_SingleDamper.h"
#include "Driver_Fan.h"
#include "IO_Device.h"
#include "Drive_Valve.h"
#include "ResolverDevice.h"
#include "SystemTimerModule.h"
#include "VerticalBeamHeater.h"
#include "DisplayInterface.h"
#include "Driver_Flash.h"
#include "Sbus_IceMaker.h"
#include "InverterUsart.h"
#include "CloudControl.h"
#include "FaultCode.h"

#define FRIDGE_NUM_COUNTS_PER_MINUTE (uint16_t)60
#define U16_FRIDGERUNNER_CYCLE_SECOND (uint16_t)1
#define U16_TEMP_SWINGS_RANGE (uint16_t)20
#define U16_ENTER_ENERGY_MODE_MINUTES (uint16_t)(20 * 60)
#define U16_ENTER_CONDENSATION_MODE_MINUTES (uint16_t)240
#define U16_EXIT_CONDENSATION_MODE_MINUTES (uint16_t)60
#define U16_ROOM_RANGE_EXIT_MINUTES (uint16_t)10
#define U16_ROOM_SWINGS_EXIT_MINUTES (uint16_t)30
#define U16_HUMI_RANGE_EXIT_MINUTES (uint16_t)20
#define U8_ENERGY_MODE_EXIT_DOOR_OPEN_COUNT (uint8_t)10
#define U8_REF_FROST_REDUCE_REF_FAN_DUTY (uint8_t)50
#define U8_REF_FROST_REDUCE_REF_FAN_DUTY_COMP_LOW (uint8_t)30
#define U8_REF_FROST_REDUCE_DOOR_OPEN_COUNT (uint8_t)5
#define U8_REF_FROST_REDUCE_HEATER_ON_MINUTES (uint8_t)10
#define U8_REF_FROST_REDUCE_RUN_IN_ENERGY_MODE_MINUTES (uint8_t)10
#define U8_VALVE_STAY_REF_FROST_MINUTES (uint8_t)30
#define U16_POWER_ON_DELAY_REF_COLLING_MINUTES (uint16_t)30
#define U16_POWER_ON_DELAY_VAR_COLLING_MINUTES (uint16_t)10
#define U8_HIGHLOAD_EXIT_DOOR_OPEN_COUNT (uint8_t)5
#define U16_ENTER_HIGHLOAD_MODE_MINUTES (uint16_t)(20 * 60)
#define U16_HIGHLOAD_DEFROST_MINUTES (uint16_t)(5 * 24 * 60)

static uint16_t ary_RefFrostDuration[] =
{
    28,
    25,
    20
};
static DefrostNormalCondition_st ary_DefrostNormalConditionFix[3];
static bool b_param_synced = false;
static bool b_param_fixed = false;
// clang-format off
static DefrostNormalCondition_st ary_DefrostNormalCondition[] = {
    // RT <= 13
    {
        5,
        { // 冰箱时间    温区时间  冷藏门  冷冻门  总开门  退出温度
            { 12 * 60,  0xFFFF,   360,   360,   0xFFFF, CON_8P0_DEGREE },
            { 16 * 60,  0xFFFF,   180,   180,   0xFFFF, CON_8P0_DEGREE },
            { 25 * 60,  0xFFFF,   120,   120,   0xFFFF, CON_8P0_DEGREE },
            { 36 * 60,  0xFFFF,    60,    60,   0xFFFF, CON_8P0_DEGREE },
            { 48 * 60,  0xFFFF,     0,     0,   0xFFFF, CON_3P0_DEGREE },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 } } },
    // 13 < RT <= 40
    {
        7,
        { // 冰箱时间   温区时间  冷藏门  冷冻门  总开门  退出温度
            { 12 * 60,  0xFFFF,  540,   540,   0xFFFF, CON_8P0_DEGREE },
            { 16 * 60,  0xFFFF,  360,   360,   0xFFFF, CON_8P0_DEGREE },
            { 25 * 60,  0xFFFF,  240,   240,   0xFFFF, CON_8P0_DEGREE },
            { 36 * 60,  0xFFFF,  180,   180,   0xFFFF, CON_8P0_DEGREE },
            { 48 * 60,  0xFFFF,  180,   180,   0xFFFF, CON_8P0_DEGREE },
            { 60 * 60,  0xFFFF,  120,   120,   0xFFFF, CON_8P0_DEGREE },
            { 73 * 60,  0xFFFF,    0,     0,   0xFFFF, CON_8P0_DEGREE } } },
    // 40 < RT
    {
        3,
        { // 冰箱时间   温区时间   冷藏门  冷冻门  总开门  退出温度
            { 12 * 60,  0xFFFF,   180,   180,   0xFFFF, CON_8P0_DEGREE },
            { 16 * 60,  0xFFFF,   120,   120,   0xFFFF, CON_8P0_DEGREE },
            { 25 * 60,  0xFFFF,     0,     0,   0xFFFF, CON_1P0_DEGREE },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 } } }
};
// clang-format on

enum
{
    Signal_Entry = SimpleFsmSignal_Entry,
    Signal_Exit = SimpleFsmSignal_Exit,
    Signal_PollTimerExpired = SimpleFsmSignal_UserStart,
    Signal_EnterDefrosting
};

static st_CoreCallbackTimer st_RunningTimer;
static SimpleFsm_t st_RunningFsm;
static RunningState_t runningState;
static uint16_t u16_FridgeRuntimeStart;
static uint16_t u16_FridgeRuntimeMinute;
static uint32_t u32_FridgePoweronMinute;
static bool b_PowerOnDelayVarCooling = true;
static bool b_PowerOnDelayRefCooling = false;
static uint16_t u16_DeforstExitTemp = U16_DEFROST_EXIT_TEMPERATURE;
static EnterDefrostingState_t enterDefrostingState;
static EnterDefrostingState_t enterDefrostingStateBak;
static bool b_EnterDeforstDelay = false;
static uint16_t u16_EnterDeforstDelayTimeStart;
static bool b_FirstDefrostingEntered;
static CoolingEntryMode_t coolingEntryMode;
static SaveDefrostType_st st_SaveDefrostType;
static TurboFreezeDefrostingState_t turboFreezeDefrostingState;
static uint16_t u16_EnergyModeDefrostTimeStart;
static uint16_t u16_EnergyDefrostModeMinute;
static bool b_TurboFreezeDefrostingAutoExitState;
static bool b_OverLoadDefrostingInTurboFreeze;
static bool b_EnergyConsumptionMode;
static bool b_EnergyConsumptionDefrostMode;
static bool b_EnergyModeFirstDeforst;
static uint16_t u16_EnterEnergyModeTimeStart;
static uint16_t u16_EnterEnergyModeMinute;
static uint16_t u16_EnterEnergyModeTotalMinute;
static uint16_t u16_ExitEnergyModeTimeStartRt;
static uint16_t u16_ExitEnergyModeTimeStartRs;
static uint16_t u16_ExitEnergyModeTimeStartHum;
static uint16_t u16_RoomTempBackup;
static uint16_t u16_EneryExitRoomTempBackup;
static bool b_EneryExitRoomTempSwingsOverRange;
static bool b_CondensationMode;
static uint16_t u16_EnterCondensationModeTimeStart;
static uint16_t u16_EnterCondensationModeMinute;
static uint16_t u16_ExitCondensationModeTimeStart;
static uint16_t u16_ExitCondensationModeMinute;
static uint8_t u8_RefDoorCounter;
static uint8_t u8_FrzDoorCounter;
static uint32_t u32_RefFrzDoorCounter;
static bool b_RefFrostReduceMode;
static bool b_RefFrostReduceHearterOn;
static bool b_RefFrostReduceExpired = true;
static uint8_t u8_RefFrostReduceFanDuty;
static bool b_ValveStayRefFrost;
static uint16_t u16_ExitRefFrostReduceModeTemp;
static uint8_t u8_RefFrostLeftDoorCounterTemp;
static uint8_t u8_RefFrostRightDoorCounterTemp;
static uint32_t u32_RefFrostDoorCounter;
static uint16_t u16_RefFrostStartMinute;
static uint16_t u16_ValveStayRefFrostStartMinute;
static uint16_t u16_OverCoolRefFrostStartMinute;
static bool b_CompOnDefrostOverLoad;
static bool b_CompOnDefrostFrzOverLoad;
static uint16_t u16_RefIonCount;
static uint16_t u16_FrzIonCount;
static uint16_t u16_RefIonCycles;
static uint16_t u16_FrzIonCycles;
static uint16_t u16_RefIonTargetCycles;
static uint16_t u16_FrzIonTargetCycles;
static uint16_t u16_PeekValleyDeforstStart;
static uint8_t u8_PeekValleyDeforstFlag;
static uint8_t u8_PeekValleyDeforstFlagBak;
static uint8_t u8_PeekValleyDeforstCount;
static bool b_HighLoadMode;
static bool b_HighLoadDefrostMode;
static uint8_t u8_HighLoadRefDoorCounter;
static uint8_t u8_HighLoadFrzDoorCounter;
static uint32_t u32_HighLoadRefFrzDoorCounter;
static uint16_t u16_HighLoadRoomTempBackup;
static uint16_t u16_EnterHighLoadTimeStart;
static uint16_t u16_HighLoadDefrostTimeStart;
static uint16_t u16_EnterHighLoadMinute;
static uint16_t u16_ExitHighLoadTimeStartRt;
static uint16_t u16_ExitHighLoadTimeStartRs;
static uint16_t u16_HighLoadRoomTempBackup;
static uint16_t u16_HighLoadExitRoomTempBackup;
static bool b_HighLoadExitRoomTempSwingsOverRange;
static uint16_t u16_TotalPower;
static uint32_t u32_ElectricEnergy;

static void RunningState_CoolingCycle(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void RunningState_Defrosting(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);

static void Judge_EnergyConsumptionMode(void)
{
    bool door_state = Get_DoorSwitchState((DoorTypeId_t)DOOR_ALL);
    uint8_t fault_number = Get_FaultCodeNumber();
    HumidityRange_t humi_range = Get_HumidityRange();
    RoomTempRange_t room_range = Get_RoomTempRange();
    bool b_room_energy = false;
    uint16_t room_temp = Get_SensorValue((uint8_t)SENSOR_ROOM);
    bool b_RoomTempSwingsOverRange = false;
    uint8_t ref_door_counter = 0;
    bool b_time_out = false;
    uint8_t frz_temp_set = Get_FrzSetTemp();
    bool def_func_error = Get_DefrostFunctionError();

    if((room_range == RT_BELOW18) || (room_range == RT_BELOW35))
    {
        b_room_energy = true;
    }

    if((u16_RoomTempBackup > (room_temp + U16_TEMP_SWINGS_RANGE)) ||
        ((u16_RoomTempBackup + U16_TEMP_SWINGS_RANGE) < room_temp))
    {
        u16_RoomTempBackup = room_temp;
        b_RoomTempSwingsOverRange = true;
    }

    if(false == b_EnergyConsumptionMode)
    {
        if((false == door_state) &&
            (true == b_room_energy) &&
            (false == b_RoomTempSwingsOverRange) &&
            (false == IsRefTurboCool()) &&
            (false == IsFrzDeepFreeze()) &&
            (frz_temp_set >= FRZ_LEVEL_F22) &&
            (0 == fault_number) &&
            (HBELOW70 >= humi_range))
        {
            u16_EnterEnergyModeMinute = Get_MinuteElapsedTime(u16_EnterEnergyModeTimeStart);
            if(u16_EnterEnergyModeMinute >= U16_ENTER_ENERGY_MODE_MINUTES)
            {
                b_EnergyConsumptionMode = true;
                b_EnergyModeFirstDeforst = false;
                u16_RoomTempBackup = room_temp;
                u16_EneryExitRoomTempBackup = room_temp;
                u16_ExitEnergyModeTimeStartHum = Get_MinuteCount();
                u16_ExitEnergyModeTimeStartRt = Get_MinuteCount();
                u16_ExitEnergyModeTimeStartRs = Get_MinuteCount();
                u16_EnergyModeDefrostTimeStart = Get_MinuteCount();
                u8_RefDoorCounter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_REF);
                u8_FrzDoorCounter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_FRZ);
                u32_RefFrzDoorCounter = 0;
                u16_EnterEnergyModeTotalMinute = 0;
                b_EnergyConsumptionDefrostMode = false;
            }
        }
        else
        {
            u16_EnterEnergyModeTimeStart = Get_MinuteCount();
        }
    }
    else
    {
        ref_door_counter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_REF);
        u32_RefFrzDoorCounter += (uint8_t)(ref_door_counter - u8_RefDoorCounter);
        u8_RefDoorCounter = ref_door_counter;
        ref_door_counter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_FRZ);
        u32_RefFrzDoorCounter += (uint8_t)(ref_door_counter - u8_FrzDoorCounter);
        u8_FrzDoorCounter = ref_door_counter;

        if((u16_EneryExitRoomTempBackup > (room_temp + U16_TEMP_SWINGS_RANGE)) ||
            ((u16_EneryExitRoomTempBackup + U16_TEMP_SWINGS_RANGE) < room_temp))
        {
            b_EneryExitRoomTempSwingsOverRange = true;
        }
        else
        {
            b_EneryExitRoomTempSwingsOverRange = false;
        }

        if((false == b_room_energy) &&
            (U16_ROOM_RANGE_EXIT_MINUTES < Get_MinuteElapsedTime(u16_ExitEnergyModeTimeStartRt)))
        {
            b_time_out = true;
        }
        else if(true == b_room_energy)
        {
            u16_ExitEnergyModeTimeStartRt = Get_MinuteCount();
        }

        if((true == b_EneryExitRoomTempSwingsOverRange) &&
            (U16_ROOM_SWINGS_EXIT_MINUTES < Get_MinuteElapsedTime(u16_ExitEnergyModeTimeStartRs)))
        {
            b_time_out = true;
        }
        else if(false == b_EneryExitRoomTempSwingsOverRange)
        {
            u16_ExitEnergyModeTimeStartRs = Get_MinuteCount();
        }

        if((HBELOW70 < humi_range) &&
            (U16_HUMI_RANGE_EXIT_MINUTES < Get_MinuteElapsedTime(u16_ExitEnergyModeTimeStartHum)))
        {
            b_time_out = true;
        }
        else if(HBELOW70 >= humi_range)
        {
            u16_ExitEnergyModeTimeStartHum = Get_MinuteCount();
        }

        if((u32_RefFrzDoorCounter > U8_ENERGY_MODE_EXIT_DOOR_OPEN_COUNT) ||
            ((EnterDefrostingState_t)eEnterState_OverLoadError == enterDefrostingState) ||
            (true == IsRefTurboCool()) || 
            (true == IsFrzDeepFreeze()) ||
            (false == IsFrzDeepFreeze() && frz_temp_set < FRZ_LEVEL_F22) ||
            (true == def_func_error) ||
            (0 != fault_number) ||
            (true == b_time_out))
        {
            b_EnergyConsumptionMode = false;
            u16_RoomTempBackup = room_temp;
            u16_EnterEnergyModeTimeStart = Get_MinuteCount();
        }
    }
}

static void Judge_HighLoadMode(void)
{
    bool door_state = Get_DoorSwitchState((DoorTypeId_t)DOOR_ALL);
    uint8_t fault_number = Get_FaultCodeNumber();
    RoomTempRange_t room_range = Get_RoomTempRange();
    bool b_room_highload = false;
    uint16_t room_temp = Get_SensorValue((uint8_t)SENSOR_ROOM);
    bool b_RoomTempSwingsOverRange = false;
    uint8_t ref_door_counter = 0;
    bool b_time_out = false;
    uint8_t icemaker_mode;

    GetSysParam(SYSPARAM_ICEMAKER_FUNC, &icemaker_mode);

    if(room_range == RT_UP40)
    {
        b_room_highload = true;
    }

    if((u16_HighLoadRoomTempBackup > (room_temp + U16_TEMP_SWINGS_RANGE)) ||
        ((u16_HighLoadRoomTempBackup + U16_TEMP_SWINGS_RANGE) < room_temp))
    {
        u16_HighLoadRoomTempBackup = room_temp;
        b_RoomTempSwingsOverRange = true;
    }

    if(false == b_HighLoadMode)
    {
        if((false == door_state) &&
            (true == b_room_highload) &&
            (false == b_RoomTempSwingsOverRange) &&
            (false == IsRefTurboCool()) &&
            (false == IsFrzDeepFreeze()) &&
            (icemaker_mode != eIceMaker_Quick_Mode) &&
            (0 == fault_number))
        {
            u16_EnterHighLoadMinute = Get_MinuteElapsedTime(u16_EnterHighLoadTimeStart);
            if(u16_EnterHighLoadMinute >= U16_ENTER_HIGHLOAD_MODE_MINUTES)
            {
                b_HighLoadMode = true;
                u16_HighLoadRoomTempBackup = room_temp;
                u16_HighLoadExitRoomTempBackup = room_temp;
                u16_ExitHighLoadTimeStartRt = Get_MinuteCount();
                u16_ExitHighLoadTimeStartRs = Get_MinuteCount();
                u8_HighLoadRefDoorCounter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_REF);
                u8_HighLoadFrzDoorCounter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_FRZ);
                u32_HighLoadRefFrzDoorCounter = 0;
                u16_HighLoadDefrostTimeStart = Get_MinuteCount();
                b_HighLoadDefrostMode = false;
            }
        }
        else
        {
            u16_EnterHighLoadTimeStart = Get_MinuteCount();
        }
    }
    else
    {
        ref_door_counter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_REF);
        u32_HighLoadRefFrzDoorCounter += (uint8_t)(ref_door_counter - u8_HighLoadRefDoorCounter);
        u8_HighLoadRefDoorCounter = ref_door_counter;
        ref_door_counter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_FRZ);
        u32_HighLoadRefFrzDoorCounter += (uint8_t)(ref_door_counter - u8_HighLoadFrzDoorCounter);
        u8_HighLoadFrzDoorCounter = ref_door_counter;

        if((u16_HighLoadExitRoomTempBackup > (room_temp + U16_TEMP_SWINGS_RANGE)) ||
            ((u16_HighLoadExitRoomTempBackup + U16_TEMP_SWINGS_RANGE) < room_temp))
        {
            b_HighLoadExitRoomTempSwingsOverRange = true;
        }
        else
        {
            b_HighLoadExitRoomTempSwingsOverRange = false;
        }

        if((false == b_room_highload) &&
            (U16_ROOM_RANGE_EXIT_MINUTES < Get_MinuteElapsedTime(u16_ExitHighLoadTimeStartRt)))
        {
            b_time_out = true;
        }
        else if(true == b_room_highload)
        {
            u16_ExitHighLoadTimeStartRt = Get_MinuteCount();
        }

        if((true == b_HighLoadExitRoomTempSwingsOverRange) &&
            (U16_ROOM_SWINGS_EXIT_MINUTES < Get_MinuteElapsedTime(u16_ExitHighLoadTimeStartRs)))
        {
            b_time_out = true;
        }
        else if(false == b_HighLoadExitRoomTempSwingsOverRange)
        {
            u16_ExitHighLoadTimeStartRs = Get_MinuteCount();
        }

        if(Get_MinuteElapsedTime(u16_HighLoadDefrostTimeStart) > U16_HIGHLOAD_DEFROST_MINUTES)
        {
            b_HighLoadDefrostMode = true;
        }

        if((u32_HighLoadRefFrzDoorCounter > U8_HIGHLOAD_EXIT_DOOR_OPEN_COUNT) ||
            (true == IsRefTurboCool()) || 
            (true == IsFrzDeepFreeze()) ||
            (icemaker_mode == eIceMaker_Quick_Mode) ||
            (0 != fault_number) ||
            (true == b_time_out))
        {
            b_HighLoadMode = false;
            u16_HighLoadRoomTempBackup = room_temp;
            u16_EnterHighLoadTimeStart = Get_MinuteCount();
            b_HighLoadDefrostMode = false;
        }
    }
}

static void Judge_CondensationMode(void)
{
    HumidityRange_t humi_range = Get_HumidityRange();
    RoomTempRange_t room_range = Get_RoomTempRange();
    bool b_room_condensation = false;
    bool b_turbo_cool = false;
    bool b_TurboFreeze = false;
    uint8_t icemaker_mode = eIceMaker_Stop_Mode;

    //b_turbo_cool = Get_TurboCoolState();
    //b_TurboFreeze = (Get_TurboFreezeState() || Get_DeepFreezeState());
    GetSysParam(SYSPARAM_ICEMAKER_FUNC, &icemaker_mode);

    if(icemaker_mode == eIceMaker_Quick_Mode)
    {
        b_TurboFreeze = true;
    }

    if((room_range > RT_BELOW23) && (room_range < RT_UP40))
    {
        b_room_condensation = true;
    }

    if(false == b_CondensationMode)
    {
        if((true == b_room_condensation) &&
            (HBELOW70 < humi_range) &&
            (false == b_turbo_cool) &&
            (false == b_TurboFreeze))
        {
            u16_EnterCondensationModeMinute = Get_MinuteElapsedTime(u16_EnterCondensationModeTimeStart);
            if(u16_EnterCondensationModeMinute >= U16_ENTER_CONDENSATION_MODE_MINUTES)
            {
                b_CondensationMode = true;
                u16_ExitCondensationModeTimeStart = Get_MinuteCount();
            }
        }
        else
        {
            u16_EnterCondensationModeTimeStart = Get_MinuteCount();
        }
    }
    else
    {
        if((false == b_room_condensation) ||
            (HBELOW70 >= humi_range) ||
            (true == b_turbo_cool) ||
            (true == b_TurboFreeze))
        {
            u16_ExitCondensationModeMinute = Get_MinuteElapsedTime(u16_ExitCondensationModeTimeStart);
            if(u16_ExitCondensationModeMinute >= U16_EXIT_CONDENSATION_MODE_MINUTES)
            {
                b_CondensationMode = false;
                u16_EnterCondensationModeTimeStart = Get_MinuteCount();
            }
        }
        else
        {
            u16_ExitCondensationModeTimeStart = Get_MinuteCount();
        }
    }
}

static void Judge_RefFrostReduceFanDuty(void)
{
    uint8_t u8_freq = 0;

    u8_RefFrostReduceFanDuty = U8_REF_FROST_REDUCE_REF_FAN_DUTY;
    u8_freq = Get_ResolvedDeviceStatus(DEVICE_Comp);
    if(u8_freq == FREQ_44HZ || u8_freq == FREQ_54HZ)
    {
        u8_RefFrostReduceFanDuty = U8_REF_FROST_REDUCE_REF_FAN_DUTY_COMP_LOW;
    }
    return;
}

static uint16_t Judge_RefFrostReduceDuration(void)
{
    uint8_t index = 0;
    RoomTempRange_t room_range= Get_RoomTempRange();

    if(room_range <= RT_BELOW18)
    {
        index = 0;
    }
    else if(room_range <= RT_BELOW35)
    {
        index = 1;
    }
    else
    {
        index = 2;
    }

    if(index < (sizeof(ary_RefFrostDuration) / sizeof(uint16_t)))
    {
        return ary_RefFrostDuration[index];
    }

    return 0;
}

static void Judge_RefFrostReduceMode(void)
{
    uint16_t ref_temp;
    uint16_t exit_temp;
    bool is_sensor_error;
    uint16_t exit_duration;
    uint16_t ref_deforst_temp;
    uint8_t door_openclose_count;
    bool b_ref_disable= Get_RefDisable();
    uint8_t ref_temp_set = Get_RefSetTemp();
    RoomTempRange_t room_range= Get_RoomTempRange();
    CoolingCompState_t comp_state = Get_CoolingCompState();

    door_openclose_count = Get_DoorOpenCloseCounter(DOOR_REF);
    u32_RefFrostDoorCounter += (uint8_t)(door_openclose_count - u8_RefFrostLeftDoorCounterTemp);
    u8_RefFrostLeftDoorCounterTemp = door_openclose_count;
    door_openclose_count = Get_DoorOpenCloseCounter(DOOR_FRZ);
    u32_RefFrostDoorCounter += (uint8_t)(door_openclose_count - u8_RefFrostRightDoorCounterTemp);
    u8_RefFrostRightDoorCounterTemp = door_openclose_count;

    if(eRunning_CoolingCycle == runningState &&
        eCooling_CompOn == comp_state &&
        b_RefFrostReduceMode == false)
    {
        is_sensor_error = Get_SensorError(SENSOR_REF_DEFROST);
        ref_deforst_temp = Get_SensorValue(SENSOR_REF_DEFROST);
        if(room_range > RT_BELOW13 &&
           is_sensor_error == false &&
           ref_deforst_temp < CON_F23P0_DEGREE)
        {
            if(Get_MinuteElapsedTime(u16_OverCoolRefFrostStartMinute) > 
               U16_REF_REDUCE_FROST_REF_OVERCOOL_TIME_MINUTES)
            {
                Set_RefFrostReduceMode();
                b_RefFrostReduceHearterOn = true;
            }
        }
        else
        {
            u16_OverCoolRefFrostStartMinute = Get_MinuteCount();
        }


        if(Driver_ValveStateGetStayTime(Valve_FrzOFF_RefON) >=
            U16_REF_REDUCE_FROST_REF_VLAVE_TOTAL_ON_TIME_MINUTES)
        {
            ref_temp = Get_SensorValue(SENSOR_REF);
            ref_deforst_temp = Get_SensorValue(SENSOR_REF_DEFROST);
            is_sensor_error = Get_SensorError(SENSOR_REF_DEFROST) || Get_SensorError(SENSOR_REF);

            if(is_sensor_error == false &&
               ref_temp > CON_15P0_DEGREE &&
               ref_deforst_temp < CON_F20P0_DEGREE)
            {
                u16_ExitRefFrostReduceModeTemp = CON_8P0_DEGREE;
                b_RefFrostReduceHearterOn = true;
            }
            else if(u32_RefFrostDoorCounter > U8_REF_FROST_REDUCE_DOOR_OPEN_COUNT)
            {
                u16_ExitRefFrostReduceModeTemp = CON_6P0_DEGREE;
            }
            else
            {
                exit_temp = 10 * (ref_temp_set - 1) + CON_0P0_DEGREE - 20;
                if(IsRoomTempBelowEightDegree())
                {
                    u16_ExitRefFrostReduceModeTemp = CON_3P0_DEGREE;
                }
                else
                {
                    u16_ExitRefFrostReduceModeTemp = MAX(exit_temp, CON_3P0_DEGREE);
                }
            }
            u16_RefFrostStartMinute = Get_MinuteCount();
            u16_ValveStayRefFrostStartMinute = Get_MinuteCount();
            b_ValveStayRefFrost = true;
            b_RefFrostReduceMode = true;
            b_RefFrostReduceExpired = false;
            Judge_RefFrostReduceFanDuty();
        }

        if(b_RefFrostReduceMode)
        {
            Disable_LinYunPowerSave();
        }
    }
    else
    {
        u16_OverCoolRefFrostStartMinute = Get_MinuteCount();
    }

    if(b_RefFrostReduceMode)
    {
        exit_duration = Judge_RefFrostReduceDuration();
        Vote_DeviceStatus((uint8_t)FSM_RefFrostReduceControl, DEVICE_Valve, Valve_FrzON_RefOFF);
        Vote_DeviceStatus((uint8_t)FSM_RefFrostReduceControl, DEVICE_RefFan, u8_RefFrostReduceFanDuty);
        
        if(b_RefFrostReduceHearterOn && 
           Get_MinuteElapsedTime(u16_RefFrostStartMinute) > U8_REF_FROST_REDUCE_HEATER_ON_MINUTES)
        {
            Vote_DeviceStatus((uint8_t)FSM_RefFrostReduceControl, DEVICE_RefFan, 0);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefDefHeater, true);
        }
        
        if(b_ref_disable == true)
        {
            Exit_RefFrostReduceMode();
        }
        else if(b_EnergyConsumptionMode == true && 
            Get_MinuteElapsedTime(u16_RefFrostStartMinute) >
            U8_REF_FROST_REDUCE_RUN_IN_ENERGY_MODE_MINUTES)
        {
            Exit_RefFrostReduceMode();
        }
        else
        {
            is_sensor_error = Get_SensorError(SENSOR_REF_DEFROST);
            ref_deforst_temp = Get_SensorValue(SENSOR_REF_DEFROST);

            if(Get_MinuteElapsedTime(u16_RefFrostStartMinute) > exit_duration)
            {
                b_RefFrostReduceExpired = true;
            }

            if(b_EnergyConsumptionMode == true)
            {
                u16_ExitRefFrostReduceModeTemp = U16_ENERGY_MODE_DEFROST_EXIT_TEMPERATURE;
            }

            if(is_sensor_error == false &&
               ref_deforst_temp >= u16_ExitRefFrostReduceModeTemp)
            {
                Exit_RefFrostReduceMode();
            }
        }
    }

    if(b_ValveStayRefFrost == true && 
       Get_MinuteElapsedTime(u16_ValveStayRefFrostStartMinute) >
       U8_VALVE_STAY_REF_FROST_MINUTES)
    {
        b_ValveStayRefFrost = false;
    }
}

static void Update_PeekValleyPowerDeforst(void)
{
    if(u8_PeekValleyDeforstFlagBak &&
       Get_MinuteElapsedTime(u16_PeekValleyDeforstStart) > U16_PEEK_VALLEY_POWER_BAK_MINUTES)
    {
        u8_PeekValleyDeforstFlagBak = 0;
    }
}

static void StartRefIonGeneratorWork(void)
{
    u16_RefIonTargetCycles = U16_ION_ENABLE_TOTAL_CYCLES;
    u16_RefIonCycles = 0;
    u16_RefIonCount = 0;
}

static void StopRefIonGeneratorWork(void)
{
    u16_RefIonTargetCycles = 0;
    u16_RefIonCycles = 0;
    u16_RefIonCount = 0;
}

static void StartFrzIonGeneratorWork(void)
{
    u16_FrzIonTargetCycles = U16_ION_ENABLE_TOTAL_CYCLES;
    u16_FrzIonCycles = 0;
    u16_FrzIonCount = 0;
}

static void StopFrzIonGeneratorWork(void)
{
    u16_FrzIonTargetCycles = 0;
    u16_FrzIonCycles = 0;
    u16_FrzIonCount = 0;
}

static void Ctrl_IonGenerator(void)
{
    static ZoneCoolingState_t save_state = eZoneCooling_Idle;
    ZoneCoolingState_t state = Get_ZoneCoolingState();
    CoolingCompState_t comp_state = Get_CoolingCompState();
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_IonGenerator, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzIonGenerator, DS_Off);

    if(b_EnergyConsumptionMode == false &&
       (runningState == eRunning_CoolingCycle && comp_state == eCooling_CompOn))
    {
        if(state != save_state)
        {
            if(state == eZoneCooling_Ref)
            {
                StartRefIonGeneratorWork();
            }
            else if(state == eZoneCooling_Frz)
            {
                StartFrzIonGeneratorWork();
            }
        
            if(save_state == eZoneCooling_Ref)
            {
                StopRefIonGeneratorWork();
            }
            else if(save_state == eZoneCooling_Frz)
            {
                StopFrzIonGeneratorWork();
            }
            save_state = state;
        }

        if(Get_FanParameter(REF_FAN) > 0 && Is_FanError(REF_FAN) == FALSE &&
           save_state == eZoneCooling_Ref && u16_RefIonTargetCycles > 0)
        {
            if(u16_RefIonCycles < u16_RefIonTargetCycles)
            {
                if(u16_RefIonCount < U16_REF_ION_CYCLE_ONSECOND)
                {
                    Vote_DeviceStatus(FSM_NormalControl, DEVICE_IonGenerator, DS_On);
                }

                if(u16_RefIonCount == (U16_REF_ION_CYCLE_ONSECOND + U16_REF_ION_CYCLE_OFFSECOND))
                {
                    u16_RefIonCycles++;
                    u16_RefIonCount = 0;
                }
                else
                {
                    u16_RefIonCount++;
                }
            }
            else
            {
                Clear_FanTotalRunMinutes(REF_FAN);
                Clear_IonGeneratorOnMinutes();
                StopRefIonGeneratorWork();
            }
        }

        if(Get_FanParameter(FRZ_FAN) > 0 && Is_FanError(FRZ_FAN) == FALSE &&
           save_state == eZoneCooling_Frz && u16_FrzIonTargetCycles > 0)
        {
            if(u16_FrzIonCycles < u16_FrzIonTargetCycles)
            {
                if(u16_FrzIonCount < U16_FRZ_ION_CYCLE_ONSECOND)
                {
                    Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzIonGenerator, DS_On);
                }

                if(u16_FrzIonCount == (U16_FRZ_ION_CYCLE_ONSECOND + U16_FRZ_ION_CYCLE_OFFSECOND))
                {
                    u16_FrzIonCycles++;
                    u16_FrzIonCount = 0;
                }
                else
                {
                    u16_FrzIonCount++;
                }
            }
            else
            {
                Clear_FanTotalRunMinutes(FRZ_FAN);
                Clear_FrzIonGeneratorOnMinutes();
                StopFrzIonGeneratorWork();
            }
        }
    }
    else
    {
        save_state = eZoneCooling_Idle;
        StopRefIonGeneratorWork();
        StopFrzIonGeneratorWork();
    }
}

static void Update_PowerOnDelayRefVarCooling(void)
{
    if((CoolingEntryMode_t)eMode_FridgePowerOn == coolingEntryMode)
    {
        if((true == b_PowerOnDelayVarCooling) && (u16_FridgeRuntimeMinute >= U16_POWER_ON_DELAY_VAR_COLLING_MINUTES))
        {
            b_PowerOnDelayVarCooling = false;
        }

        if((true == b_PowerOnDelayRefCooling) && (u16_FridgeRuntimeMinute >= U16_POWER_ON_DELAY_REF_COLLING_MINUTES))
        {
            b_PowerOnDelayRefCooling = false;
        }
    }
    else
    {
        b_PowerOnDelayVarCooling = false;
        b_PowerOnDelayRefCooling = false;
    }

    if(u16_FridgeRuntimeMinute >= U16_POWER_ON_DELAY_VAR_COLLING_MINUTES || b_PowerOnDelayVarCooling == false)
    {
        if(IsSingleDamperAlreadyReset(Damper_Var) == false)
        {
            Reset_SingleDamper(Damper_Var);
        }
    }
}

void Update_ElectricEnergy(void)
{
    uint16_t u16_12V_power = Get_12VPower();
    uint16_t comp_power = Get_CompPower();
    uint16_t voltage = 0;
    float voltage_coef = 0;
    uint16_t defrost_heater_power = 0;
    uint16_t offset = U16_POWER_OFFSET;
    uint8_t b_defrost_heater_on = Get_ResolvedDeviceStatus(DEVICE_FrzDefHeater);

    if(DS_On == b_defrost_heater_on)
    {
        voltage = Get_CompBusVoltage();
        voltage_coef = ((float)voltage) / FLOAT_AC_VOLTAGE;
        voltage_coef *= voltage_coef;
        defrost_heater_power = (uint16_t)(voltage_coef * FLOAT_DEFROST_RATING);
    }

    if(Get_CompFeedbackFreq() > 0)
    {
        offset = U16_COMP_POWER_OFFSET;
    }
    u16_TotalPower = defrost_heater_power + u16_12V_power + offset + comp_power / 2;
    u32_ElectricEnergy += (uint32_t)u16_TotalPower;
}

static void PollTimerExpired(void)
{
    u16_FridgeRuntimeMinute = Get_MinuteElapsedTime(u16_FridgeRuntimeStart);
    Update_PowerOnDelayRefVarCooling();
    Ctrl_VerticalBeamHeater();
    Process_UserMode();
    Judge_EnergyConsumptionMode();
    Judge_HighLoadMode();
    Judge_CondensationMode();
    Judge_RefFrostReduceMode();
    Ctrl_IonGenerator();
    Update_PeekValleyPowerDeforst();
    SimpleFsm_SendSignal(&st_RunningFsm, Signal_PollTimerExpired, NULL);
}

static void ArmPollTimer(uint16_t tickSec)
{
    Core_CallbackTimer_TimerStart(
        &st_RunningTimer,
        PollTimerExpired,
        tickSec,
        0,
        eCallbackTimer_Type_Periodic,
        eCallbackTimer_Priority_Normal);
}

static void Stop_PollTimer(void)
{
    Core_CallbackTimer_TimerStop(&st_RunningTimer);
}


static void Save_DefrostType(EnterDefrostingState_t state)
{
    uint8_t u8_index = 0;

    st_SaveDefrostType.u8_SavedFlag = 0xAA;

    if(st_SaveDefrostType.u8_SavedCount < U8_DEFROST_TYPE_MAX_SAVE_NUMBER)
    {
        st_SaveDefrostType.u8_SavedCount++;
    }
    else
    {
        st_SaveDefrostType.u8_SavedCount = U8_DEFROST_TYPE_MAX_SAVE_NUMBER;
    }

    if(st_SaveDefrostType.u8_SavedCount >= 2)
    {
        for(u8_index = st_SaveDefrostType.u8_SavedCount - 1; u8_index > 0; u8_index--)
        {
            st_SaveDefrostType.ary_DefrostTypeBuff[u8_index] = st_SaveDefrostType.ary_DefrostTypeBuff[u8_index - 1];
        }
    }

    st_SaveDefrostType.ary_DefrostTypeBuff[0] = state;
}

static uint16_t Calc_DefrostExitTempFromTable(void)
{
    uint8_t u8_index = 0;
    uint8_t u8_defrost_condition_number = 0;
    uint8_t u8_defrost_condition_romm_temp_index = 0;
    RoomTempRange_t room_range = Get_RoomTempRange();
    HumidityRange_t humidity_range = Get_HumidityRange();
    uint16_t u16_condition_ref_door_total_open_time_seconds = 0;
    uint16_t u16_condition_frz_door_total_open_time_seconds = 0;
    DefrostNormalCondition_st *p_condition = (DefrostNormalCondition_st *)NULL;
    uint16_t u16_ref_door_total_open_time_seconds = Get_DoorOpenTimeSecond((DoorTypeId_t)DOOR_REF);
    uint16_t u16_frz_door_total_open_time_seconds = Get_DoorOpenTimeSecond((DoorTypeId_t)DOOR_FRZ);

    if(room_range > RT_BELOW23 && 
       humidity_range > HBELOW70
      )
    {
        return U16_DEFROST_HIGHHUM_EXIT_TEMPERATURE;
    }

    if(room_range <= RT_BELOW13)
    {
        u8_defrost_condition_romm_temp_index = 0;
    }
    else if(room_range <= RT_BELOW40)
    {
        u8_defrost_condition_romm_temp_index = 1;
    }
    else
    {
        u8_defrost_condition_romm_temp_index = 2;
    }
    p_condition = &ary_DefrostNormalCondition[u8_defrost_condition_romm_temp_index];
    if(b_param_fixed)
    {
        p_condition = &ary_DefrostNormalConditionFix[u8_defrost_condition_romm_temp_index];
    }
    u8_defrost_condition_number = p_condition->u8_DefrostConditionNumber;

    if(u8_defrost_condition_number > 0)
    {
        for(u8_index = 0; u8_index < u8_defrost_condition_number; u8_index++)
        {
            u16_condition_ref_door_total_open_time_seconds =
                p_condition->ary_DefrostCondition[u8_index].u16_RefDoorTotalOpenTimeSecond;

            u16_condition_frz_door_total_open_time_seconds =
                p_condition->ary_DefrostCondition[u8_index].u16_FrzVarDoorTotalOpenTimeSecond;

            if(((u16_ref_door_total_open_time_seconds >= u16_condition_ref_door_total_open_time_seconds) ||
                    (u16_frz_door_total_open_time_seconds >= u16_condition_frz_door_total_open_time_seconds)))
            {
                return p_condition->ary_DefrostCondition[u8_index].u16_DefrostExitTemp;
            }
        }
    }
    return U16_DEFROST_EXIT_TEMPERATURE;
}

static uint16_t Calc_DefrostExitTemp(void)
{
    return U16_DEFROST_EXIT_TEMPERATURE;
}

static void Judge_EnterFirstDefrost(void)
{
    if(u16_FridgeRuntimeMinute >= U16_FIRST_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES)
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_First;
        u16_DeforstExitTemp = Calc_DefrostExitTemp();
    }
    else
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
    }
}

static bool Get_OverLoadDefrost(void)
{
    uint16_t comp_still_on_minute = Get_CompStillOnTimeMinute();
    bool b_ref_over_load = false;
    bool b_frz_over_load = false;

    if(b_CompOnDefrostOverLoad == false &&
        (coolingEntryMode == (CoolingEntryMode_t)eMode_DefrostingCompleted))
    {
        b_ref_over_load = Get_RefTempOverLoadDefrost();
        b_frz_over_load = Get_FrzTempOverLoadDefrost();

        if(comp_still_on_minute >= U16_OVERLOAD_DEFROST_COMP_STILL_ON_TIME_MINUTES &&
            (b_ref_over_load || b_frz_over_load))
        {
            b_CompOnDefrostOverLoad = true;
            if(b_frz_over_load)
            {
                b_CompOnDefrostFrzOverLoad = true;
            }
        }
        else if(comp_still_on_minute >= U16_OVERLOAD_DEFROST_COMP_STILL_LONG_ON_TIME_MINUTES &&
            Get_RefTempOverOnDefrost())
        {
            b_CompOnDefrostOverLoad = true;
        }
    }
    return b_CompOnDefrostOverLoad;
}

void Set_TurboFreezeDefrostingAutoExitState(bool state)
{
    b_TurboFreezeDefrostingAutoExitState = state;
    if(false == state || b_OverLoadDefrostingInTurboFreeze == true)
    {
        turboFreezeDefrostingState = (TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_None;
    }
    b_OverLoadDefrostingInTurboFreeze = false;
}

static void Judge_EnterTurboFreezeDefrost(void)
{
    bool b_TurboFreeze = Get_TurboFreezeState() || Get_DeepFreezeState();
    uint16_t turbo_freeze_minute = Get_TurboFreezeTimeMinute();

    switch(turboFreezeDefrostingState)
    {
        case(TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_None:
            if(true == b_TurboFreeze)
            {
                turboFreezeDefrostingState = (TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_First;
            }
            enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
            break;
        case(TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_First:
            if((true == b_TurboFreeze) &&
                (turbo_freeze_minute >= U16_TURBO_FRZ_DEFROST_COMP_TOTAL_ON_TIME_MINUTES))
            {
                enterDefrostingState = (EnterDefrostingState_t)eEnterState_TurboFreeze;
                u16_DeforstExitTemp = Calc_DefrostExitTemp();
                turboFreezeDefrostingState = (TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_Second;
            }
            else
            {
                enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
            }
            break;
        case(TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_Second:
            if((false == b_TurboFreeze) && (true == b_TurboFreezeDefrostingAutoExitState))
            {
                enterDefrostingState = (EnterDefrostingState_t)eEnterState_TurboFreeze;
                u16_DeforstExitTemp = Calc_DefrostExitTempFromTable();
                turboFreezeDefrostingState = (TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_None;
            }
            else
            {
                enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
            }
            break;
        default:
            break;
    }
}

static bool Get_SensorErrorDefrost(void)
{
    bool b_state = false;
    bool b_frz_def_snr_error = Get_SensorError((SensorType_t)SENSOR_DEFROST);
    bool b_frz_snr_error = Get_SensorError((SensorType_t)SENSOR_FRZ);
    bool b_ref_door_error = Get_DoorSwitchErrorState((DoorTypeId_t)DOOR_REF);
    bool b_frz_door_error = Get_DoorSwitchErrorState((DoorTypeId_t)DOOR_FRZ);

    if((true == b_frz_snr_error) || (true == b_frz_def_snr_error))
    {
        b_state = true;
    }

    if((true == b_ref_door_error) || (true == b_frz_door_error))
    {
        b_state = true;
    }

    return (b_state);
}

static void Judge_EnterSensorErrorDefrost(void)
{
    if(u16_FridgeRuntimeMinute >= U16_DEF_FUNCTION_ERROR_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES)
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_SensorError;
        u16_DeforstExitTemp = Calc_DefrostExitTemp();
    }
    else
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
    }
}

static void Judge_EnterDefFunctionErrorDefrost(void)
{
    if(u16_FridgeRuntimeMinute >= U16_DEF_FUNCTION_ERROR_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES)
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_DefFunctionError;
        u16_DeforstExitTemp = Calc_DefrostExitTemp();
    }
    else
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
    }
}

static void Judge_EnterEnergyModeDefrost(void)
{
    uint16_t energy_deforst_minute = 0;

    if(false == b_EnergyModeFirstDeforst)
    {
        energy_deforst_minute = U16_ENERGY_FIRST_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES;
    }
    else
    {
        energy_deforst_minute = U16_ENERGY_MORMAL_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES;
    }

    u16_EnergyDefrostModeMinute = Get_MinuteElapsedTime(u16_EnergyModeDefrostTimeStart);

    if(u16_EnergyDefrostModeMinute >= energy_deforst_minute)
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_EnergyMode;
        u16_DeforstExitTemp = U16_ENERGY_MODE_DEFROST_EXIT_TEMPERATURE;
        u16_EnergyModeDefrostTimeStart = Get_MinuteCount();
        b_EnergyModeFirstDeforst = true;
        b_EnergyConsumptionDefrostMode = false;
        u16_EnterEnergyModeTotalMinute += u16_EnergyDefrostModeMinute;
        if(u16_EnterEnergyModeTotalMinute >= U16_ENERGY_FORCE_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES)
        {
            enterDefrostingState = (EnterDefrostingState_t)eEnterState_Normal;
            u16_DeforstExitTemp = Calc_DefrostExitTempFromTable();
            u16_EnterEnergyModeTotalMinute = 0;
            b_EnergyConsumptionDefrostMode = true;
        }
    }
    else
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
        b_EnergyConsumptionDefrostMode = false;
    }
}

static void Judge_EnterNormalDefrost(void)
{
    uint8_t u8_item = 0;
    uint8_t u8_index = 0;
    uint8_t mute_mode = 0;
    uint8_t mute_enable = 0;
    uint8_t u8_peek_valley_power = 0;
    uint16_t u16_cloud_deforst_gap = 0;
    uint8_t u8_defrost_condition_number = 0;
    uint8_t u8_defrost_condition_romm_temp_index = 0;
    RoomTempRange_t room_range = Get_RoomTempRange();
    HumidityRange_t humidity_range = Get_HumidityRange();
    uint16_t u16_condition_fridge_total_on_time_minutes = 0;
    uint16_t u16_condition_ref_door_total_open_time_seconds = 0;
    uint16_t u16_condition_frz_door_total_open_time_seconds = 0;
    DefrostNormalCondition_st *p_condition = (DefrostNormalCondition_st *)NULL;
    uint16_t u16_ref_door_total_open_time_seconds = Get_DoorOpenTimeSecond((DoorTypeId_t)DOOR_REF);
    uint16_t u16_frz_door_total_open_time_seconds = Get_DoorOpenTimeSecond((DoorTypeId_t)DOOR_FRZ);

    GetSysParam(SYSPARAM_PEEK_VALLEY_POWER, &u8_peek_valley_power);
    GetSysParam(SYSPARAM_LINGYUN_MUTE, &mute_enable);
    mute_mode = Get_Mute_Mode();
    enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;

    if((u8_peek_valley_power || mute_enable > 0) && (mute_mode != LINYUN_MUTE_DEEP))
    {
        u16_cloud_deforst_gap = U16_PEEK_VALLEY_POWER_DEFORST_MINUTES;
        if(mute_enable > 0)
        {
            u16_cloud_deforst_gap = U16_DEEP_MUTE_DEFORST_MINUTES;
        }
        if(u8_PeekValleyDeforstFlag && u16_FridgeRuntimeMinute > u16_cloud_deforst_gap)
        {
            enterDefrostingState = (EnterDefrostingState_t)eEnterState_Normal;
            u16_DeforstExitTemp = Calc_DefrostExitTempFromTable();
            u8_PeekValleyDeforstFlag = 0;
            return;
        }
    }
	
    if(room_range <= RT_BELOW13)
    {
        u8_defrost_condition_romm_temp_index = 0;
    }
    else if(room_range <= RT_BELOW40)
    {
        u8_defrost_condition_romm_temp_index = 1;
    }
    else
    {
        u8_defrost_condition_romm_temp_index = 2;
    }
    p_condition = &ary_DefrostNormalCondition[u8_defrost_condition_romm_temp_index];
    if(b_param_fixed)
    {
        p_condition = &ary_DefrostNormalConditionFix[u8_defrost_condition_romm_temp_index];
    }
    u8_defrost_condition_number = p_condition->u8_DefrostConditionNumber;

    if(u8_defrost_condition_number > 0)
    {
        for(u8_index = 0, u8_item = u8_defrost_condition_number - 1;
            u8_index < u8_defrost_condition_number;
            u8_index++, u8_item--)
        {
            u16_condition_fridge_total_on_time_minutes =
                p_condition->ary_DefrostCondition[u8_item].u16_FridgeTotalOnTimeMinutes;

            u16_condition_ref_door_total_open_time_seconds =
                p_condition->ary_DefrostCondition[u8_item].u16_RefDoorTotalOpenTimeSecond;

            u16_condition_frz_door_total_open_time_seconds =
                p_condition->ary_DefrostCondition[u8_item].u16_FrzVarDoorTotalOpenTimeSecond;

            if((u16_FridgeRuntimeMinute >= u16_condition_fridge_total_on_time_minutes) &&
                ((u16_ref_door_total_open_time_seconds >= u16_condition_ref_door_total_open_time_seconds) ||
                    (u16_frz_door_total_open_time_seconds >= u16_condition_frz_door_total_open_time_seconds)))
            {
                enterDefrostingState = (EnterDefrostingState_t)eEnterState_Normal;
                u16_DeforstExitTemp = p_condition->ary_DefrostCondition[u8_item].u16_DefrostExitTemp;
                if(room_range > RT_BELOW23 && 
                   humidity_range > HBELOW70
                  )
                {
                    u16_DeforstExitTemp = U16_DEFROST_HIGHHUM_EXIT_TEMPERATURE;
                }
                break;
            }
        }
    }
}

static bool Fix_DeforstNormalCondition(parameter_section_st *psection)
{
    uint8_t rt;
    uint16_t params;
    uint8_t cond;
    uint16_t sum;
    uint16_t value;
    uint16_t index = 0;
    uint8_t *data = psection->data;
    DefrostNormalCondition_st *p_dnc;

    params = 3 * U8_NORMAL_DEFROST_CONDITION_MAX_NUMBER * 4;
    if(params > psection->length - 3)
    {
        return false;
    }

    for(rt = 0; rt < 3; rt++)
    {
        p_dnc = &ary_DefrostNormalConditionFix[rt];
        sum = 0;
        for(cond = 0; cond < U8_NORMAL_DEFROST_CONDITION_MAX_NUMBER; cond++)
        {
            value = data[index++] << 8;
            value |= data[index++];
            if(value != 0)
            {
                p_dnc->ary_DefrostCondition[cond].u16_CompTotalOnTimeMinutes = 0xFFFF;
                p_dnc->ary_DefrostCondition[cond].u16_AllDoorTotalOpenTimeSecond  = 0xFFFF;
                sum++;
            }
            p_dnc->ary_DefrostCondition[cond].u16_FridgeTotalOnTimeMinutes = value * 60;
            value = data[index++] << 8;
            value |= data[index++];
            p_dnc->ary_DefrostCondition[cond].u16_RefDoorTotalOpenTimeSecond = value;
            value = data[index++] << 8;
            value |= data[index++];
            p_dnc->ary_DefrostCondition[cond].u16_FrzVarDoorTotalOpenTimeSecond = value;
            value = data[index++] << 8;
            value |= data[index++];
            p_dnc->ary_DefrostCondition[cond].u16_DefrostExitTemp = value;
        }
        p_dnc->u8_DefrostConditionNumber = sum;
    }
    return true;
}

static void Update_EnterDefrostingState(void)
{
    bool b_turbo_cool = false;
    bool b_TurboFreeze = false;
    bool b_OverLoadDefrost = false;
    bool b_SensorErrorDefrost = false;
    parameter_section_st *psection = NULL;
    bool b_DefFunctionErrorDefrost = false;
    uint8_t icemaker_mode = eIceMaker_Stop_Mode;

    b_SensorErrorDefrost = Get_SensorErrorDefrost();
    b_DefFunctionErrorDefrost = Get_DefrostFunctionError();
    b_turbo_cool = Get_TurboCoolState();
    b_TurboFreeze = Get_TurboFreezeState() || Get_DeepFreezeState();
    b_OverLoadDefrost = Get_OverLoadDefrost();
    GetSysParam(SYSPARAM_ICEMAKER_FUNC, &icemaker_mode);

    if(b_param_synced == false && IsParameterManagerReady())
    {
        psection = GetParameterSection(PARAMETER_SECTION_DEFORST);
        if(NULL != psection && Fix_DeforstNormalCondition(psection))
        {
            b_param_fixed = true;
        }
        b_param_synced = true;
    }

    if(Get_SmartGrid_Forbid_LoadDeforst() > 0)
    {
        enterDefrostingState = eEnterState_Forbid;
    }
    else if(Get_SmartGrid_Forbid_Deforst() > 0)
    {
        if(Get_SmartGrid_Deforst() > 0 && u16_FridgeRuntimeMinute > U16_PEEK_VALLEY_POWER_DEFORST_MINUTES)
        {
            Set_SmartGrid_Deforst(0);
            u16_DeforstExitTemp = Calc_DefrostExitTemp();
            enterDefrostingState = (EnterDefrostingState_t)eEnterState_Normal;
        }
        else
        {
            Set_SmartGrid_Deforst(0);
            enterDefrostingState = eEnterState_Forbid;
        }
    }
    else if(icemaker_mode == eIceMaker_Quick_Mode)
    {
        enterDefrostingState = eEnterState_Forbid;
    }
    else if(false == b_FirstDefrostingEntered)
    {
        Judge_EnterFirstDefrost();
    }
    else if(b_OverLoadDefrost == true &&
        u16_FridgeRuntimeMinute >= U16_OVERLOAD_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES)
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_OverLoadError;
        u16_DeforstExitTemp = Calc_DefrostExitTemp();
        if(b_TurboFreeze)
        {
            b_OverLoadDefrostingInTurboFreeze = true;
        }
    }
    else if(b_OverLoadDefrostingInTurboFreeze == false &&
        ((true == b_TurboFreeze) || (turboFreezeDefrostingState != (TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_None)))
    {
        Judge_EnterTurboFreezeDefrost();
    }
    else if(true == b_SensorErrorDefrost)
    {
        Judge_EnterSensorErrorDefrost();
    }
    else if(true == b_DefFunctionErrorDefrost)
    {
        Judge_EnterDefFunctionErrorDefrost();
    }
    else if(true == b_turbo_cool)
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
    }
    else if(true == b_EnergyConsumptionMode)
    {
        Judge_EnterEnergyModeDefrost();
    }
    else
    {
        Judge_EnterNormalDefrost();
    }

    u8_PeekValleyDeforstFlag = 0;
    if(enterDefrostingStateBak != enterDefrostingState)
    {
        enterDefrostingStateBak = enterDefrostingState;
        Save_DefrostType(enterDefrostingState);
    }
}

static void RunningState_CoolingCycle(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    RoomTempRange_t rt = Get_RoomTempRange();
    CoolingCompState_t comp_state = Get_CoolingCompState();

    switch(signal)
    {
        case(SimpleFsmSignal_t)Signal_Entry:
            runningState = (RunningState_t)eRunning_CoolingCycle;
            CoolingCycle_Init(coolingEntryMode);
            Clear_DefrostMode();
            enterDefrostingState = eEnterState_Forbid;
            b_CompOnDefrostOverLoad = false;
            b_CompOnDefrostFrzOverLoad = false;
            b_EnterDeforstDelay = false;
            break;
        case(SimpleFsmSignal_t)Signal_PollTimerExpired:
            if(b_EnterDeforstDelay == false)
            {
                Update_EnterDefrostingState();
            }
            
            if((EnterDefrostingState_t)eEnterState_Forbid != enterDefrostingState)
            {
                if(eEnterState_Normal == enterDefrostingState &&
                   eCooling_CompOn == comp_state &&
                   ((RT_UP40 == rt) || (RT_BELOW13 == rt)))
                {
                    if(b_EnterDeforstDelay == false)
                    {
                        u16_EnterDeforstDelayTimeStart = Get_MinuteCount();
                        b_EnterDeforstDelay = true;
                        return;
                    }
                    else if(Get_MinuteElapsedTime(u16_EnterDeforstDelayTimeStart) < U16_ENTER_DEFORST_DELAY_TIME_MINUTES)
                    {
                        return;
                    }
                }
                b_EnterDeforstDelay = false;
                b_ValveStayRefFrost = false;
                Exit_RefFrostReduceMode();
                SimpleFsm_Transition(&st_RunningFsm, RunningState_Defrosting);
            }
            break;
        case(SimpleFsmSignal_t)Signal_Exit:
            CoolingCycle_Exit();
            Reset_ZoneCoolingState();
            break;
        default:
            break;
    }
}

static void RunningState_Defrosting(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    DefrostMode_t defrost_mode;

    switch(signal)
    {
        case(SimpleFsmSignal_t)Signal_Entry:
            runningState = (RunningState_t)eRunning_Defrosting;
            Defrosting_Init(enterDefrostingState);
            b_FirstDefrostingEntered = true;
            u8_PeekValleyDeforstFlag = 0;
            b_ValveStayRefFrost = false;
            break;
        case(SimpleFsmSignal_t)Signal_PollTimerExpired:
            defrost_mode = Get_DefrostMode();
            if((DefrostMode_t)eDefrostMode_Completed == defrost_mode)
            {
                Clear_ValveStayRefForstState();
                coolingEntryMode = (CoolingEntryMode_t)eMode_DefrostingCompleted;
                SimpleFsm_Transition(&st_RunningFsm, RunningState_CoolingCycle);
            }
            break;
        case(SimpleFsmSignal_t)Signal_Exit:
            Defrosting_Exit();
            break;
        default:
            break;
    }
}

void FridgeRunner_Init(void)
{
    if(IsValveAlreadyReset() == false)
    {
        Drive_ValveReset();
    }
    Update_PowerOnDelayRefVarCooling();
    SimpleFsm_Init(&st_RunningFsm, RunningState_CoolingCycle, NULL);
    ArmPollTimer(U16_FRIDGERUNNER_CYCLE_SECOND);
    Init_UserMode();
    b_FirstDefrostingEntered = false;
    u16_FridgeRuntimeStart = Get_MinuteCount();
    u16_FridgeRuntimeMinute = 0;
    turboFreezeDefrostingState = (TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_None;
    b_RefFrostReduceMode = false;
    b_RefFrostReduceExpired = true;
    b_ValveStayRefFrost = false;
    u8_RefFrostLeftDoorCounterTemp = Get_DoorOpenCloseCounter(DOOR_REF);
    u8_RefFrostRightDoorCounterTemp = Get_DoorOpenCloseCounter(DOOR_FRZ);
    u32_RefFrostDoorCounter = 0;
    u16_OverCoolRefFrostStartMinute = Get_MinuteCount();
    u8_PeekValleyDeforstFlag = 0;
    u8_PeekValleyDeforstFlagBak = 0;
    u16_EnterEnergyModeTimeStart = Get_MinuteCount();
    u16_EnterHighLoadTimeStart = Get_MinuteCount();
    u16_EnterCondensationModeTimeStart = Get_MinuteCount();
    Clc_Deforst_First_On_Flag();
    Disable_SmartGridFunc();
}

void FridgeRunner_Exit(void)
{
    Stop_PollTimer();
    CoolingCycle_Exit();
    Defrosting_Exit();
    Exit_RefFrostReduceMode();
}

RunningState_t Get_RunningState(void)
{
    return (runningState);
}

EnterDefrostingState_t Get_EnterDefrostingState(void)
{
    return (enterDefrostingState);
}

uint8_t Get_SavedDefrostType(uint8_t **p_defrost_type_buff)
{
    *p_defrost_type_buff = (uint8_t *)(st_SaveDefrostType.ary_DefrostTypeBuff);

    return (st_SaveDefrostType.u8_SavedCount);
}

void Clear_FridgeTotalOnTimeMinute(void)
{
    u32_FridgePoweronMinute += u16_FridgeRuntimeMinute;
    u16_FridgeRuntimeStart = Get_MinuteCount();
    u16_FridgeRuntimeMinute = 0;
}

uint16_t Get_FridgeTotalOnTimeMinute(void)
{
    return (u16_FridgeRuntimeMinute);
}

uint32_t Get_FridgePowerOnTimeMinute(void)
{
    return (u32_FridgePoweronMinute + u16_FridgeRuntimeMinute);
}

uint16_t Get_DeforstExitTemp(void)
{
    return (u16_DeforstExitTemp);
}

void Set_DeforstExitTemp(uint16_t temp)
{
    enterDefrostingState = eEnterState_Manual;
    u16_DeforstExitTemp = temp;
    return;
}

void Set_CoolingEntryMode(CoolingEntryMode_t mode)
{
    coolingEntryMode = mode;
}

void Set_EnergyConsumptionModeState(bool state)
{
    b_EnergyConsumptionMode = state;
}

bool Get_EnergyConsumptionModeState(void)
{
    return b_EnergyConsumptionMode;
}

bool Get_EnergyConsumptionDeforstModeState(void)
{
    return (b_EnergyConsumptionMode && !b_EnergyConsumptionDefrostMode);
}

bool Get_HighLoadMode(void)
{
    return (b_HighLoadMode && !b_HighLoadDefrostMode);
}

void Clear_HighLoadDeforstMode(void)
{
    if(b_HighLoadMode && b_HighLoadDefrostMode)
    {
        b_HighLoadDefrostMode = false;
        u16_HighLoadDefrostTimeStart = Get_MinuteCount();
    }
}

bool Get_CondensationModeState(void)
{
    return b_CondensationMode;
}

bool Get_RefFrostReduceMode(void)
{
    return b_RefFrostReduceMode;
}

bool Is_RefFrostReduceExpired(void)
{
    return b_RefFrostReduceExpired;
}

void Set_RefFrostReduceMode(void)
{
    RoomTempRange_t room_range= Get_RoomTempRange();
    bool b_ref_disable= Get_RefDisable();
    uint8_t ref_temp_set = Get_RefSetTemp();
    uint16_t exit_temp;

    if(b_ref_disable == true)
    {
        return;
    }

    if(eRunning_CoolingCycle == runningState &&
        b_RefFrostReduceMode == false)
    {
        exit_temp = 10 * (ref_temp_set - 1) + CON_0P0_DEGREE - 20;
        if(room_range == RT_BELOW13)
        {
            u16_ExitRefFrostReduceModeTemp = CON_3P0_DEGREE;
        }
        else
        {
            u16_ExitRefFrostReduceModeTemp = MAX(exit_temp, CON_3P0_DEGREE);
        }
        u16_RefFrostStartMinute = Get_MinuteCount();
        b_RefFrostReduceMode = true;
        b_RefFrostReduceExpired = false;
        Judge_RefFrostReduceFanDuty();
    }
}

void Exit_RefFrostReduceMode(void)
{
    b_RefFrostReduceMode = false;
    b_RefFrostReduceHearterOn = false;
    b_RefFrostReduceExpired = true;
    u8_RefFrostLeftDoorCounterTemp = Get_DoorOpenCloseCounter(DOOR_REF);
    u8_RefFrostRightDoorCounterTemp = Get_DoorOpenCloseCounter(DOOR_FRZ);
    u32_RefFrostDoorCounter = 0;
    Vote_DeviceStatus((uint8_t)FSM_RefFrostReduceControl, DEVICE_Valve, DS_DontCare);
    Vote_DeviceStatus((uint8_t)FSM_RefFrostReduceControl, DEVICE_RefFan, DS_DontCare);
    Vote_DeviceStatus((uint8_t)FSM_RefFrostReduceControl, DEVICE_RefDamper, DS_DontCare);
    Vote_DeviceStatus((uint8_t)FSM_NormalControl, DEVICE_RefDefHeater, false);
}

bool Get_PowerOnDelayRefCoolingState(void)
{
    return (b_PowerOnDelayRefCooling);
}

bool Get_PowerOnDelayVarCoolingState(void)
{
    return (b_PowerOnDelayVarCooling);
}

bool Get_ValveStayRefForstState(void)
{
    return (b_ValveStayRefFrost);
}

void Clear_ValveStayRefForstState(void)
{
    b_ValveStayRefFrost = false;
}

void Force_EnterEnergyConsumptionModeState(void)
{
    uint16_t room_temp = Get_SensorValue((uint8_t)SENSOR_ROOM);

    b_EnergyConsumptionMode = true;
    b_EnergyModeFirstDeforst = false;
    u16_RoomTempBackup = room_temp;
    u16_EneryExitRoomTempBackup = room_temp;
    u16_ExitEnergyModeTimeStartHum = Get_MinuteCount();
    u16_ExitEnergyModeTimeStartRt = Get_MinuteCount();
    u16_ExitEnergyModeTimeStartRs = Get_MinuteCount();
    u16_EnergyModeDefrostTimeStart = Get_MinuteCount();
    u8_RefDoorCounter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_REF);
    u8_FrzDoorCounter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_FRZ);
    u32_RefFrzDoorCounter = 0;
}

uint16_t GetRefIonGeneratorTargetMinutes(void)
{
    return (u16_RefIonTargetCycles * U16_REF_ION_CYCLE_SECOND) / 60;
}

uint16_t GetFrzIonGeneratorTargetMinutes(void)
{
    return (u16_FrzIonTargetCycles * U16_FRZ_ION_CYCLE_SECOND) / 60;
}

uint16_t GetRefIonGeneratorProcessMinutes(void)
{
    return (u16_RefIonCycles * U16_REF_ION_CYCLE_SECOND + u16_RefIonCount) / 60;
}

uint16_t GetFrzIonGeneratorProcessMinutes(void)
{
    return (u16_FrzIonCycles * U16_FRZ_ION_CYCLE_SECOND + u16_FrzIonCount) / 60;
}


uint8_t GetRefIonGeneratorRunProcess(void)
{
    uint8_t process = 100;
    uint32_t runseconds = 0;
    uint32_t targetseconds = 0;

    if(u16_RefIonTargetCycles > 0)
    {
        if(u16_RefIonCycles < u16_RefIonTargetCycles)
        {
            runseconds = u16_RefIonCycles * U16_REF_ION_CYCLE_SECOND + u16_RefIonCount;
            targetseconds = u16_RefIonTargetCycles * U16_REF_ION_CYCLE_SECOND;
            process = runseconds * 100 / targetseconds;
        }
    }
    return process;
}

uint8_t GetFrzIonGeneratorRunProcess(void)
{
    uint8_t process = 100;
    uint32_t runseconds = 0;
    uint32_t targetseconds = 0;

    if(u16_FrzIonTargetCycles > 0)
    {
        if(u16_FrzIonCycles < u16_FrzIonTargetCycles)
        {
            runseconds = u16_FrzIonCycles * U16_FRZ_ION_CYCLE_SECOND + u16_FrzIonCount;
            targetseconds = u16_FrzIonTargetCycles * U16_FRZ_ION_CYCLE_SECOND;
            process = runseconds * 100 / targetseconds;
        }
    }
    return process;
}

uint8_t IsRefIonGeneratorWorking(void)
{
    return u16_RefIonTargetCycles > 0 ? 1 : 0;
}

uint8_t IsFrzIonGeneratorWorking(void)
{
    return u16_FrzIonTargetCycles > 0 ? 1 : 0;
}

void Force_PeekValleyPowerDeforst(void)
{
    u8_PeekValleyDeforstFlag = 1;
    Set_SmartGrid_Deforst(1);
    u8_PeekValleyDeforstFlagBak = 1;
    u16_PeekValleyDeforstStart = Get_MinuteCount();
    u8_PeekValleyDeforstCount++;
}

void Clear_PeekValleyPowerDeforst(void)
{
    u8_PeekValleyDeforstFlagBak = 0;
}

uint8_t Get_PeekValleyPowerDeforst(void)
{
    return u8_PeekValleyDeforstCount;
}

uint8_t Get_PeekValleyDeforstFlag(void)
{
    return u8_PeekValleyDeforstFlagBak;
}

void Clear_EnergyHighLoadDoorOpenCloseCount(void)
{
    u32_RefFrzDoorCounter = 0;
    u32_HighLoadRefFrzDoorCounter = 0;
}

uint16_t Get_TotalPower(void)
{
    return u16_TotalPower;
}

uint32_t Get_ElectricEnergy(void)
{
    return u32_ElectricEnergy;
}

void StopAutoRefIonGeneratorWork(void)
{
    StopRefIonGeneratorWork();
}

void StopAutoFrzIonGeneratorWork(void)
{
    StopFrzIonGeneratorWork();
}

bool IsDefrostFrzOverLoad(void)
{
    return b_CompOnDefrostFrzOverLoad;
}
