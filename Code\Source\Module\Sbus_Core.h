/*!
 * @file
 * @brief This file defines public constants, types and functions for the fan drive module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __SBUS_CORE_H
#define __SBUS_CORE_H

#include <stdio.h>
#include <string.h>
#include <stddef.h>
#include <stdint.h>
#include "base_types.h"
#include "ddl.h"
#include "FirewareComm.h"

#define U8_MAX_RESEND_TIMES      0x03     // 最大重发次数
#define U8_ERROR_RESEND_TIMES      0x01   // 错误重发次数
#define U8_MAX_SEND_ERROR_TIMES 0x0A      // 最大连续发送错误次数
#define U8_MAX_RECE_ERROR_TIMES 0x0A      // 最大连续接收错误次数
#define U8_MAX_NEXT_FRAME_OVERTIME      0x14   // 最大下一帧间隔时间
#define U8_MAX_DATA_OVERTIME      0x05    // 最大数据间隔重发时间
#define U8_MAX_WAIT_RESPONSE_OVERTIME      0x64  // 最大等待应答时间(最大帧间隔重发时间)
#define U8_MIN_WAIT_RESPONSE_OVERTIME      0x10  // 最小等待应答时间(最小帧间隔重发时间)
#define U8_MAX_HANDLE_MESSAGE_OVERTIME      0x10  // 最大报文处理时间
#define U8_ALL_SLAVE_MACHINE_COMM_TIMER_SEONDS      0x05  // 全部从机通讯时间间隔
#define SBUS_SLAVE_MAX 5

struct sbus_master;

typedef enum
{
    SBUS_TYPE_ID0,
    SBUS_TYPE_MAX,
} sbus_type_e;

typedef enum
{
    SBUS_PKT_PRI_LEVEL0 = 0,
    SBUS_PKT_PRI_LEVEL1 = 1,
    SBUS_PKT_PRI_LEVEL2 = 2,
    SBUS_PKT_PRI_LEVEL3 = 3,
    SBUS_PKT_PRI_LEVEL4 = 4,
    SBUS_PKT_PRI_LEVEL5 = 5,
    SBUS_PKT_PRI_LEVEL6 = 6,
    SBUS_PKT_PRI_LEVEL7 = 7,
} sbus_pkt_pri_e;

typedef struct
{
    void (*sendOneData)(uint8_t u8_data);
} sbus_master_ops;

typedef struct
{
    int32_t (*recvFrame)(fireware_frame_st *data);
    fireware_frame_st *(*sendFrame)(sbus_pkt_pri_e pri);
} sbus_slave_ops;

typedef struct
{
    uint32_t sendPacketStatistics;
    uint32_t sendPacketErrorStatistics;
    uint32_t recvPacketStatistics;
    uint32_t recvPacketErrorStatistics;
    uint32_t recvPacketCrcErrorStatistics;
} sbus_slave_stats;

typedef struct
{
    fireware_addr_e addr;
    uint8_t pri_mask;
    struct sbus_master *master;
    sbus_slave_ops ops;
    uint8_t poll_pri;
    sbus_slave_stats stats;
} sbus_slave_st;

typedef struct sbus_master
{
    sbus_type_e sbus_id;
    uint8_t cur_slave;
    uint8_t poll_slave;
    uint8_t slave_num;
    sbus_slave_st *slaves[SBUS_SLAVE_MAX];
    uint16_t u16_ReceErrorFlag;           // 接收错误标识
    uint8_t f_BusSendIE           : 1;    // 发送允许
    uint8_t f_SendCompleted       : 1;    // 发送完成
    uint8_t f_SendError           : 1;    // 发送错误
    uint8_t f_BusReceIE           : 1;    // 接收允许
    uint8_t f_LoopBackFrame       : 1;    // 接收回环帧
    uint8_t f_SendTimeout         : 1;      // 报文发送超时
    uint8_t u8_SendErrorCount;            // 发送错误计数
    uint8_t ary_ReceErrorCount[SBUS_SLAVE_MAX];   // 接收错误计数
    uint8_t u8_ReSendCount;               // 重发次数
    uint8_t u8_SendCount;                 // 发送计数
    uint8_t u8_SendingData;               // 发送中字节
    uint8_t u8_SendDataState; // 发送数据状态
    uint8_t u8_ReceDataState; // 接收数据状态
    uint16_t u16_SendCrcValue;
    uint16_t u16_ReceCrcValue;
    uint8_t u8_SendLength;
    uint8_t u8_ReceLength;
    uint8_t *sendPos;
    uint8_t *recvPos;
    uint8_t u8_ReceCount;                 // 接收计数
    uint8_t u8_WaitResponseOverTime;      // 等待应答超时时间
    uint8_t u8_OverTimeCount;             // 超时计数
    fireware_frame_st sendframe;
    fireware_frame_st recvframe;
    sbus_master_ops ops;
} sbus_master_st;



void Handle_Sbus_Frame(void);
void Handle_SBusOverTime(void);
bool Is_Sbus_Slave_Err(sbus_slave_st *slave);
bool Is_Sbus_Master_Err(sbus_slave_st *slave);
void Sbus_Master_Register(sbus_master_st *master);
void Sbus_ReceData(sbus_master_st *master, uint8_t u8_recedata);
void Sbus_Slave_Register(sbus_type_e busId, sbus_slave_st *slave);
void Sbus_Slave_Request(sbus_slave_st *slave, sbus_pkt_pri_e pri);

#endif
