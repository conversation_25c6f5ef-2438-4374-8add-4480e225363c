/**
 *******************************************************************************
 * @file  flash.c
 * @brief This file provides - functions to manage the FLASH.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-12-12       MADS            First version
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2023, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 *******************************************************************************
 */

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "flash.h"

/**
 * @addtogroup HC32L186_DDL 驱动库
 * @{
 */

/**
 * @defgroup DDL_FLASH FLASH模块驱动库
 * @brief FLASH Driver Library FLASH模块驱动库
 * @{
 */


/*******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/
/**
 * @defgroup FLASH_Local_Macros FLASH局部宏定义
 * @{
 */
/**
 * @defgroup FLASH_Value_Define FLASH模式值设置
 * @{
 */

#define FLASH_END_ADDR              (0x0003FFFFu)
#define FLASH_BYPASS()              do{ M0P_FLASH->BYPASS = 0x5A5A;\
                                        M0P_FLASH->BYPASS = 0xA5A5;\
                                    }while(0);
#define FLASH_IE_TRUE               (0x03)
#define FLASH_IE_FALSE              (0x00)

#define FLASH_TIMEOUT_INIT          (0xFFFFFFu)
#define FLASH_TIMEOUT_PGM           (0xFFFFFFu)
#define FLASH_TIMEOUT_ERASE         (0xFFFFFFu)

#define FLASH_LOCK_ALL              (0u)
#define FLASH_UNLOCK_ALL            (0xFFFFFFFFu)


/**
 * @}
 */

/**
 * @}
 */

/*******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/*******************************************************************************
 * Global variable definitions (declared in header file with 'extern')
 ******************************************************************************/

/**
 * @defgroup FLASH_Global_Variable FLASH全局变量定义
 * @{
 */
/**
 * @defgroup FLASH_Config_Define FLASH相关参数设置
 * @{
 */

const uint32_t pu32PcgTimer4M[] =
{
    0x67u,     /*!< 0:TPNVS   */
    0xEFu,     /*!< 1:TPGS    */
    0x17u,     /*!< 2:TPROG   */
    0x270Fu,   /*!< 3:TSERASE */
    0x222DFu,  /*!< 4:TMERASE */
    0xEFu,     /*!< 5:TPRCV   */
    0xEFu,     /*!< 6:TSRCV   */
    0x3E7u,    /*!< 7:TMRCV   */
    0x67u,     /*!< 8:TSNVS   */
    0x19Fu,    /*!< 9:TMNVS   */
    0x2u,      /*!< 10:TADS   */
    0x2u,      /*!< 11:TADH   */
    0x2u,      /*!< 12:TPGH   */
    0x3u,      /*!< 13:TRW1   */
    0x2Fu      /*!< 14:TRW2   */
};

/**
 * @}
 */

/**
 * @}
 */
/*******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/

/*******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/

/*******************************************************************************
 * Function implementation - global ('extern') and local ('static')
 ******************************************************************************/

/**
 * @defgroup FLASH_Global_Functions FLASH全局函数定义
 * @{
 */



/**
 * @brief  Flash中断标志获取
 * @param  [in] enFlashIntType: Flash 中断类型枚举  @ref en_flash_int_type_t
 * @retval boolean_t:
 *              - TRUE: 标志置位
 *              - FALSE: 标志未置位
 */
boolean_t Flash_GetIntFlag(en_flash_int_type_t enFlashIntType)
{
    boolean_t bRetVal = FALSE;

    if (M0P_FLASH->IFR & enFlashIntType)
    {
        bRetVal =  TRUE;
    }

    return bRetVal;
}


/**
 * @brief  Flash中断标志清除
 * @param  [in] enFlashIntType: Flash 中断类型枚举  @ref en_flash_int_type_t
 * @retval en_result_t:
 *              - Ok: 标志置位
 *              - Error: 标志未置位
 */
en_result_t Flash_ClearIntFlag(en_flash_int_type_t enFlashIntType)
{
    en_result_t enResult = Error;

    M0P_FLASH->ICLR &= ~(uint32_t)enFlashIntType;
    enResult = Ok;

    return enResult;
}


/**
 * @brief  Flash中断使能
 * @param  [in] enFlashIntType: Flash Flash中断类型  @ref en_flash_int_type_t
 * @retval en_result_t:
 *              - Ok: 标志置位
 *              - Error: 标志未置位
 */
en_result_t Flash_EnableIrq(en_flash_int_type_t enFlashIntType)
{
    en_result_t enResult = Error;

    FLASH_BYPASS();
    M0P_FLASH->CR_f.IE |= enFlashIntType;

    enResult = Ok;

    return enResult;
}


/**
 * @brief  Flash中断禁止
 * @param  [in] enFlashIntType: Flash Flash中断类型  @ref en_flash_int_type_t
 * @retval en_result_t:
 *              - Ok: 禁止成功
 *              - Error: 禁止失败
 */
en_result_t Flash_DisableIrq(en_flash_int_type_t enFlashIntType)
{
    en_result_t enResult = Error;

    FLASH_BYPASS();
    M0P_FLASH->CR_f.IE &= ~(uint32_t)enFlashIntType;

    enResult = Ok;

    return enResult;
}

/**
 * @brief  FLASH 初始化函数——中断服务程序、编程时间配置及低功耗模式
 * @param  [in] u8FreqCfg: FLASH编程时钟频率配置(根据HCLK的频率选择配置值):
 *              - 1       :4MHz;
 *              - 2       :8MHz;
 *              - 4       :16MHz;
 *              - 6       :24MHz;
 *              - 8       :32MHz;
 *              - 12      :48MHz;
 *              - other   :无效值
 * @param  [in] bDpstbEn: 低功耗模式使能位:
 *              - TRUE   : 当系统进入DeepSleep模式，FLASH进入低功耗模式;
 *              - FALSE  : 当系统进入DeepSleep模式，FLASH不进入低功耗模式;
 * @retval en_result_t:
 *              - Ok                    :操作成功.
 *              - ErrorInvalidParameter :参数无效.
 *              - ErrorUninitialized    :初始化失败
 */
en_result_t Flash_Init(uint8_t u8FreqCfg, boolean_t bDpstbEn)
{
    uint32_t                u32Index  = 0;
    volatile uint32_t       u32TimeOut = FLASH_TIMEOUT_INIT;
    en_result_t             enResult  = Error;
    uint32_t                u32PrgTimer[15] = {0};
    volatile uint32_t       *pu32PrgTimerReg = (volatile uint32_t *)M0P_FLASH;

    if ((1  != u8FreqCfg) && (2  != u8FreqCfg) &&
            (4  != u8FreqCfg) && (6  != u8FreqCfg) &&
            (8  != u8FreqCfg) && (12 != u8FreqCfg))
    {
        enResult = ErrorInvalidParameter;
        return (enResult);
    }

    FLASH_BYPASS();
    M0P_FLASH->CR_f.DPSTB_EN = bDpstbEn;
    if (bDpstbEn != M0P_FLASH->CR_f.DPSTB_EN)
    {
        enResult = ErrorUninitialized;
        return (enResult);
    }

    /* flash时间参数配置值计算 */
    for (u32Index = 0; u32Index < 15; u32Index++)
    {
        u32PrgTimer[u32Index] = (u8FreqCfg * (pu32PcgTimer4M[u32Index] + 1u)) - 1u;
    }

    /* flash时间参数寄存器配置 */
    for (u32Index = 0; u32Index < 15; u32Index++)
    {
        if (u32Index < 8)
        {
            u32TimeOut = FLASH_TIMEOUT_INIT;
            while (pu32PrgTimerReg[u32Index]  != u32PrgTimer[u32Index])
            {
                if (u32TimeOut--)
                {
                    FLASH_BYPASS();
                    pu32PrgTimerReg[u32Index] = u32PrgTimer[u32Index];
                }
                else
                {
                    enResult = ErrorUninitialized;
                    return ErrorUninitialized;
                }
            }
        }
        else
        {
            u32TimeOut = FLASH_TIMEOUT_INIT;
            while (pu32PrgTimerReg[u32Index + 12u]  != u32PrgTimer[u32Index])
            {
                if (u32TimeOut--)
                {
                    FLASH_BYPASS();
                    pu32PrgTimerReg[u32Index + 12u] = u32PrgTimer[u32Index];
                }
                else
                {
                    enResult = ErrorUninitialized;
                    return ErrorUninitialized;
                }
            }
        }
    }

    enResult = Ok;
    return (enResult);
}


/**
 * @brief  Flash 连续字节编程,以字节方式向FLASH写入连续的数据
 * @param  [in] u32Addr: Flash目标首地址
 * @param  [in] pu8Data: 数据Buffer首地址
 * @param  [in] u32Len: 写入数据长度
 * @retval en_result_t:
 *              - Ok:                    写入成功.
 *              - ErrorInvalidParameter: FLASH地址无效
 *              - ErrorTimeout:          操作超时
 *              - Error:                 编程、校验失败
 *              - ErrorInvalidMode:      操作模式无效
 */
en_result_t Flash_Write8(uint32_t u32Addr, uint8_t pu8Data[], uint32_t u32Len)
{
    en_result_t             enResult = Error;
    volatile uint32_t       u32TimeOut = FLASH_TIMEOUT_PGM;
    uint32_t                u32Index = 0;

    if (FlashWriteMode != M0P_FLASH->CR_f.OP)
    {
        return ErrorInvalidMode;
    }

    if (FLASH_END_ADDR < (u32Addr + u32Len - 1))
    {
        enResult = ErrorInvalidParameter;
        return (enResult);
    }

    /* busy */
    u32TimeOut = FLASH_TIMEOUT_PGM;
    while (TRUE == M0P_FLASH->CR_f.BUSY)
    {
        if (0 == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    Flash_UnlockAll();
    /* write data byte */
    for (u32Index = 0; u32Index < u32Len; u32Index++)
    {
        *((volatile uint8_t *)u32Addr) = pu8Data[u32Index];

        /* busy */
        u32TimeOut = FLASH_TIMEOUT_PGM;
        while (TRUE == M0P_FLASH->CR_f.BUSY)
        {
            if (0 == u32TimeOut--)
            {
                Flash_LockAll();
                return ErrorTimeout;
            }
        }

        if (pu8Data[u32Index] != *((volatile uint8_t *)u32Addr))
        {
            Flash_LockAll();
            return Error;
        }
        u32Addr++;
    }

    Flash_LockAll();
    enResult = Ok;
    return (enResult);
}

/**
 * @brief  Flash 连续半字（16位方式）编程,以半字方式向FLASH写入连续的数据
 * @param  [in] u32Addr: Flash目标首地址
 * @param  [in] pu16Data: 数据Buffer首地址
 * @param  [in] u32Len: 写入数据长度
 * @retval en_result_t:
 *              - Ok:                    写入成功.
 *              - ErrorInvalidParameter: FLASH地址无效
 *              - ErrorTimeout:          操作超时
 *              - Error:                 编程、校验失败
 *              - ErrorInvalidMode:      操作模式无效
 */
en_result_t Flash_Write16(uint32_t u32Addr, uint16_t pu16Data[], uint32_t u32Len)
{
    en_result_t             enResult = Error;
    volatile uint32_t       u32TimeOut = FLASH_TIMEOUT_PGM;
    uint32_t                u32Index = 0;

    if (FlashWriteMode != M0P_FLASH->CR_f.OP)
    {
        return ErrorInvalidMode;
    }

    if (FLASH_END_ADDR < (u32Addr + u32Len - 1))
    {
        enResult = ErrorInvalidParameter;
        return (enResult);
    }

    /* busy */
    u32TimeOut = FLASH_TIMEOUT_PGM;
    while (TRUE == M0P_FLASH->CR_f.BUSY)
    {
        if (0 == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    Flash_UnlockAll();
    /* write data byte */
    for (u32Index = 0; u32Index < u32Len; u32Index++)
    {
        *((volatile uint16_t *)u32Addr) = pu16Data[u32Index];

        /* busy */
        u32TimeOut = FLASH_TIMEOUT_PGM;
        while (TRUE == M0P_FLASH->CR_f.BUSY)
        {
            if (0 == u32TimeOut--)
            {
                Flash_LockAll();
                return ErrorTimeout;
            }
        }

        if (pu16Data[u32Index] != *((volatile uint16_t *)u32Addr))
        {
            Flash_LockAll();
            return Error;
        }
        u32Addr += 2;
    }

    Flash_LockAll();
    enResult = Ok;
    return (enResult);
}


/**
 * @brief  Flash 连续字（32位方式）编程,以字方式向FLASH写入连续的数据
 * @param  [in] u32Addr: Flash目标首地址
 * @param  [in] pu32Data: 数据Buffer首地址
 * @param  [in] u32Len: 写入数据长度
 * @retval en_result_t:
 *              - Ok:                    写入成功.
 *              - ErrorInvalidParameter: FLASH地址无效
 *              - ErrorTimeout:          操作超时
 *              - Error:                 编程、校验失败
 *              - ErrorInvalidMode:      操作模式无效
 */
en_result_t Flash_Write32(uint32_t u32Addr, uint32_t pu32Data[], uint32_t u32Len)
{
    en_result_t             enResult = Error;
    volatile uint32_t       u32TimeOut = FLASH_TIMEOUT_PGM;
    uint32_t                u32Index = 0;

    if (FlashWriteMode != M0P_FLASH->CR_f.OP)
    {
        return ErrorInvalidMode;
    }

    if (FLASH_END_ADDR < (u32Addr + u32Len - 1))
    {
        enResult = ErrorInvalidParameter;
        return (enResult);
    }

    /* busy */
    u32TimeOut = FLASH_TIMEOUT_PGM;
    while (TRUE == M0P_FLASH->CR_f.BUSY)
    {
        if (0 == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    Flash_UnlockAll();
    /* write data byte */
    for (u32Index = 0; u32Index < u32Len; u32Index++)
    {
        *((volatile uint32_t *)u32Addr) = pu32Data[u32Index];

        /* busy */
        u32TimeOut = FLASH_TIMEOUT_PGM;
        while (TRUE == M0P_FLASH->CR_f.BUSY)
        {
            if (0 == u32TimeOut--)
            {
                Flash_LockAll();
                return ErrorTimeout;
            }
        }

        if (pu32Data[u32Index] != *((volatile uint32_t *)u32Addr))
        {
            Flash_LockAll();
            return Error;
        }
        u32Addr += 4;
    }

    Flash_LockAll();
    enResult = Ok;
    return (enResult);
}

/**
 * @brief  Flash 扇区擦除,对目标地址所在的FLASH 扇区进行擦除，擦除后该扇区FLASH数据为全0xFF
 * @param  [in] u32SectorAddr: 所擦除扇区内的地址
 * @retval en_result_t:
 *              - Ok:                    擦除成功.
 *              - ErrorInvalidParameter: FLASH地址无效
 *              - ErrorTimeout:          操作超时
 *              - ErrorInvalidMode:      操作模式无效
 */
en_result_t Flash_SectorErase(uint32_t u32SectorAddr)
{
    en_result_t             enResult = Ok;
    volatile uint32_t       u32TimeOut = FLASH_TIMEOUT_ERASE;

    if (FlashSectorEraseMode != M0P_FLASH->CR_f.OP)
    {
        return ErrorInvalidMode;
    }

    if (FLASH_END_ADDR < u32SectorAddr)
    {
        enResult = ErrorInvalidParameter;
        return (enResult);
    }

    /* busy */
    u32TimeOut = FLASH_TIMEOUT_ERASE;
    while (TRUE == M0P_FLASH->CR_f.BUSY)
    {
        if (0 == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    Flash_UnlockAll();
    /* write data */
    *((volatile uint32_t *)u32SectorAddr) = 0;

    /* busy */
    u32TimeOut = FLASH_TIMEOUT_ERASE;
    while (TRUE == M0P_FLASH->CR_f.BUSY)
    {
        if (0 == u32TimeOut--)
        {
            Flash_LockAll();
            return ErrorTimeout;
        }
    }
    Flash_LockAll();
    return (enResult);
}


/**
 * @brief  FLASH 操作模式配置
 * @param  [in] enFlashOpMode: 操作模式配置  @ref en_flash_op_mode_t
 * @retval en_result_t:
 *              - Ok:                    配置成功
 *              - Error:                 配置失败
 */
en_result_t Flash_OpModeConfig(en_flash_op_mode_t enFlashOpMode)
{
    en_result_t enResult = Error;

    if (enFlashOpMode != FlashContinueWriteMode)
    {
        FLASH_BYPASS();
        M0P_FLASH->CR_f.OP = enFlashOpMode;

        if (enFlashOpMode == M0P_FLASH->CR_f.OP)
        {
            enResult = Ok;
        }
    }
    else
    {
        FLASH_BYPASS();
        M0P_FLASH->CR_f.OP = FlashReadMode;
        FLASH_BYPASS();
        M0P_FLASH->CR_f.CONTP = TRUE;

        if ((FlashReadMode == M0P_FLASH->CR_f.OP) && (M0P_FLASH->CR_f.CONTP))
        {
            enResult = Ok;
        }
    }


    return enResult;
}


/**
 * @brief  FLASH 读等待周期设置
 * @param  [in] enWaitCycle: 插入FLASH读等待周期数枚举类型  @ref en_flash_waitcycle_t
 * @retval en_result_t:
 *              - Ok:                    配置成功
 *              - Error:                 配置失败
 */
en_result_t Flash_WaitCycle(en_flash_waitcycle_t enWaitCycle)
{
    en_result_t enResult = Error;

    FLASH_BYPASS();
    M0P_FLASH->CR_f.WAIT = enWaitCycle;

    if (enWaitCycle == M0P_FLASH->CR_f.WAIT)
    {
        enResult =  Ok;
    }

    return enResult;
}

/**
 * @brief  Flash 低功耗模式配置:该函数用于配置低功耗模式
 * @param  [in] bDpstbEn: 低功耗使能位:
 *                - TRUE:当系统进入DeepSleep模式，FLASH进入低功耗模式;
 *                - FALSE:当系统进入DeepSleep模式，FLASH不进入低功耗模式;
 * @retval en_result_t:
 *              - Ok:                    操作成功.
 *              - ErrorInvalidParameter: 参数无效
 *              - ErrorUninitialized:    初始化失败
 */

en_result_t Flash_EnDpstb(boolean_t bDpstbEn)
{
    en_result_t             enResult = Ok;
    volatile uint32_t       u32TimeOut = FLASH_TIMEOUT_ERASE;

    u32TimeOut = FLASH_TIMEOUT_ERASE;
    while (bDpstbEn != M0P_FLASH->CR_f.DPSTB_EN)
    {
        FLASH_BYPASS();
        M0P_FLASH->CR_f.DPSTB_EN = bDpstbEn;

        if (0 == u32TimeOut--)
        {
            enResult = ErrorUninitialized;
            return (enResult);
        }
    }

    enResult = Ok;
    return (enResult);
}


/**
 * @brief  FLASH 编程保护加锁
 * @retval en_result_t:
 *              - Ok:    加锁成功.
 *              - Error: 加锁失败
 */
en_result_t Flash_LockAll(void)
{
    en_result_t enResult = Error;

    FLASH_BYPASS();
    M0P_FLASH->SLOCK0 = FLASH_LOCK_ALL;
    FLASH_BYPASS();
    M0P_FLASH->SLOCK1 = FLASH_LOCK_ALL;
    FLASH_BYPASS();
    M0P_FLASH->SLOCK2 = FLASH_LOCK_ALL;
    FLASH_BYPASS();
    M0P_FLASH->SLOCK3 = FLASH_LOCK_ALL;

    if ((FLASH_LOCK_ALL == M0P_FLASH->SLOCK0) &&
            (FLASH_LOCK_ALL == M0P_FLASH->SLOCK1) &&
            (FLASH_LOCK_ALL == M0P_FLASH->SLOCK2) &&
            (FLASH_LOCK_ALL == M0P_FLASH->SLOCK3))
    {
        enResult = Ok;
    }

    return enResult;
}

/**
 * @brief  FLASH 编程保护解锁
 * @retval en_result_t:
 *              - Ok:    解锁成功.
 *              - Error: 解锁失败
 */
en_result_t Flash_UnlockAll(void)
{
    en_result_t enResult = Error;

    FLASH_BYPASS();
    M0P_FLASH->SLOCK0 = FLASH_UNLOCK_ALL;
    FLASH_BYPASS();
    M0P_FLASH->SLOCK1 = FLASH_UNLOCK_ALL;
    FLASH_BYPASS();
    M0P_FLASH->SLOCK2 = FLASH_UNLOCK_ALL;
    FLASH_BYPASS();
    M0P_FLASH->SLOCK3 = FLASH_UNLOCK_ALL;

    if ((FLASH_UNLOCK_ALL == M0P_FLASH->SLOCK0) &&
            (FLASH_UNLOCK_ALL == M0P_FLASH->SLOCK1) &&
            (FLASH_UNLOCK_ALL == M0P_FLASH->SLOCK2) &&
            (FLASH_UNLOCK_ALL == M0P_FLASH->SLOCK3))
    {
        enResult = Ok;
    }

    return enResult;
}


/**
 * @brief  FLASH LOCK 设置
 * @param  [in] enLock: 加锁使能位             @ref en_flash_lock_t
 * @param  [in] u32LockValue 32bits，对应bit=0:加锁，对应Sector不允许擦写；对应bit=1:解锁。
 * @note   加解锁范围Sector:[enLock*128 + i*4, enLock*128 + i*4+3]
 *         -i: 表示u32LockValue的bit位置，0~31;
 *         -enLock: 表示枚举编号(FlashLock[n]:n=0~3),并非枚举值;)
 *         例如:enLock = FlashLock1, u32LockValue = 0x00000005,
 *         则FLASH解锁范围为:[Sector128,Sector131]和[Sector136,Sector139]
 * @retval en_result_t:
 *              - Ok:    解锁成功.
 *              - Error: 解锁失败
 */

en_result_t Flash_LockSet(en_flash_lock_t enLock, uint32_t u32LockValue)
{
    en_result_t enResult = Error;

    FLASH_BYPASS();
    *((&M0P_FLASH->SLOCK0) + enLock) = u32LockValue;

    if (u32LockValue == *((&M0P_FLASH->SLOCK0) + enLock))
    {
        return enResult = Ok;
    }

    return enResult;
}


#if defined (__ARMCC_VERSION) && (__ARMCC_VERSION >= 6100100)

#pragma clang section text = "ramfunc"

/**
 * @brief   Flash 整片擦除
 * @note    该函数需要放到RAM中执行
 * @retval  en_result_t:
 *              - Ok: 执行成功
 *              - Error: 执行失败
*/
en_result_t Flash_Chip_Erase(void)
{
    en_result_t enResult = Ok;
    volatile uint32_t u32TimeOut = FLASH_TIMEOUT_PGM;

    while (FlashChipEraseMode != M0P_FLASH->CR_f.OP)
    {
        M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
        M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

        M0P_FLASH->CR_f.OP = FlashChipEraseMode;

        if (0u == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
    M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

    M0P_FLASH->SLOCK0 = 0xFFFFFFFFu;

    M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
    M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

    M0P_FLASH->SLOCK1 = 0xFFFFFFFFu;

    M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
    M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

    M0P_FLASH->SLOCK2 = 0xFFFFFFFFu;

    M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
    M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

    M0P_FLASH->SLOCK3 = 0xFFFFFFFFu;

    if ((0xFFFFFFFFu != M0P_FLASH->SLOCK0) || (0xFFFFFFFFu != M0P_FLASH->SLOCK1) || (0xFFFFFFFFu != M0P_FLASH->SLOCK2) || (0xFFFFFFFFu != M0P_FLASH->SLOCK3))
    {
        enResult = Error;
    }

    *((volatile unsigned int *)0x00000000) = 0;

    u32TimeOut = 0xFFFFFFu;
    while (TRUE == M0P_FLASH->CR_f.BUSY)
    {
        if (0u == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    enResult = Ok;
    return (enResult);
}


/**
 * @brief  Flash字节连续写 用于向FLASH写入1字节数据
 * @param [in]  u32Addr:    Flash地址
 * @param [in]  pu8Data[]:  数据buf
 * @param [in]  u32Len:   buf长度
 * @retval en_result_t:
 *              - Ok: 写入成功.
 *              - ErrorInvalidMode: 模式错误
 *              - ErrorTimeout: 操作超时
 */
en_result_t Flash_ContWrite_Byte(uint32_t u32Addr, uint8_t pu8Data[], uint32_t u32Len)
{
    en_result_t             enResult = Error;
    volatile uint32_t       u32TimeOut = 0xFFFFFF;
    uint32_t                u32Index = 0u;

    if ((FlashReadMode != M0P_FLASH->CR_f.OP) && (M0P_FLASH->CR_f.CONTP))
    {
        return ErrorInvalidMode;
    }

    if (FLASH_END_ADDR < (u32Addr + u32Len - 1u))
    {
        enResult = ErrorInvalidParameter;
        return (enResult);
    }

    /* busy? */
    u32TimeOut = 0xFFFFFF;
    while (M0P_FLASH->CR_f.BUSY)
    {
        if (0u == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    /* write data byte */
    for (u32Index = 0u; u32Index < u32Len; u32Index++)
    {
        *((volatile uint8_t *)u32Addr) = pu8Data[u32Index];
        /* busy? */
        u32TimeOut = 0xFFFFFF;
        while (M0P_FLASH->CR_f.BUSY)
        {
            if (0u == u32TimeOut--)
            {
                return ErrorTimeout;
            }
        }
        u32Addr++;
    }

    while (FlashWriteMode != M0P_FLASH->CR_f.OP)
    {
        M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
        M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

        M0P_FLASH->CR_f.OP = FlashWriteMode;
    }

    while (M0P_FLASH->CR_f.CONTP)
    {
        M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
        M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

        M0P_FLASH->CR_f.CONTP = FALSE;
    }

    /* busy? */
    u32TimeOut = 0xFFFFFF;
    while (M0P_FLASH->CR_f.BUSY)
    {
        if (0u == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    enResult = Ok;

    return (enResult);
}

/**
 * @brief  Flash半字连续写 用于向FLASH写入半字（2字节）数据
 * @param [in]  u32Addr:    Flash地址
 * @param [in]  pu16Data[]:  数据buf
 * @param [in]  u32Len:   buf长度
 * @retval en_result_t:
 *              - Ok: 写入成功
 *              - ErrorInvalidMode: 模式错误
 *              - ErrorTimeout: 操作超时
 */
en_result_t Flash_ContWrite_HalfWord(uint32_t u32Addr, uint16_t pu16Data[], uint32_t u32Len)
{
    en_result_t             enResult = Error;
    volatile uint32_t       u32TimeOut = 0xFFFFFFu;
    uint32_t                u32Index = 0u;

    if ((FlashReadMode != M0P_FLASH->CR_f.OP) && (M0P_FLASH->CR_f.CONTP))
    {
        return ErrorInvalidMode;
    }

    if (FLASH_END_ADDR < (u32Addr + u32Len - 1u))
    {
        enResult = ErrorInvalidParameter;
        return (enResult);
    }

    /* busy? */
    u32TimeOut = 0xFFFFFFu;
    while (M0P_FLASH->CR_f.BUSY)
    {
        if (0u == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    /* write data byte */
    for (u32Index = 0u; u32Index < u32Len; u32Index++)
    {
        *((volatile uint16_t *)u32Addr) = pu16Data[u32Index];
        /* busy? */
        u32TimeOut = 0xFFFFFFu;
        while (TRUE == M0P_FLASH->CR_f.BUSY)
        {
            if (0u == u32TimeOut--)
            {
                return ErrorTimeout;
            }
        }
        u32Addr += 2u;
    }

    while (FlashWriteMode != M0P_FLASH->CR_f.OP)
    {
        M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
        M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

        M0P_FLASH->CR_f.OP = FlashWriteMode;
    }

    while (M0P_FLASH->CR_f.CONTP)
    {
        M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
        M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

        M0P_FLASH->CR_f.CONTP = FALSE;
    }

    /* busy? */
    u32TimeOut = 0xFFFFFFu;
    while (M0P_FLASH->CR_f.BUSY)
    {
        if (0u == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    enResult = Ok;
    return (enResult);
}

/**
 * @brief  Flash字连续写 用于向FLASH写入1个字的数据
 * @param [in]  u32Addr:    Flash地址
 * @param [in]  pu32Data[]:  数据buf
 * @param [in]  u32Len:   buf长度
 * @retval en_result_t:
 *              - Ok: 写入成功
 *              - ErrorInvalidMode: 模式错误
 *              - ErrorTimeout: 操作超时
 */
en_result_t Flash_ContWrite_Word(uint32_t u32Addr, uint32_t pu32Data[], uint32_t u32Len)
{
    en_result_t             enResult = Error;
    volatile uint32_t       u32TimeOut = 0xFFFFFFu;
    uint32_t                u32Index = 0u;

    if ((FlashReadMode != M0P_FLASH->CR_f.OP) && (M0P_FLASH->CR_f.CONTP))
    {
        return ErrorInvalidMode;
    }

    if (FLASH_END_ADDR < (u32Addr + u32Len - 1u))
    {
        enResult = ErrorInvalidParameter;
        return (enResult);
    }

    /* busy? */
    u32TimeOut = 0xFFFFFFu;
    while (M0P_FLASH->CR_f.BUSY)
    {
        if (0u == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    /* write data byte */
    for (u32Index = 0u; u32Index < u32Len; u32Index++)
    {
        *((volatile uint32_t *)u32Addr) = pu32Data[u32Index];
        /* busy? */
        u32TimeOut = 0xFFFFFFu;
        while (TRUE == M0P_FLASH->CR_f.BUSY)
        {
            if (0u == u32TimeOut--)
            {
                return ErrorTimeout;
            }
        }

        u32Addr += 4u;
    }

    while (FlashWriteMode != M0P_FLASH->CR_f.OP)
    {
        M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
        M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

        M0P_FLASH->CR_f.OP = FlashWriteMode;
    }

    while (M0P_FLASH->CR_f.CONTP)
    {
        M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
        M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

        M0P_FLASH->CR_f.CONTP = FALSE;
    }

    /* busy? */
    u32TimeOut = 0xFFFFFFu;
    while (M0P_FLASH->CR_f.BUSY)
    {
        if (0u == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    enResult = Ok;
    return (enResult);
}

#pragma clang section

#elif defined(__ICCARM__)

__ramfunc   en_result_t Flash_Chip_Erase(void)
{
    en_result_t enResult = Ok;
    volatile uint32_t u32TimeOut = FLASH_TIMEOUT_PGM;

    while (FlashChipEraseMode != M0P_FLASH->CR_f.OP)
    {
        M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
        M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

        M0P_FLASH->CR_f.OP = FlashChipEraseMode;

        if (0u == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
    M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

    M0P_FLASH->SLOCK0 = 0xFFFFFFFFu;

    M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
    M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

    M0P_FLASH->SLOCK1 = 0xFFFFFFFFu;

    M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
    M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

    M0P_FLASH->SLOCK2 = 0xFFFFFFFFu;

    M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
    M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

    M0P_FLASH->SLOCK3 = 0xFFFFFFFFu;

    if ((0xFFFFFFFFu != M0P_FLASH->SLOCK0) || (0xFFFFFFFFu != M0P_FLASH->SLOCK1) || (0xFFFFFFFFu != M0P_FLASH->SLOCK2) || (0xFFFFFFFFu != M0P_FLASH->SLOCK3))
    {
        enResult = Error;
    }

    *((volatile unsigned int *)0x00000000) = 0;

    u32TimeOut = FLASH_TIMEOUT_PGM;
    while (TRUE == M0P_FLASH->CR_f.BUSY)
    {
        if (0u == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    enResult = Ok;
    return (enResult);
}

/**
 * @brief  Flash字节连续写 用于向FLASH写入1字节数据
 * @param [in]  u32Addr:    Flash地址
 * @param [in]  pu8Data[]:  数据buf
 * @param [in]  u32Len:   buf长度
 * @retval en_result_t:
 *              - Ok: 写入成功.
 *              - ErrorInvalidMode: 模式错误
 *              - ErrorTimeout: 操作超时
 */
__ramfunc en_result_t Flash_ContWrite_Byte(uint32_t u32Addr, uint8_t pu8Data[], uint32_t u32Len)
{
    en_result_t             enResult = Error;
    volatile uint32_t       u32TimeOut = 0xFFFFFFu;
    uint32_t                u32Index = 0u;

    if ((FlashReadMode != M0P_FLASH->CR_f.OP) && (M0P_FLASH->CR_f.CONTP))
    {
        return ErrorInvalidMode;
    }

    if (FLASH_END_ADDR < (u32Addr + u32Len - 1u))
    {
        enResult = ErrorInvalidParameter;
        return (enResult);
    }

    /* busy? */
    u32TimeOut = 0xFFFFFF;
    while (M0P_FLASH->CR_f.BUSY)
    {
        if (0u == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    /* write data byte */
    for (u32Index = 0u; u32Index < u32Len; u32Index++)
    {
        *((volatile uint8_t *)u32Addr) = pu8Data[u32Index];
        /* busy? */
        u32TimeOut = 0xFFFFFF;
        while (TRUE == M0P_FLASH->CR_f.BUSY)
        {
            if (0u == u32TimeOut--)
            {
                return ErrorTimeout;
            }
        }

        u32Addr++;
    }

    while (FlashWriteMode != M0P_FLASH->CR_f.OP)
    {
        M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
        M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

        M0P_FLASH->CR_f.OP = FlashWriteMode;
    }

    while (M0P_FLASH->CR_f.CONTP)
    {
        M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
        M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

        M0P_FLASH->CR_f.CONTP = FALSE;
    }

    /* busy? */
    u32TimeOut = 0xFFFFFF;
    while (M0P_FLASH->CR_f.BUSY)
    {
        if (0u == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    enResult = Ok;

    return (enResult);
}

/**
 * @brief  Flash半字连续写 用于向FLASH写入半字（2字节）数据
 * @param [in]  u32Addr:    Flash地址
 * @param [in]  pu16Data[]:  数据buf
 * @param [in]  u32Len:   buf长度
 * @retval en_result_t:
 *              - Ok: 写入成功
 *              - ErrorInvalidMode: 模式错误
 *              - ErrorTimeout: 操作超时
 */
__ramfunc en_result_t Flash_ContWrite_HalfWord(uint32_t u32Addr, uint16_t pu16Data[], uint32_t u32Len)
{
    en_result_t             enResult = Error;
    volatile uint32_t       u32TimeOut = 0xFFFFFF;
    uint32_t                u32Index = 0u;

    if ((FlashReadMode != M0P_FLASH->CR_f.OP) && (M0P_FLASH->CR_f.CONTP))
    {
        return ErrorInvalidMode;
    }

    if (FLASH_END_ADDR < (u32Addr + u32Len - 1u))
    {
        enResult = ErrorInvalidParameter;
        return (enResult);
    }

    /* busy? */
    u32TimeOut = 0xFFFFFF;
    while (M0P_FLASH->CR_f.BUSY)
    {
        if (0u == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    /* write data byte */
    for (u32Index = 0u; u32Index < u32Len; u32Index++)
    {
        *((volatile uint16_t *)u32Addr) = pu16Data[u32Index];
        /* busy? */
        u32TimeOut = 0xFFFFFF;
        while (TRUE == M0P_FLASH->CR_f.BUSY)
        {
            if (0u == u32TimeOut--)
            {
                return ErrorTimeout;
            }
        }

        u32Addr += 2u;
    }

    while (FlashWriteMode != M0P_FLASH->CR_f.OP)
    {
        M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
        M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

        M0P_FLASH->CR_f.OP = FlashWriteMode;
    }

    while (M0P_FLASH->CR_f.CONTP)
    {
        M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
        M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

        M0P_FLASH->CR_f.CONTP = FALSE;
    }

    /* busy? */
    u32TimeOut = 0xFFFFFF;
    while (M0P_FLASH->CR_f.BUSY)
    {
        if (0u == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    enResult = Ok;
    return (enResult);
}

/**
 * @brief  Flash字连续写 用于向FLASH写入1个字的数据
 * @param [in]  u32Addr:    Flash地址
 * @param [in]  pu32Data[]:  数据buf
 * @param [in]  u32Len:   buf长度
 * @retval en_result_t:
 *              - Ok: 写入成功
 *              - ErrorInvalidMode: 模式错误
 *              - ErrorTimeout: 操作超时
 */
__ramfunc en_result_t Flash_ContWrite_Word(uint32_t u32Addr, uint32_t pu32Data[], uint32_t u32Len)
{
    en_result_t             enResult = Error;
    volatile uint32_t       u32TimeOut = 0xFFFFFF;
    uint32_t                u32Index = 0u;

    if ((FlashReadMode != M0P_FLASH->CR_f.OP) && (M0P_FLASH->CR_f.CONTP))
    {
        return ErrorInvalidMode;
    }

    if (FLASH_END_ADDR < (u32Addr + u32Len - 1u))
    {
        enResult = ErrorInvalidParameter;
        return (enResult);
    }

    /* busy? */
    u32TimeOut = 0xFFFFFF;
    while (M0P_FLASH->CR_f.BUSY)
    {
        if (0u == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    /* write data byte */
    for (u32Index = 0u; u32Index < u32Len; u32Index++)
    {
        *((volatile uint32_t *)u32Addr) = pu32Data[u32Index];
        /* busy? */
        u32TimeOut = 0xFFFFFF;
        while (TRUE == M0P_FLASH->CR_f.BUSY)
        {
            if (0u == u32TimeOut--)
            {
                return ErrorTimeout;
            }
        }

        u32Addr += 4u;
    }

    while (FlashWriteMode != M0P_FLASH->CR_f.OP)
    {
        M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
        M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

        M0P_FLASH->CR_f.OP = FlashWriteMode;
    }

    while (M0P_FLASH->CR_f.CONTP)
    {
        M0P_FLASH->BYPASS = (uint32_t)0x5A5A;
        M0P_FLASH->BYPASS = (uint32_t)0xA5A5;

        M0P_FLASH->CR_f.CONTP = FALSE;
    }

    /* busy? */
    u32TimeOut = 0xFFFFFF;
    while (M0P_FLASH->CR_f.BUSY)
    {
        if (0u == u32TimeOut--)
        {
            return ErrorTimeout;
        }
    }

    enResult = Ok;
    return (enResult);
}

#endif







/**
 * @}
 */

/**
 * @}
 */

/**
* @}
*/

/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/
