/*!
 * @file
 * @brief This file defines public constants, types and functions for the fridge runner.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef FRIDGE_RUNNER_H
#define FRIDGE_RUNNER_H

#include <stdint.h>
#include <stdbool.h>
#include "SimpleFsm.h"
#include "Defrosting.h"
#include "CoolingCycle.h"

// 正常变化条件各环温段中最大个数
#define U8_NORMAL_DEFROST_CONDITION_MAX_NUMBER ((uint8_t)7)
// 首次化霜压机累计运行时间7小时
#define U16_FIRST_DEFROST_COMP_TOTAL_ON_TIME_MINUTES ((uint16_t)(7 * 60))
// 首次化霜冰箱运行8小时
#define U16_FIRST_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES ((uint16_t)(8 * 60))
// 速冻化霜压机累计运行时间7小时
#define U16_SUPER_FRZ_DEFROST_COMP_TOTAL_ON_TIME_MINUTES ((uint16_t)(7 * 60))
// 速冻化霜冰箱累计运行12小时
#define U16_SUPER_FRZ_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES ((uint16_t)(12 * 60))
// 冷冻传感器故障化霜压机累计运行12小时
#define U16_FRZ_SENSOR_ERROR_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES ((uint16_t)(12 * 60))
// 化霜传感器故障化霜压机累计运行8小时
#define U16_DEF_SENSOR_ERROR_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES ((uint16_t)(12 * 60))
// 门开关故障化霜压机累计运行12小时
#define U16_DOOR_SW_ERROR_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES ((uint16_t)(12 * 60))
// 化霜功能故障化霜压机累计运行12小时
#define U16_DEF_FUNCTION_ERROR_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES ((uint16_t)(12 * 60))
#define U16_ENERGY_FIRST_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES ((uint16_t)(36 * 60))
#define U16_ENERGY_MORMAL_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES ((uint16_t)(72 * 60))
#define U16_ENERGY_FORCE_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES ((uint16_t)(7 * 24 * 60))
// 过负载化霜压机累计运行4小时
#define U16_OVERLOAD_DEFROST_COMP_STILL_ON_TIME_MINUTES ((uint16_t)(4 * 60))
#define U16_OVERLOAD_DEFROST_COMP_STILL_LONG_ON_TIME_MINUTES ((uint16_t)(6 * 60))
#define U16_OVERLOAD_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES ((uint16_t)(12 * 60))
// 速冻第一次化霜速冻运行16小时
#define U16_TURBO_FRZ_DEFROST_COMP_TOTAL_ON_TIME_MINUTES ((uint16_t)(16 * 60))
// 过负载化霜压机累计开4小时
#define U16_OVERLOAD_DEFROST_REF_DAMPER_TOTAL_ON_TIME_MINUTES ((uint16_t)(4 * 60))

#define U16_ENTER_DEFORST_DELAY_TIME_MINUTES ((uint16_t)(30))

// 冷藏减霜模式电动阀保持冷藏状态60分钟
#define U16_REF_REDUCE_FROST_REF_VLAVE_TOTAL_ON_TIME_MINUTES ((uint16_t)(60))
#define U16_REF_REDUCE_FROST_REF_OVERCOOL_TIME_MINUTES ((uint16_t)(5))

// 离子发生器开启风扇累计运行时间
#define U16_FAN_ION_ENABLE_TOTAL_MINUTES ((uint16_t)(60))

// 离子发生器开启时间
#define U16_ION_ENABLE_TOTAL_CYCLES ((uint16_t)(5))
#define U16_REF_ION_CYCLE_SECOND ((uint16_t)(300))
#define U16_REF_ION_CYCLE_ONSECOND ((uint16_t)(30))
#define U16_REF_ION_CYCLE_OFFSECOND ((uint16_t)(270))
#define U16_FRZ_ION_CYCLE_SECOND ((uint16_t)(300))
#define U16_FRZ_ION_CYCLE_ONSECOND ((uint16_t)(30))
#define U16_FRZ_ION_CYCLE_OFFSECOND ((uint16_t)(270))


// 峰谷用电自动化霜时间
#define U16_DEEP_MUTE_DEFORST_MINUTES ((uint16_t)(16 * 60))
#define U16_PEEK_VALLEY_POWER_DEFORST_MINUTES ((uint16_t)(8 * 60))
#define FLOAT_DEFROST_RATING (float)2100
#define FLOAT_AC_VOLTAGE (float)3110
#define U16_COMP_POWER_OFFSET ((uint16_t)16)
#define U16_POWER_OFFSET ((uint16_t)6)
#define U16_PEEK_VALLEY_POWER_BAK_MINUTES ((uint16_t)(10))

enum
{
    eRunning_CoolingCycle = 0,
    eRunning_Defrosting
};
typedef uint8_t RunningState_t;

typedef struct
{
    uint16_t u16_FridgeTotalOnTimeMinutes; // 冰箱运行时间
    uint16_t u16_CompTotalOnTimeMinutes; // 温区制冷时间
    uint16_t u16_RefDoorTotalOpenTimeSecond; // 冷藏门开门总时间
    uint16_t u16_FrzVarDoorTotalOpenTimeSecond; // 冷冻、变温开门总时间
    uint16_t u16_AllDoorTotalOpenTimeSecond; // 开门总时间
    uint16_t u16_DefrostExitTemp;
} DefrostCondition_st;

typedef struct
{
    uint8_t u8_DefrostConditionNumber; // 各个环温段条件个数
    DefrostCondition_st ary_DefrostCondition[U8_NORMAL_DEFROST_CONDITION_MAX_NUMBER];
} DefrostNormalCondition_st;

// 化霜类型存储数据
typedef struct
{
    uint8_t u8_SavedFlag;
    uint8_t u8_SavedCount;
    EnterDefrostingState_t ary_DefrostTypeBuff[U8_DEFROST_TYPE_MAX_SAVE_NUMBER];
} SaveDefrostType_st;

void FridgeRunner_Init(void);
void FridgeRunner_Exit(void);
RunningState_t Get_RunningState(void);
EnterDefrostingState_t Get_EnterDefrostingState(void);
uint8_t Get_SavedDefrostType(uint8_t **p_defrost_type_buff);
uint16_t Get_FridgeTotalOnTimeMinute(void);
void Clear_FridgeTotalOnTimeMinute(void);
uint16_t Get_DeforstExitTemp(void);
void Set_DeforstExitTemp(uint16_t temp);
void Set_CoolingEntryMode(CoolingEntryMode_t mode);
void Set_TurboFreezeDefrostingAutoExitState(bool state);
void Set_EnergyConsumptionModeState(bool state);
bool Get_EnergyConsumptionModeState(void);
bool Get_CondensationModeState(void);
bool Get_RefFrostReduceMode(void);
void Set_RefFrostReduceMode(void);
void Exit_RefFrostReduceMode(void);
bool Is_RefFrostReduceExpired(void);
bool Get_ValveStayRefForstState(void);
bool Get_PowerOnDelayRefCoolingState(void);
bool Get_PowerOnDelayVarCoolingState(void);
void Force_EnterEnergyConsumptionModeState(void);
bool Get_EnergyConsumptionDeforstModeState(void);
uint8_t GetRefIonGeneratorRunProcess(void);
uint8_t GetFrzIonGeneratorRunProcess(void);
void Force_PeekValleyPowerDeforst(void);
void Clear_PeekValleyPowerDeforst(void);
uint32_t Get_FridgePowerOnTimeMinute(void);
uint16_t GetRefIonGeneratorTargetMinutes(void);
uint16_t GetFrzIonGeneratorTargetMinutes(void);
uint8_t Get_PeekValleyPowerDeforst(void);
void Clear_ValveStayRefForstState(void);
void Clear_EnergyHighLoadDoorOpenCloseCount(void);
bool Get_HighLoadMode(void);
void Clear_HighLoadDeforstMode(void);
uint16_t GetRefIonGeneratorProcessMinutes(void);
uint16_t GetFrzIonGeneratorProcessMinutes(void);
uint16_t Get_TotalPower(void);
uint32_t Get_ElectricEnergy(void);
void Update_ElectricEnergy(void);
uint8_t IsRefIonGeneratorWorking(void);
uint8_t IsFrzIonGeneratorWorking(void);
void StopAutoRefIonGeneratorWork(void);
void StopAutoFrzIonGeneratorWork(void);
uint8_t Get_PeekValleyDeforstFlag(void);
bool IsDefrostFrzOverLoad(void);
#endif
