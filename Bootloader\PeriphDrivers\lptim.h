/**
 ******************************************************************************
 * @file  lptim.h
 * @brief This file contains all the functions prototypes of the LPTIM driver
 *        library.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-03-15       MADS            First version
 @endverbatim
 ******************************************************************************
 * Copyright (C) 2023, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 ******************************************************************************
 */

#ifndef __LPTIM_H__
#define __LPTIM_H__

/* C binding of definitions if building with C++ compiler */
#ifdef __cplusplus
extern "C" {
#endif

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "ddl.h"

/**
 * @addtogroup HC32L186_DDL 驱动库
 * @{
 */

/**
 * @addtogroup DDL_LPTIM LPTIM模块驱动库
 * @{
 */

/*******************************************************************************
 * Global type definitions ('typedef')
 ******************************************************************************/
/**
 * @defgroup LPTim_Global_Types LPTIM 全局类型定义
 * @{
 */

/**
 * @brief  时钟预除频
 * @note   LPTIMx_CR  PRS     在定时器运行时不可以更改该值
 */
typedef enum
{
    LptimPrsDiv1   = 0, /*!< 1分频 */
    LptimPrsDiv2   = 1, /*!< 2分频 */
    LptimPrsDiv4   = 2, /*!< 4分频 */
    LptimPrsDiv8   = 3, /*!< 8分频 */
    LptimPrsDiv16  = 4, /*!< 16分频 */
    LptimPrsDiv32  = 5, /*!< 32分频 */
    LptimPrsDiv64  = 6, /*!< 64分频 */
    LptimPrsDiv256 = 7  /*!< 256分频 */
} en_lptim_prs_t;

/**
 * @brief  时钟选择
 * @note   LPTIMx_CR  TCK_SEL
 */
typedef enum
{
    LptimPclk = 0,
    LptimXtl  = 2,
    LptimRcl  = 3
} en_lptim_tcksel_t;

/**
 * @brief  GATE极性控制位
 * @note   LPTIMx_CR  GATE_P
 */
typedef enum
{
    LptimGatePLowStop  = 0, /*!< GATE低电平停止计数，高电平计数 */
    LptimGatePHighStop = 1  /*!< GATE高电平停止计数，低电平计数 */
} en_lptim_gatep_t;

/**
 * @brief  GATE使能控制位
 * @note   LPTIMx_CR  GATE
 */
typedef enum
{
    LptimGateDisable = 0,
    LptimGateEnable  = 1
} en_lptim_gate_t;

/**
 * @brief  TOG输出使能位
 * @note   LPTIMx_CR  TOG_EN
 */
typedef enum
{
    LptimTogEnLow  = 0, /*!< TOG,TOGN 同时输出0 */
    LptimTogEnHigh = 1  /*!< TOG,TOGN 输出相位相反的信号 */
} en_lptim_togen_t;

/**
 * @brief  CT计数器/定时器功能选择
 * @note   LPTIMx_CR  CT
 */
typedef enum
{
    LptimTimerFun = 0, /*!< 定时器功能，定时器使用TCK_SEL选择的时钟进行计数 */
    LptimCntFun   = 1  /*!< 计数器功能，计数器使用外部输入的下降沿进行计数，采样时钟使用TCK_SEL选择的时钟 */
} en_lptim_ct_t;

/**
 * @brief  定时器工作模式
 * @note   LPTIMx_CR  MD
 */
typedef enum
{
    LptimMode1 = 0, /*!< 模式1无重载16位计数器/定时器 */
    LptimMode2 = 1  /*!< 模式2自动重载16位计数器/定时器 */
} en_lptim_md_t;

/**
 * @brief  初始化配置的结构体
 */
typedef struct
{
    en_lptim_prs_t    enPrs;    /*!< 预除频 */
    en_lptim_tcksel_t enTcksel; /*!< 时钟选择 */
    en_lptim_gatep_t  enGatep;  /*!< GATE极性控制位 */
    en_lptim_gate_t   enGate;   /*!< GATE使能控制位 */
    en_lptim_togen_t  enTogen;  /*!< TOG输出使能位 */
    en_lptim_ct_t     enCt;     /*!< CT计数器/定时器功能选择 */
    en_lptim_md_t     enMd;     /*!< 定时器工作模式 */
    uint16_t          u16Arr;   /*!< 重载值设定 */
} stc_lptim_cfg_t;

/**
 * @}
 */

/*******************************************************************************
 * Global pre-processor symbols/macros ('#define')
 ******************************************************************************/

/*******************************************************************************
 * Global variable definitions ('extern')
 ******************************************************************************/

/*******************************************************************************
  Global function prototypes (definition in C source)
 ******************************************************************************/
/**
 * @addtogroup LPTIM_Global_Functions LPTIM全局函数定义
 * @{
 */
extern void        Lptim_ConfIt(M0P_LPTIMER_TypeDef *Lptimx, boolean_t NewStatus);
extern void        Lptim_Cmd(M0P_LPTIMER_TypeDef *Lptimx, boolean_t NewStatus);
extern boolean_t   Lptim_GetItStatus(M0P_LPTIMER_TypeDef *Lptimx);
extern void        Lptim_ClrItStatus(M0P_LPTIMER_TypeDef *Lptimx);
extern en_result_t Lptim_Init(M0P_LPTIMER_TypeDef *Lptimx, stc_lptim_cfg_t *InitStruct);

/**
 * @}
 */

/**
 * @}
 */

/**
 * @}
 */

#ifdef __cplusplus
}
#endif

#endif /* __LPTIM_H__ */
/******************************************************************************
 * EOF (not truncated)
 *****************************************************************************/
