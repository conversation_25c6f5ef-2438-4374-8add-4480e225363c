/*!
 * @file
 * @brief This file defines public constants, types and functions for the heartbeat led module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef __HEARTBEAT_LED_H__
#define __HEARTBEAT_LED_H__

#include <stdint.h>
#include <stdbool.h>
#include "Adpt_GPIO.h"

enum
{
    eBlinking_Normal = 0,
    eBlinking_ResetWdt,
    eBlinking_SpecialMode,
    eBlinking_Max
};
typedef uint8_t BlinkingState_t;

void Set_HeartbeatState(uint8_t blink_state);
void Update_HeartbeatLed(void);

#endif /* __HEARTBEAT_LED_H__ */