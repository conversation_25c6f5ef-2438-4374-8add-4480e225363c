#include "ParameterManager.h"
#include "Crc16_CCITT_FALSE.h"
#include "Driver_Flash.h"
#include "InverterUsart.h"
#include "FactoryMode.h"
#include "SystemManager.h"
#include "Sbus_Nfc.h"
#include "miio_api.h"
#include "syslog.h"

parameter_manager_st parameter_manager;
model_pid_st model_pid_maps[] = 
{
    {"bf52s", "25348", MACHINE_TYPE_FRENCH, MACHINE_CAPACITY_ICEMAKER | MACHINE_CAPACITY_MICROICE},
    {"bs52s", "25357", MACHINE_TYPE_CROSS,  MACHINE_CAPACITY_MICROICE},
    {"bf52s1", "29450", MACHINE_TYPE_FRENCH1, MACHINE_CAPACITY_ICEMAKER | MACHINE_CAPACITY_MICROICE},
    {"bs52s1", "29726", MACHINE_TYPE_CROSS_WITH_ICEMAKER, MACHINE_CAPACITY_ICEMAKER | MACHINE_CAPACITY_MICROICE | MACHINE_CAPACITY_SN},
};

static bool b_cmd_wifi = false;
static bool b_cmd_inverter = false;
static bool b_crc = false;
static parameter_fireware_st model_parameter;
static parameter_section_st psections[PARAMETER_SECTION_MAX];

static model_pid_st *SearchModelPid(uint8_t *model)
{
    uint8_t index;
    uint8_t num;

    num = sizeof(model_pid_maps) / sizeof(model_pid_st);

    for(index = 0; index < num; index++)
    {
        if(strcmp((const char *)model, (const char *)model_pid_maps[index].model) == 0)
        {
            return &model_pid_maps[index];
        }
    }
    return NULL;
}

static void ParameterHeaderTrans(parameter_fireware_st *pfh)
{
    uint8_t index;
    parameter_fireware_header_st *fh;

    pfh->magic = convert_to_bigendian32(pfh->magic);
    for(index = 0; index < PARAMETER_FIREWARE_MAX; index++)
    {
        fh = &pfh->fheader[index];
        fh->id = convert_to_bigendian32(fh->id);
        fh->version = convert_to_bigendian32(fh->version);
        fh->offset = convert_to_bigendian32(fh->offset);
        fh->length = convert_to_bigendian32(fh->length);
        fh->crc = convert_to_bigendian32(fh->crc);
    }
    pfh->len = convert_to_bigendian32(pfh->len);
    pfh->crc = convert_to_bigendian32(pfh->crc);
}

static bool ParameterSectionParse(uint8_t *data, uint32_t len)
{
    uint16_t value;
    uint32_t index = 0;
    parameter_section_st *ps;

    memset((void *)psections, 0, sizeof(psections));
    while(index + sizeof(uint16_t) <= len)
    {
        value = data[index] << 8 | data[index + 1];
        if(value >= PARAMETER_SECTION_MAX)
        {
            return false;
        }
        else
        {
            ps = &psections[value];
            ps->id = value;
            if(index + 4 > len)
            {
                return false;
            }
            ps->length = data[index + 2] << 8 | data[index + 3];
            if(ps->length < 3 || index + ps->length * sizeof(uint16_t) > len)
            {
                return false;
            }
            ps->data = data + index + 4;
            index = index + ps->length * sizeof(uint16_t);
            value = data[index - 2] << 8 | data[index - 1];
            if(value != PARAMETER_SECTION_END)
            {
                ps->data = NULL;
                return false;
            }
            
        }
    }
    return true;
}

static bool ParameterHeaderCheck(parameter_fireware_st *pf)
{
    uint16_t crc;
    uint8_t index;
    parameter_fireware_header_st *fh;

    if(pf->magic != PARAMETER_MAGIC)
    {
        return false;
    }

    crc = Cal_CRC_MultipleData(pf->data, pf->len);
    if(crc != pf->crc)
    {
        return false;
    }

    for(index = 0; index < PARAMETER_FIREWARE_MAX; index++)
    {
        fh = &pf->fheader[index];
        if(fh->id == FIREWARE_ADDR_NONE)
        {
            break;
        }
        crc = Cal_CRC_MultipleData(pf->data + fh->offset, fh->length);
        if(crc != fh->crc)
        {
            return false;
        }

        if(fh->id == FIREWARE_MAIN_ADDR)
        {
            ParameterSectionParse(pf->data + fh->offset, fh->length);
        }

        if(fh->id == MAINTENANCE_PARAMETER_ID)
        {
            Init_Maintenance(pf->data + fh->offset);
        }
    }
    return true;
}

static int8_t SetMainSn(sn_fireware_st *sf)
{
    if(WriteProductSn(sf->outsn, PRODUCT_SN_SIZE, true) == 0)
    {
        sf->complete = true;
    }
    return 0;
}

static int8_t GetMainSn(sn_fireware_st *sf)
{
    if(ReadProductSn(sf->insn, PRODUCT_SN_SIZE) < 0)
    {
        memset(sf->insn, 0, PRODUCT_SN_SIZE);
    }
    sf->complete = true;
    return 0;
}

static void SetModelResult(const char* pValue, char result)
{
    if(result > 0 && b_cmd_wifi == true)
    {
        parameter_manager.b_model_complete = true;
    }
    else
    {
        parameter_manager.modeltimer = 0;
    }
    b_cmd_wifi = false;

}

static void SetModelPidResult(const char* pValue, char result)
{
    if(result > 0 && b_cmd_wifi == true)
    {
        parameter_manager.b_pid_complete = true;
    }
    else
    {
        parameter_manager.modeltimer = 0;
    }
    b_cmd_wifi = false;
}

static void GetMacResult(const char* pValue, char result)
{
    static uint8_t error_count = 0;

    if(result > 0 && b_cmd_wifi == true)
    {
        memset(parameter_manager.mac, 0, FIREWARE_PROPERTY_MAC_LEN);
        strncpy((char *)parameter_manager.mac, pValue, FIREWARE_PROPERTY_MAC_LEN - 1);
        if(strlen((char *)parameter_manager.mac) == FIREWARE_PROPERTY_MAC_LEN - 1)
        {
            b_cmd_wifi = false;
            parameter_manager.b_mac_complete = true;
            SetNfcPropertyValue(NFC_PROPERTY_TYPE_MAC, parameter_manager.mac);
            return;
        }
        else if(error_count++ > WIFI_MODEL_RETRY_COUNT)
        {
            parameter_manager.b_mac_complete = true;
        }
    }
    parameter_manager.modeltimer = 0;
    b_cmd_wifi = false;
}

static void GetDidResult(const char* pValue, char result)
{
    static uint8_t error_count = 0;

    if(result > 0 && b_cmd_wifi == true)
    {
        memset(parameter_manager.did, 0, FIREWARE_PROPERTY_DID_LEN);
        strncpy((char *)parameter_manager.did, pValue, FIREWARE_PROPERTY_DID_LEN - 1);
        if(strlen((char *)parameter_manager.did) > 0 && strncmp((char *)parameter_manager.did, "error", 5) != 0)
        {
            b_cmd_wifi = false;
            parameter_manager.b_did_complete = true;
            SetNfcPropertyValue(NFC_PROPERTY_TYPE_DID, parameter_manager.did);
            return;
        }
        else if(error_count++ > WIFI_MODEL_RETRY_COUNT)
        {
            parameter_manager.b_did_complete = true;
        }
    }

    b_cmd_wifi = false;
    parameter_manager.modeltimer = 0;
}

static int8_t CancleWifiSnAsync(sn_fireware_st *sf)
{
    b_cmd_wifi = false;
    return 0;
}

static void GetWifiSnResult(const char* pValue, char result)
{
    if(result > 0 && b_cmd_wifi == true)
    {
        if(CheckSnFormat((uint8_t *)pValue, PRODUCT_SN_SIZE) == true)
        {
            strncpy((char *)parameter_manager.sn_firewares[SN_FIREWARE_WIFI].insn, pValue, PRODUCT_SN_SIZE);
        }
        else
        {
            memset(parameter_manager.sn_firewares[SN_FIREWARE_WIFI].insn, 0, PRODUCT_SN_SIZE);
        }
        parameter_manager.sn_firewares[SN_FIREWARE_WIFI].complete = true;
    }
    else
    {
        b_cmd_wifi = false;
    }
}

static void SetWifiSnResult(const char* pValue, char result)
{
    if(result > 0 && b_cmd_wifi == true)
    {
        parameter_manager.sn_firewares[SN_FIREWARE_WIFI].complete = true;
    }
    else
    {
        b_cmd_wifi = false;
    }
}

static int8_t SetWifiSn(sn_fireware_st *sf)
{
    if(b_cmd_wifi == false)
    {
        if(execute_wifi_cmd_async(WIFI_CMD_SN, SetWifiSnResult) == 0)
        {
            b_cmd_wifi = true;
        }
    }
    return 0;
}

static int8_t GetWifiSn(sn_fireware_st *sf)
{
    if(b_cmd_wifi == false)
    {
        if(execute_wifi_cmd_async(WIFI_CMD_GET_SN, GetWifiSnResult) == 0)
        {
            b_cmd_wifi = true;
        }
    }
    return 0;
}

static void CancleFirewareAsync(void)
{
    sn_fireware_e index;
    sn_fireware_st *sf;

    parameter_manager.complete = false;
    for(index = SN_FIREWARE_MAIN; index < SN_FIREWARE_MAX; index++)
    {
        sf = &parameter_manager.sn_firewares[index];
        if(sf->cancle_fireware_async != NULL)
        {
            sf->cancle_fireware_async(sf);
        }
        sf->complete = false;
    }
}

#ifndef USER_MODEL
static void ParameterManagerHandShake(void)
{
    sn_fireware_e index;
    sn_fireware_st *sf;

    parameter_manager.timercount++;
    parameter_manager.complete = true;
    for(index = SN_FIREWARE_MAIN; index < SN_FIREWARE_MAX; index++)
    {
        sf = &parameter_manager.sn_firewares[index];
        sf->get_sn(sf);
        if(sf->complete == false)
        {
            parameter_manager.complete = false;
        }
    }

    if(parameter_manager.complete ||
       parameter_manager.timercount > HANDSHAKE_TIMEOUT_100MS)
    {
        parameter_manager.timercount = 0;
        for(index = SN_FIREWARE_MAIN; index < SN_FIREWARE_MAX; index++)
        {
            sf = &parameter_manager.sn_firewares[index];
            if(sf->insn[0] != 0)
            {
                if(parameter_manager.latest_sn[0] == 0)
                {
                    memcpy(parameter_manager.latest_sn, sf->insn, PRODUCT_SN_SIZE);
                }
                else
                {
                    if(memcmp(parameter_manager.latest_sn, sf->insn, PRODUCT_SN_SIZE) != 0)
                    {
                        memset(parameter_manager.latest_sn, 0, PRODUCT_SN_SIZE);
                        parameter_manager.fcode = index << 8 | PARAMETER_MANAGER_SN_CONFLICT_ERROR;
                        parameter_manager.state = PARAMETER_MANAGER_STATE_FAULT;
                        goto out;
                    }
                }
            }
        }

        if(parameter_manager.latest_sn[0] == 0)
        {
            parameter_manager.fcode = PARAMETER_MANAGER_SN_INVAILD_ERROR;
            parameter_manager.state = PARAMETER_MANAGER_STATE_FAULT;
            goto out;
        }
        parameter_manager.state = PARAMETER_MANAGER_STATE_SYNC;
        goto out;
    }
    return;
out:
    CancleFirewareAsync();
}

static void ParameterManagerSync(void)
{
    sn_fireware_e index;
    sn_fireware_st *sf;

    parameter_manager.timercount++;
    parameter_manager.complete = true;
    if(parameter_manager.b_sn_force)
    {
        for(index = SN_FIREWARE_MAIN; index < SN_FIREWARE_MAX; index++)
        {
            sf = &parameter_manager.sn_firewares[index];
            memcpy(sf->outsn, parameter_manager.latest_sn, PRODUCT_SN_SIZE);
            sf->set_sn(sf);
            if(sf->complete == false)
            {
                parameter_manager.complete = false;
            }
        }
    }
    else
    {
        for(index = SN_FIREWARE_MAIN; index < SN_FIREWARE_MAX; index++)
        {
            sf = &parameter_manager.sn_firewares[index];
            if(sf->insn[0] == 0)
            {
                memcpy(sf->outsn, parameter_manager.latest_sn, PRODUCT_SN_SIZE);
                sf->set_sn(sf);
                if(sf->complete == false)
                {
                    parameter_manager.complete = false;
                }
            }
        }
    }

    if(parameter_manager.complete ||
       parameter_manager.timercount > SYNC_TIMEOUT_100MS)
    {
        parameter_manager.timercount = 0;
        if(parameter_manager.match == false || parameter_manager.b_sn_force == false)
        {
            parameter_manager.b_sn_force = false;
            CancleFirewareAsync();
            if(parameter_manager.latest_sn[0] == 0)
            {
                parameter_manager.fcode = PARAMETER_MANAGER_SN_INVAILD_ERROR;
                parameter_manager.state = PARAMETER_MANAGER_STATE_FAULT;
            }
            else
            {
                parameter_manager.fcode = PARAMETER_MANAGER_SN_NONE;
                parameter_manager.state = PARAMETER_MANAGER_STATE_PROBE;
            }
        }
        else
        {
            err("modify the sn reboot\n");
            while(1);
        }
    }

}

void ParameterSnUpdate(void)
{
    sn_fireware_e index;
    sn_fireware_st *sf;

    if(ReadProductSn(parameter_manager.latest_sn, PRODUCT_SN_SIZE) < 0)
    {
        memset(parameter_manager.latest_sn, 0, PRODUCT_SN_SIZE);
    }
    parameter_manager.state = PARAMETER_MANAGER_STATE_SYNC;
    for(index = SN_FIREWARE_MAIN; index < SN_FIREWARE_MAX; index++)
    {
        sf = &parameter_manager.sn_firewares[index];
        sf->complete = false;
    }
    CancleFirewareAsync();
    parameter_manager.timercount = 0;
    parameter_manager.b_sn_force = true;
}
#else
static void ParameterManagerHandShake(void)
{
     parameter_manager.state = PARAMETER_MANAGER_STATE_SYNC;
     return;
}

static void ParameterManagerSync(void)
{
    sn_fireware_e index;
    sn_fireware_st *sf;

    parameter_manager.timercount++;
    parameter_manager.complete = true;
    ReadProductSn(parameter_manager.latest_sn, PRODUCT_SN_SIZE);
    if(parameter_manager.latest_sn[0] == 0)
    {
        parameter_manager.timercount = 0;
        CancleFirewareAsync();
        parameter_manager.fcode = PARAMETER_MANAGER_SN_NONE;
        parameter_manager.state = PARAMETER_MANAGER_STATE_PROBE;
        return;   
    }

    for(index = SN_FIREWARE_MAIN; index < SN_FIREWARE_MAX; index++)
    {
        sf = &parameter_manager.sn_firewares[index];
        if(sf->insn[0] == 0)
        {
            memcpy(sf->outsn, parameter_manager.latest_sn, PRODUCT_SN_SIZE);
            sf->set_sn(sf);
            if(sf->complete == false)
            {
                parameter_manager.complete = false;
            }
        }
    }

    if(parameter_manager.complete ||
       parameter_manager.timercount > SYNC_TIMEOUT_100MS)
    {
        parameter_manager.timercount = 0;
        CancleFirewareAsync();
        parameter_manager.fcode = PARAMETER_MANAGER_SN_NONE;
        parameter_manager.state = PARAMETER_MANAGER_STATE_PROBE;
    }
}

void ParameterSnUpdate(void)
{

}

#endif

static void ParameterManagerProbe(void)
{
    uint8_t model[PRODUCT_MODEL_BYTES + 1] = {0};
    uint8_t index;
    uint8_t id;
    uint8_t num;

    if(parameter_manager.match)
    {
        parameter_manager.state = PARAMETER_MANAGER_STATE_PARAM;
        return;
    }
    
    parameter_manager.pm = NULL;
    ReadProductModel(model, PRODUCT_MODEL_BYTES);
    for(index = 0; model_parameters[index] != NULL; index++)
    {
        for(id = 0; id < PARAMETER_ID_MAX; id++)
        {
            if(strcmp((const char *)(model_parameters[index]->hw_id_str[id]), (const char *)model) == 0 &&
               ParameterHeaderCheck(model_parameters[index]) == true)
            {
                memcpy(&model_parameter, model_parameters[index], sizeof(parameter_fireware_st));
                parameter_manager.size = model_parameter.len;
                ParameterHeaderTrans(&model_parameter);
                parameter_manager.data_parameter = model_parameters[index];
                parameter_manager.device_parameter = &model_parameter;
                parameter_manager.match = true;
                parameter_manager.pindex = 0;
                parameter_manager.state = PARAMETER_MANAGER_STATE_UPLOAD;
                parameter_manager.pm = SearchModelPid(model);
                ReadProductUserModel(parameter_manager.model, FIREWARE_PROPERTY_MODEL_LEN);
                SetNfcPropertyValue(NFC_PROPERTY_TYPE_MODEL, parameter_manager.model);
            }
        }
    }

    if(parameter_manager.match == false)
    {
        parameter_manager.fcode = PARAMETER_MANAGER_SN_MATCH_ERROR;
        parameter_manager.state = PARAMETER_MANAGER_STATE_FAULT;
    }

}

static void ParameterManagerParam(void)
{
    sn_fireware_e index;
    sn_fireware_st *sf;

    if(parameter_manager.pindex >= SN_FIREWARE_MAX)
    {
        parameter_manager.timercount = 0;
        CancleFirewareAsync();
        parameter_manager.pindex = 0;
        for(index = SN_FIREWARE_MAIN; index < SN_FIREWARE_MAX; index++)
        {
            sf = &parameter_manager.sn_firewares[index];
            sf->state = PARAMETER_MANAGER_PARAM_CHECKCRC;
        }
        parameter_manager.state = PARAMETER_MANAGER_STATE_CHECK;
        return;
    }

    parameter_manager.timercount++;
    sf = &parameter_manager.sn_firewares[parameter_manager.pindex];
    if(sf->state == PARAMETER_MANAGER_PARAM_CHECKCRC)
    {
        if(sf->check_param_crc)
        {
            sf->check_param_crc(sf);
        }
        else
        {
            parameter_manager.pindex++;
            return;
        }

        if(sf->complete == true && b_crc == false)
        {
            sf->complete = false;
            sf->cancle_fireware_async(sf);
            sf->state  = PARAMETER_MANAGER_PARAM_WRITEPARAM;
        }
        else if(sf->complete == true && b_crc == true)
        {
            sf->complete = false;
            sf->cancle_fireware_async(sf);
            sf->state  = PARAMETER_MANAGER_PARAM_READCRC;
        }
    }
    else if(sf->state == PARAMETER_MANAGER_PARAM_READCRC)
    {
        if(sf->read_param_crc)
        {
            sf->read_param_crc(sf);
        }
        else
        {
            parameter_manager.pindex++;
            return;
        }
        if(sf->complete == true && b_crc == false)
        {
            sf->complete = false;
            sf->cancle_fireware_async(sf);
            sf->state  = PARAMETER_MANAGER_PARAM_WRITEPARAM;
        }
        else if(sf->complete == true && b_crc == true)
        {
            sf->complete = false;
            sf->cancle_fireware_async(sf);
            sf->b_param_error = false;
            parameter_manager.pindex++;
            //sf->state  = PARAMETER_MANAGER_PARAM_READPARAM;
            return;
        }
    }
    else if(sf->state == PARAMETER_MANAGER_PARAM_READPARAM)
    {
        if(sf->complete == true && sf->packno < 0xFF)
        {
            SaveEeParam(parameter_manager.data, sf->packno - 1);
        }

        if(sf->read_param)
        {
            if(sf->packno == 0xFF && sf->complete == true)
            {
                sf->complete = false;
                sf->cancle_fireware_async(sf);
                sf->packno = 0;
                parameter_manager.pindex++;
                return;
            }
            else
            {
                sf->read_param(sf);
            }
        }
        else
        {
            parameter_manager.pindex++;
            return;
        }
    }
    else if(sf->state == PARAMETER_MANAGER_PARAM_WRITEPARAM)
    {
        if(sf->write_param)
        {
            if(sf->packno == 0xFF && sf->complete == true)
            {
                sf->complete = false;
                sf->cancle_fireware_async(sf);
                sf->packno = 0;
                sf->state = PARAMETER_MANAGER_PARAM_RECHECKCRC;
            }
            else
            {
                sf->write_param(sf);
            }
        }
        else
        {
            parameter_manager.pindex++;
            return;
        }
    }
    else if(sf->state == PARAMETER_MANAGER_PARAM_RECHECKCRC)
    {
        if(sf->check_param_crc)
        {
            sf->check_param_crc(sf);
        }
        else
        {
            parameter_manager.pindex++;
            return;
        }

        if(sf->complete == true && b_crc == true)
        {
            sf->complete = false;
            sf->cancle_fireware_async(sf);
            sf->b_param_error = false;
            parameter_manager.pindex++;
            return;
        }
        else if(sf->complete == true && b_crc == false)
        {
            sf->complete = false;
            sf->cancle_fireware_async(sf);
            sf->b_param_error = true;
            parameter_manager.pindex++;
            return;
        }
    }

    if(parameter_manager.timercount > SYNC_TIMEOUT_100MS)
    {
        parameter_manager.timercount = 0;
        sf->complete = false;
        sf->cancle_fireware_async(sf);
        sf->b_param_error = true;
        parameter_manager.pindex++;
    }
}

static void ParameterManagerCheck(void)
{
    bool b_sn_error = false;
    sn_fireware_e index;
    sn_fireware_st *sf;
    FridgeState_t fstate;
    static bool b_model_check = false;
    static bool b_param_check = false;

    fstate = Get_FridgeState();
    if(parameter_manager.modeltimer < MODEL_TIMEOUT_100MS)
    {
        parameter_manager.modeltimer++;
    }
    else if((fstate != eFridge_Factory || Get_WifiFactoryModeResult()) && b_param_check == false)
    {
        b_model_check = true;
        if(b_cmd_wifi == false &&
           parameter_manager.b_model_complete == false)
        {
            if(execute_wifi_cmd_async(WIFI_CMD_SET_MODEL, SetModelResult) == 0)
            {
                b_cmd_wifi = true;
            }
        }

        if(b_cmd_wifi == false &&
           parameter_manager.b_model_complete == true &&
           parameter_manager.b_pid_complete == false)
        {
            if(execute_wifi_cmd_async(WIFI_CMD_BLE_CONF, SetModelPidResult) == 0)
            {
                b_cmd_wifi = true;
            }
        }

        if(b_cmd_wifi == false &&
           parameter_manager.b_model_complete == true &&
           parameter_manager.b_pid_complete == true &&
           parameter_manager.b_mac_complete == false)
        {
            if(execute_wifi_cmd_async(WIFI_CMD_MAC, GetMacResult) == 0)
            {
                b_cmd_wifi = true;
            }
        }

        if(b_cmd_wifi == false &&
           parameter_manager.b_model_complete == true &&
           parameter_manager.b_pid_complete == true &&
           parameter_manager.b_mac_complete == true &&
           parameter_manager.b_did_complete == false)
        {
            if(execute_wifi_cmd_async(WIFI_CMD_GETDID, GetDidResult) == 0)
            {
                b_cmd_wifi = true;
            }
        }

        if(parameter_manager.b_did_complete == true)
        {
            parameter_manager.timercount = 0;
            b_model_check = false;
        }
        else if(parameter_manager.timercount++ > CHECK_TIMEOUT_100MS)
        {
            parameter_manager.modeltimer = 0;
            parameter_manager.timercount = 0;
            b_cmd_wifi = false;
            b_model_check = false;
        }
    }

    if(parameter_manager.checktimer < CHECK_GAP_100MS)
    {
        parameter_manager.checktimer++;
    }
    else if(parameter_manager.latest_sn[0] == 0 && 
            b_model_check == false)
    {
        parameter_manager.checktimer = 0;
        parameter_manager.timercount = 0;
        ReadProductSn(parameter_manager.latest_sn, PRODUCT_SN_SIZE);
        if(parameter_manager.param_check_cycle++ > PARAM_CHECK_CYCLE)
        {
            parameter_manager.param_check_cycle = 0;
            parameter_manager.state = PARAMETER_MANAGER_STATE_PARAM;
        }       
    }
    else if(b_model_check == false)
    {
        b_param_check = true;
        parameter_manager.timercount++;
        for(index = SN_FIREWARE_MAIN; index < SN_FIREWARE_MAX; index++)
        {
            sf = &parameter_manager.sn_firewares[index];
            sf->get_sn(sf);
            if(sf->complete == true)
            {
                if(memcmp(sf->insn, parameter_manager.latest_sn, PRODUCT_SN_SIZE) != 0)
                {
                    sf->b_sn_error = true;
                }
                else
                {
                    sf->b_sn_error = false;
                }
            }
        }

        if(parameter_manager.timercount > CHECK_TIMEOUT_100MS)
        {
            b_param_check = false;
            parameter_manager.timercount = 0;
            parameter_manager.checktimer = 0;
            for(index = SN_FIREWARE_MAIN; index < SN_FIREWARE_MAX; index++)
            {
                sf = &parameter_manager.sn_firewares[index];
                if(sf->complete == false || sf->b_sn_error == true)
                {
                    memset(sf->insn, 0, PRODUCT_SN_SIZE);
                    sf->b_sn_error = true;
                    b_sn_error = true;
                }
            }
            CancleFirewareAsync();
            if(b_sn_error == false)
            {
                parameter_manager.sn_sync_cycle = 0;
            }

            if(b_sn_error && parameter_manager.sn_sync_cycle++ >= SN_ERROR_CYCLE)
            {
                parameter_manager.sn_sync_cycle = 0;
                parameter_manager.param_check_cycle = 0;
                parameter_manager.state = PARAMETER_MANAGER_STATE_SYNC;
            }
            else if(parameter_manager.param_check_cycle++ > PARAM_CHECK_CYCLE)
            {
                parameter_manager.param_check_cycle = 0;
                parameter_manager.state = PARAMETER_MANAGER_STATE_PARAM;
            }
        }
    }
}

static void ParameterManagerFault(void)
{
    if(parameter_manager.timercount++ > FAULT_TIMEOUT_100MS)
    {
        parameter_manager.timercount = 0;
        parameter_manager.state = PARAMETER_MANAGER_STATE_HANDSHAKE;
    }
}

static void ParameterManagerUpload(void)
{
    FridgeState_t fstate;

    parameter_manager.timercount++;
    fstate = Get_FridgeState();

    if(fstate != eFridge_Factory || Get_WifiFactoryModeResult())
    {
        if(b_cmd_wifi == false && parameter_manager.b_model_complete == false)
        {
            if(execute_wifi_cmd_async(WIFI_CMD_SET_MODEL, SetModelResult) == 0)
            {
                b_cmd_wifi = true;
            }
        }

        if(b_cmd_wifi == false && parameter_manager.b_model_complete == true)
        {
            if(execute_wifi_cmd_async(WIFI_CMD_BLE_CONF, SetModelPidResult) == 0)
            {
                b_cmd_wifi = true;
            }
        }
    }

    if(parameter_manager.b_pid_complete || parameter_manager.timercount > SYNC_TIMEOUT_100MS)
    {
        b_cmd_wifi = false;
        parameter_manager.timercount = 0;
        parameter_manager.modeltimer = 0;
        parameter_manager.state = PARAMETER_MANAGER_STATE_PARAM;
    }
}

void ParameterManagerRun(void)
{
    if(parameter_manager.state >= PARAMETER_MANAGER_STATE_HANDSHAKE
       && parameter_manager.state < PARAMETER_MANAGER_STATE_MAX)
    {
        if(parameter_manager.statefuncs[parameter_manager.state] != NULL)
        {
            parameter_manager.statefuncs[parameter_manager.state]();
        }
    }
}

bool IsParameterManagerReady(void)
{
    return parameter_manager.match;
}

bool IsParameterManagerUploadReady(void)
{
    return false;
#if 0
    return (parameter_manager.match && parameter_manager.b_model_complete &&
            parameter_manager.b_pid_complete && parameter_manager.b_mac_complete &&
            parameter_manager.b_did_complete);
#endif
}

bool IsParameterManagerFault(void)
{
    return (parameter_manager.fcode != PARAMETER_MANAGER_SN_NONE &&
            parameter_manager.state == PARAMETER_MANAGER_STATE_FAULT);
}

void GetParameterManagerFault(uint8_t *fault, uint8_t size)
{
    uint8_t index;

    if(size < PRODUCT_SN_SIZE)
    {
        return;
    }

    switch(parameter_manager.fcode & 0xFF)
    {
    case PARAMETER_MANAGER_SN_CONFLICT_ERROR:
        index = (parameter_manager.fcode >> 8) & 0xFF;
        sprintf((char *)fault, "SN:CONFILCT%d", index);
        break;
    case PARAMETER_MANAGER_SN_INVAILD_ERROR:
        strcpy((char *)fault, "SN:INVAILD");
        break;
    case PARAMETER_MANAGER_SN_MATCH_ERROR:
        strcpy((char *)fault, "SN:NOT MATCH");
        break;
    default:
        strcpy((char *)fault, "SN:UNKOWN");
        break;
    }
    return;
}

void ParameterManagerInit(void)
{
    sn_fireware_st *sf;

    memset(&parameter_manager, 0, sizeof(parameter_manager_st));
    sf = &parameter_manager.sn_firewares[SN_FIREWARE_MAIN];
    sf->set_sn = SetMainSn;
    sf->get_sn = GetMainSn;
    sf = &parameter_manager.sn_firewares[SN_FIREWARE_WIFI];
    sf->set_sn = SetWifiSn;
    sf->get_sn = GetWifiSn;
    sf->cancle_fireware_async = CancleWifiSnAsync;
    parameter_manager.statefuncs[PARAMETER_MANAGER_STATE_HANDSHAKE] = ParameterManagerHandShake;
    parameter_manager.statefuncs[PARAMETER_MANAGER_STATE_SYNC] = ParameterManagerSync;
    parameter_manager.statefuncs[PARAMETER_MANAGER_STATE_PROBE] = ParameterManagerProbe;
    parameter_manager.statefuncs[PARAMETER_MANAGER_STATE_UPLOAD] = ParameterManagerUpload;
    parameter_manager.statefuncs[PARAMETER_MANAGER_STATE_PARAM] = ParameterManagerParam;
    parameter_manager.statefuncs[PARAMETER_MANAGER_STATE_CHECK] = ParameterManagerCheck;
    parameter_manager.statefuncs[PARAMETER_MANAGER_STATE_FAULT] = ParameterManagerFault;
}

uint32_t GetMachineCapacity(void)
{
    if(NULL == parameter_manager.pm)
    {
        return MACHINE_CAPACITY_NORMAL;
    }
    return parameter_manager.pm->capacity;
}

uint8_t GetMachineType(void)
{
    if(NULL == parameter_manager.pm)
    {
        return MACHINE_TYPE_UNKOWN;
    }
    return parameter_manager.pm->mtype;
}

uint8_t *GetMachinePid(void)
{
#ifdef BLE_PID
    return (uint8_t *)BLE_PID;
#else
    if(NULL == parameter_manager.pm)
    {
        return NULL;
    }
    return parameter_manager.pm->pid;
#endif
}

void GetParameterSn(sn_fireware_e index, uint8_t *sn, uint8_t size)
{
    uint8_t *fsn;

    if(index >= SN_FIREWARE_MAX || size < PRODUCT_SN_SIZE)
    {
       strcpy((char *)sn, "SN:ERROR");
       return;
    }
    fsn = parameter_manager.sn_firewares[index].insn;
    if(CheckSnFormat(fsn, PRODUCT_SN_SIZE))
    {
        memcpy(sn, fsn, PRODUCT_SN_SIZE);
    }
    else
    {
        strcpy((char *)sn, "SN:INVAILD");
    }
}

uint16_t GetParameterSnFaultCode(void)
{
    sn_fireware_e index;
    sn_fireware_st *sf;
    uint16_t fcode;

    for(index = SN_FIREWARE_MAIN; index < SN_FIREWARE_MAX; index++)
    {
        sf = &parameter_manager.sn_firewares[index];
        if(sf->b_sn_error)
        {
            fcode |= 1 << index;
        }
    }

    if(IsParameterManagerFault())
    {
        fcode |= 1 << 15;
    }
    return fcode;
}

uint16_t GetParameterFaultCode(void)
{
    sn_fireware_e index;
    sn_fireware_st *sf;
    uint16_t fcode;

    for(index = SN_FIREWARE_MAIN; index < SN_FIREWARE_MAX; index++)
    {
        sf = &parameter_manager.sn_firewares[index];
        if(sf->b_param_error)
        {
            fcode |= 1 << index;
        }
    }

    return fcode;
}

parameter_section_st *GetParameterSection(parameter_section_e sid)
{
    if(sid >= PARAMETER_SECTION_MAX || psections[sid].data == NULL)
    {
        return NULL;
    }

    return &psections[sid];
}

