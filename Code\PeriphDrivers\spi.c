/**
 *******************************************************************************
 * @file  spi.c
 * @brief This file provides - functions to manage the SPI.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-11-27       MADS            First version
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2023, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 *******************************************************************************
 */

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "spi.h"


/**
 * @addtogroup HC32L186_DDL 驱动库
 * @{
 */

/**
 * @defgroup DDL_SPI SPI模块驱动库
 * @brief SPI Driver Library SPI模块驱动库
 * @{
 */

/*******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/*******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/

#define IS_VALID_STAT(x)            (   SpiIf    == (x)||\
                                        SpiSserr == (x)||\
                                        SpiBusy  == (x)||\
                                        SpiMdf   == (x)||\
                                        SpiTxe   == (x)||\
                                        SpiRxne  == (x))
/*******************************************************************************
 * Global variable definitions (declared in header file with 'extern')
 ******************************************************************************/

/*******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/

/*******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/

/*******************************************************************************
 * Function implementation - global ('extern') and local ('static')
 ******************************************************************************/
/**
 * @defgroup SPI_Global_Functions SPI全局函数定义
 * @{
 */

/**
 * @brief  SPI 状态获取.
 * @param  [in] SPIx: SPIx 通道              @ref M0P_SPI_TypeDef
 * @param  [in] enStatus: SPI状态位          @ref en_spi_status_t
 * @retval boolean_t:
 *         - TRUE: SPI状态位置起
 *         - FALSE: SPI状态位未置起
 */
boolean_t Spi_GetStatus(M0P_SPI_TypeDef *SPIx, en_spi_status_t enStatus)
{
    ASSERT(IS_VALID_STAT(enStatus));

    if (SPIx->STAT & enStatus)
    {
        return TRUE;
    }
    else
    {
        return FALSE;
    }

}

/**
 * @brief  SPI 中断清除.
 * @param  [in] SPIx: SPIx 通道              @ref M0P_SPI_TypeDef
 * @retval en_result_t:
 *         - Ok: SPI中断清除成功
 */
en_result_t Spi_ClearStatus(M0P_SPI_TypeDef *SPIx)
{
    SPIx->ICLR = 0;

    return Ok;
}

/**
 * @brief  SPI 中断使能函数.
 * @param  [in] SPIx: SPIx 通道              @ref M0P_SPI_TypeDef
 * @retval en_result_t:
 *         - Ok: SPI中断使能成功
 */
en_result_t Spi_IrqEnable(M0P_SPI_TypeDef *SPIx)
{
    SPIx->CR2 |= 0x4u;

    return Ok;
}


/**
 * @brief  SPI 中断禁止函数.
 * @param  [in] SPIx: SPIx 通道              @ref M0P_SPI_TypeDef
 * @retval en_result_t:
 *         - Ok: SPI中断禁止成功
 */
en_result_t Spi_IrqDisable(M0P_SPI_TypeDef *SPIx)
{
    SPIx->CR2 &= ~0x4u;

    return Ok;
}

/**
 * @brief  SPI 功能使能函数.
 * @param  [in] SPIx: SPIx 通道              @ref M0P_SPI_TypeDef
 * @param  [in] enFunc: SPI功能              @ref en_spi_func_t
 * @retval en_result_t:
 *         - Ok: SPI功能使能成功
 */
en_result_t Spi_FuncEnable(M0P_SPI_TypeDef *SPIx, en_spi_func_t enFunc)
{
    SPIx->CR2 |= enFunc;

    return Ok;
}


/**
 * @brief  SPI 功能禁止函数.
 * @param  [in] SPIx: SPIx 通道              @ref M0P_SPI_TypeDef
 * @param  [in] enFunc: SPI功能              @ref en_spi_func_t
 * @retval en_result_t:
 *         - Ok: SPI功能禁止成功
 */
en_result_t Spi_FuncDisable(M0P_SPI_TypeDef *SPIx, en_spi_func_t enFunc)
{
    SPIx->CR2 &= ~(uint32_t)enFunc;

    return Ok;
}


/**
 * @brief  SPI 总体初始化函数.
 * @param  [in] SPIx: SPIx 通道                 @ref M0P_SPI_TypeDef
 * @param  [in] pstcSpiCfg: 初始化结构体        @ref stc_spi_cfg_t
 * @retval en_result_t:
 *         - Ok: SPI功能初始化成功
 */
en_result_t Spi_Init(M0P_SPI_TypeDef *SPIx, stc_spi_cfg_t *pstcSpiCfg)
{
    ASSERT(NULL != pstcSpiCfg);

    SPIx->CR = 0;

    SPIx->SSN = TRUE;

    SPIx->CR = (uint32_t)pstcSpiCfg->enSpiMode |
               (uint32_t)pstcSpiCfg->enPclkDiv |
               (uint32_t)pstcSpiCfg->enCPOL    |
               (uint32_t)pstcSpiCfg->enCPHA    |
               (uint32_t)0x40;

    SPIx->STAT = 0x00;

    return Ok;
}


/**
 * @brief  SPI 配置主发送的电平.
 * @param  [in] SPIx: SPIx 通道              @ref M0P_SPI_TypeDef
 * @param  [in] bFlag: 高低电平              @ref boolean_t
 * @retval None
 */
void Spi_SetCS(M0P_SPI_TypeDef *SPIx, boolean_t bFlag)
{
    SPIx->SSN = bFlag;
}


/**
 * @brief  SPI 发送一字节函数.
 * @param  [in] SPIx: SPIx 通道                       @ref M0P_SPI_TypeDef
 * @param  [in] u8Data: 发送一字节数据
 * @retval en_result_t:
 *         - Ok: 发送一字节成功
 */
en_result_t Spi_SendData(M0P_SPI_TypeDef *SPIx, uint8_t u8Data)
{
    SPIx->DATA = u8Data;

    return Ok;
}

/**
 * @brief  SPI 写和读一字节函数.
 * @param  [in] SPIx: SPIx 通道                       @ref M0P_SPI_TypeDef
 * @param  [in] u8Data: 发送一字节数据
 * @retval uint8_t: 接收一字节数据
 */
uint8_t Spi_RWByte(M0P_SPI_TypeDef *SPIx, uint8_t u8Data)
{
    while (FALSE == SPIx->STAT_f.TXE) {;}
    SPIx->DATA = u8Data;
    while (FALSE == SPIx->STAT_f.RXNE) {;}
    return SPIx->DATA;
}

/**
 * @brief  SPI 从机预准备第一字节数据.
 * @param  [in] SPIx: SPIx 通道                       @ref M0P_SPI_TypeDef
 * @param  [in] u8Data: 预准备第一字节数据
 * @retval None
 */
void Spi_Slave_DummyWriteData(M0P_SPI_TypeDef *SPIx, uint8_t u8Data)
{
    while (FALSE == SPIx->STAT_f.TXE) {;}
    SPIx->DATA = u8Data;
}


/**
 * @brief  SPI 连续发送多字节函数.
 * @param  [in] SPIx: SPIx 通道                       @ref M0P_SPI_TypeDef
 * @param  [in] pu8Buf: 发送数据指针
 * @param  [in] u32Len: 发送数据长度
 * @retval en_result_t:
 *         - Ok: 发送多字节成功
 */
en_result_t Spi_SendBuf(M0P_SPI_TypeDef *SPIx, uint8_t *pu8Buf, uint32_t u32Len)
{
    uint32_t u32Index = 0;
    volatile uint8_t  u8data = 0;

    for (u32Index = 0; u32Index < u32Len; u32Index++)
    {
        while (FALSE == SPIx->STAT_f.TXE) {;}
        SPIx->DATA = pu8Buf[u32Index];
        while (FALSE == SPIx->STAT_f.RXNE) {;}
        u8data = SPIx->DATA;
    }

    while (FALSE == SPIx->STAT_f.TXE) {;}
    while (TRUE == SPIx->STAT_f.BUSY) {;}

    return Ok;
}


/**
 * @brief  SPI 接收一字节函数.
 * @param  [in] SPIx: SPIx 通道                       @ref M0P_SPI_TypeDef
 * @retval uint8_t:接收一字节
 */
uint8_t Spi_ReceiveData(M0P_SPI_TypeDef *SPIx)
{
    return SPIx->DATA;
}

/**
 * @brief  SPI 连续接收多字节函数.
 * @param  [in] SPIx: SPIx 通道                       @ref M0P_SPI_TypeDef
 * @param  [in] pu8Buf: 接收数据指针
 * @param  [in] u32Len: 接收数据长度
 * @retval en_result_t:
 *         - Ok: 接收多字节成功
 */
en_result_t Spi_ReceiveBuf(M0P_SPI_TypeDef *SPIx, uint8_t *pu8Buf, uint32_t u32Len)
{
    uint32_t u32Index = 0;

    for (u32Index = 0; u32Index < u32Len; u32Index++)
    {
        while (FALSE == SPIx->STAT_f.TXE) {;}
        SPIx->DATA = 0x00;
        while (FALSE == SPIx->STAT_f.RXNE) {;}
        pu8Buf[u32Index] = SPIx->DATA;
    }

    while (TRUE == SPIx->STAT_f.BUSY) {;}

    return Ok;
}

/**
 * @}
 */

/**
 * @}
 */

/**
* @}
*/

/******************************************************************************
 * EOF (not truncated)
 *****************************************************************************/

