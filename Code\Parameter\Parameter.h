/*!
 * @file
 * @brief This file defines public constants, types and functions for the fan drive module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __PARAMETER__H
#define __PARAMETER__H

#include <stdio.h>
#include <string.h>
#include <stddef.h>
#include <stdint.h>
#include "base_types.h"
#include "ddl.h"

#define PARAMETER_MAGIC 0x22446688
#define PARAMETER_FIREWARE_MAX      (10)
#define PARAMETER_ID_MAX      (5)
#define PARAMETER_ID_LEN      (8)
#define PARAMETER_FIREWARE_FILL      (1)
#define PARAMETER_SECTION_END 5555
#define MAINTENANCE_PARAMETER_ID 0xEE
#define MAINTENANCE_SECTION_PARAMETERS 64

typedef struct {
    uint32_t id;
    uint32_t version;
    uint32_t offset;
    uint32_t length;
    uint32_t crc;
}parameter_fireware_header_st;

typedef struct{
    uint32_t magic;
    parameter_fireware_header_st fheader[PARAMETER_FIREWARE_MAX];
    uint8_t hw_id_str[PARAMETER_ID_MAX][PARAMETER_ID_LEN];
    uint32_t len;
    uint32_t crc;
    uint32_t fill[PARAMETER_FIREWARE_FILL];
    uint8_t data[];
}parameter_fireware_st;

typedef enum
{
    PARAMETER_SECTION_REF = 0u,
    PARAMETER_SECTION_VAR,
    PARAMETER_SECTION_FRZ,
    PARAMETER_SECTION_VH,
    PARAMETER_SECTION_DEFORST,
    PARAMETER_SECTION_MAX
} parameter_section_e;

typedef struct{
    uint16_t id;
    uint16_t length;
    uint8_t *data;
}parameter_section_st;

typedef enum
{
    MAINTENANCE_PARAMETER_CRC = 0u,
    MAINTENANCE_PARAMETER_REFON_ADJUST = 10u,
    MAINTENANCE_PARAMETER_REFOFF_ADJUST,
    MAINTENANCE_PARAMETER_FRZON_ADJUST,
    MAINTENANCE_PARAMETER_FRZOFF_ADJUST,
    MAINTENANCE_PARAMETER_VARON_ADJUST,
    MAINTENANCE_PARAMETER_VAROFF_ADJUST,
    MAINTENANCE_PARAMETER_MAX
} maintenance_parameter_e;

extern parameter_fireware_st *model_parameters[];
#endif
