#include "Parameter.h"
typedef struct{
uint32_t magic;
parameter_fireware_header_st fheader[PARAMETER_FIREWARE_MAX];
uint8_t  hw_id_str[PARAMETER_ID_MAX][PARAMETER_ID_LEN];
uint32_t len;
uint32_t crc;
uint32_t fill[PARAMETER_FIREWARE_FILL];
uint8_t data[3840];
}parameter_fireware_st_3840;
static const parameter_fireware_st_3840 bs52s =
{
0x22446688,
{{0x1,0x1,0x0,0x800,0x9e51},{0x5,0x1,0x800,0x100,0x4cc7},{0x4,0x1,0x900,0x100,0x30ff},{0x2,0x1,0xa00,0x100,0x30ff},{0xee,0x1,0xb00,0x400,0x8915},{0},{0},{0},{0},{0}},
{"bs52s","bs52s1"},
0xf00,
0x1394,
{0},
{0x0,0x0,0x0,0x73,0x2,0x17,0x2,0xd,0x2,0x1c,0x2,0x12,0x2,0x2b,0x2,0x21,0x2,0x35,0x2,0x2b,
 0x2,0x44,0x2,0x3a,0x2,0x4e,0x2,0x44,0x2,0x53,0x2,0x49,0x2,0x17,0x2,0xd,0x2,0x1c,0x2,0x12,
 0x2,0x2b,0x2,0x21,0x2,0x35,0x2,0x2b,0x2,0x44,0x2,0x3a,0x2,0x4e,0x2,0x44,0x2,0x53,0x2,0x49,
 0x2,0x17,0x2,0xd,0x2,0x1c,0x2,0x12,0x2,0x2b,0x2,0x21,0x2,0x35,0x2,0x2b,0x2,0x44,0x2,0x3a,
 0x2,0x4e,0x2,0x44,0x2,0x53,0x2,0x49,0x2,0x12,0x2,0x8,0x2,0x17,0x2,0xd,0x2,0x26,0x2,0x1c,
 0x2,0x30,0x2,0x26,0x2,0x3f,0x2,0x35,0x2,0x49,0x2,0x3f,0x2,0x4e,0x2,0x44,0x2,0x12,0x2,0x8,
 0x2,0x17,0x2,0xd,0x2,0x26,0x2,0x1c,0x2,0x30,0x2,0x26,0x2,0x3f,0x2,0x35,0x2,0x49,0x2,0x3f,
 0x2,0x4e,0x2,0x44,0x2,0x12,0x2,0x8,0x2,0x17,0x2,0xd,0x2,0x26,0x2,0x1c,0x2,0x30,0x2,0x26,
 0x2,0x3f,0x2,0x35,0x2,0x49,0x2,0x3f,0x2,0x4e,0x2,0x44,0x2,0x12,0x2,0x8,0x2,0x17,0x2,0xd,
 0x2,0x26,0x2,0x1c,0x2,0x30,0x2,0x26,0x2,0x3f,0x2,0x35,0x2,0x49,0x2,0x3f,0x2,0x4e,0x2,0x44,
 0x0,0xa,0x0,0xa5,0x0,0xf,0x0,0x82,0x0,0x15,0x0,0x48,0x0,0x15,0x0,0x48,0x0,0x15,0x0,0x32,
 0x0,0x16,0x0,0x29,0x0,0x1a,0x0,0x2c,0x15,0xb3,0x0,0x1,0x0,0x49,0x1,0xe0,0x1,0xdb,0x1,0xef,
 0x1,0xea,0x1,0xe0,0x1,0xdb,0x1,0xd6,0x1,0xd1,0x2,0x1c,0x2,0x12,0x1,0xe0,0x1,0xdb,0x1,0xea,
 0x1,0xe5,0x1,0xe0,0x1,0xdb,0x1,0xd6,0x1,0xd1,0x2,0x1c,0x2,0x12,0x1,0xe0,0x1,0xdb,0x1,0xea,
 0x1,0xe5,0x1,0xe0,0x1,0xdb,0x1,0xd6,0x1,0xd1,0x2,0x1c,0x2,0x12,0x1,0xe0,0x1,0xdb,0x1,0xea,
 0x1,0xe5,0x1,0xe0,0x1,0xdb,0x1,0xd6,0x1,0xd1,0x2,0x1c,0x2,0x12,0x1,0xe0,0x1,0xdb,0x1,0xea,
 0x1,0xe5,0x1,0xe0,0x1,0xdb,0x1,0xd6,0x1,0xd1,0x2,0x1c,0x2,0x12,0x1,0xe0,0x1,0xdb,0x1,0xe5,
 0x1,0xe0,0x1,0xe0,0x1,0xdb,0x1,0xd6,0x1,0xd1,0x2,0x1c,0x2,0x12,0x1,0xe0,0x1,0xdb,0x1,0xdb,
 0x1,0xd6,0x1,0xe0,0x1,0xdb,0x1,0xd6,0x1,0xd1,0x2,0x1c,0x2,0x12,0x15,0xb3,0x0,0x2,0x0,0x9d,
 0x1,0x9,0x0,0xf5,0x1,0x13,0x0,0xff,0x1,0x1d,0x1,0x9,0x1,0x27,0x1,0x13,0x1,0x31,0x1,0x1d,
 0x1,0x40,0x1,0x2c,0x1,0x45,0x1,0x31,0x1,0x4f,0x1,0x3b,0x1,0x59,0x1,0x45,0x1,0x9,0x0,0xf5,
 0x1,0x13,0x0,0xff,0x1,0x1d,0x1,0x9,0x1,0x27,0x1,0x13,0x1,0x31,0x1,0x1d,0x1,0x40,0x1,0x2c,
 0x1,0x45,0x1,0x31,0x1,0x4f,0x1,0x3b,0x1,0x59,0x1,0x45,0x1,0x13,0x0,0xff,0x1,0x1d,0x1,0x9,
 0x1,0x27,0x1,0x13,0x1,0x31,0x1,0x1d,0x1,0x3b,0x1,0x27,0x1,0x40,0x1,0x2c,0x1,0x45,0x1,0x31,
 0x1,0x4f,0x1,0x3b,0x1,0x59,0x1,0x45,0x1,0x18,0x0,0xfa,0x1,0x22,0x1,0x4,0x1,0x2c,0x1,0xe,
 0x1,0x36,0x1,0x18,0x1,0x40,0x1,0x22,0x1,0x45,0x1,0x27,0x1,0x4a,0x1,0x2c,0x1,0x54,0x1,0x36,
 0x1,0x5e,0x1,0x40,0x1,0x18,0x0,0xfa,0x1,0x22,0x1,0x4,0x1,0x2c,0x1,0xe,0x1,0x36,0x1,0x18,
 0x1,0x40,0x1,0x22,0x1,0x45,0x1,0x27,0x1,0x4a,0x1,0x2c,0x1,0x54,0x1,0x36,0x1,0x5e,0x1,0x40,
 0x1,0xe,0x0,0xf0,0x1,0x22,0x1,0x4,0x1,0x2c,0x1,0xe,0x1,0x36,0x1,0x18,0x1,0x40,0x1,0x22,
 0x1,0x45,0x1,0x27,0x1,0x4a,0x1,0x2c,0x1,0x54,0x1,0x36,0x1,0x5e,0x1,0x40,0x1,0xe,0x0,0xf0,
 0x1,0x18,0x0,0xfa,0x1,0x22,0x1,0x4,0x1,0x2c,0x1,0xe,0x1,0x36,0x1,0x18,0x1,0x3b,0x1,0x1d,
 0x1,0x45,0x1,0x27,0x1,0x4f,0x1,0x31,0x1,0x59,0x1,0x3b,0x0,0x9,0x0,0x13,0x0,0xc,0x0,0x1e,
 0x0,0x14,0x0,0x28,0x0,0x14,0x0,0x28,0x0,0x1b,0x0,0x2e,0x0,0x18,0x0,0x26,0x0,0x23,0x0,0x34,
 0x0,0xc3,0x0,0xaf,0x0,0xc3,0x0,0xaf,0x0,0xc3,0x0,0xaf,0x0,0xc8,0x0,0xaa,0x0,0xc8,0x0,0xaa,
 0x0,0xc8,0x0,0xaa,0x0,0xc8,0x0,0xaa,0x15,0xb3,0x0,0x3,0x0,0x88,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x3,0x0,0x3,0x0,0x4,0x0,0x4,0x0,0x6,0x0,0x6,0x0,0xc,
 0x0,0xc,0x0,0x10,0x0,0x10,0x0,0x12,0x0,0x12,0x0,0x18,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x4,0x0,0x4,0x0,0x6,0x0,0x6,0x0,0x6,0x0,0x9,0x0,0xc,0x0,0xc,
 0x0,0x12,0x0,0x12,0x0,0x18,0x0,0x18,0x0,0x1e,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x6,0x0,0x6,0x0,0x9,0x0,0x9,0x0,0xc,0x0,0xc,0x0,0x12,0x0,0x12,0x0,0x18,
 0x0,0x18,0x0,0x1e,0x0,0x1e,0x0,0x24,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0xa,0x0,0xa,0x0,0x10,0x0,0x10,0x0,0x16,0x0,0x16,0x0,0x1c,0x0,0x1e,0x0,0x24,0x0,0x2a,
 0x0,0x30,0x0,0x33,0x0,0x39,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x6,0x0,0x6,0x0,0xc,
 0x0,0xc,0x0,0x12,0x0,0x12,0x0,0x12,0x0,0x18,0x0,0x1e,0x0,0x39,0x0,0x39,0x0,0x39,0x0,0x3c,
 0x0,0x3c,0x0,0x3c,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0xc,0x0,0xc,0x0,0x18,0x0,0x18,
 0x0,0x1e,0x0,0x1e,0x0,0x24,0x0,0x24,0x0,0x2a,0x0,0x39,0x0,0x39,0x0,0x39,0x0,0x3c,0x0,0x3c,
 0x0,0x3c,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x12,0x0,0x12,0x0,0x1e,0x0,0x1e,0x0,0x24,
 0x0,0x24,0x0,0x2a,0x0,0x2a,0x0,0x2d,0x0,0x39,0x0,0x39,0x0,0x39,0x0,0x3c,0x0,0x3c,0x0,0x3c,
 0x15,0xb3,0x0,0x4,0x0,0x57,0x0,0xc,0x1,0x68,0x1,0x68,0x2,0x44,0x0,0x10,0x0,0xb4,0x0,0xb4,
 0x2,0x44,0x0,0x19,0x0,0x78,0x0,0x78,0x2,0x44,0x0,0x24,0x0,0x3c,0x0,0x3c,0x2,0x44,0x0,0x30,
 0x0,0x0,0x0,0x0,0x2,0x12,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0xc,0x2,0x1c,0x2,0x1c,0x2,0x44,0x0,0x10,0x1,0x68,0x1,0x68,0x2,0x44,0x0,0x19,
 0x0,0xf0,0x0,0xf0,0x2,0x44,0x0,0x24,0x0,0xb4,0x0,0xb4,0x2,0x44,0x0,0x30,0x0,0xb4,0x0,0xb4,
 0x2,0x44,0x0,0x3c,0x0,0x78,0x0,0x78,0x2,0x44,0x0,0x49,0x0,0x0,0x0,0x0,0x2,0x44,0x0,0xc,
 0x0,0xb4,0x0,0xb4,0x2,0x44,0x0,0x10,0x0,0x78,0x0,0x78,0x2,0x44,0x0,0x19,0x0,0x0,0x0,0x0,
 0x1,0xfe,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x15,0xb3,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0x0,0x16,0x0,0x0,0x0,0x3,0x30,0xc,0x4,0xf6,0x7,0x62,
 0x7,0xa,0x11,0x94,0x3,0x20,0x9,0x60,0x5,0xdc,0x13,0x88,0x0,0x10,0x8,0x98,0x1,0xf4,0x12,0x5c,
 0x2,0x96,0x3,0xe8,0x1,0xf4,0x3,0x20,0x4,0xb0,0x0,0x26,0x4,0xf8,0x2,0xbb,0x4,0xcb,0x2,0xe9,
 0x3,0x6b,0x4,0xb0,0x4,0x4c,0x3,0xe8,0x3,0x20,0x0,0x14,0x0,0x96,0x1,0x90,0x0,0x96,0x0,0x1,
 0x1,0x4a,0x0,0xc8,0x1,0x31,0x1,0x31,0x2,0x90,0x0,0x32,0x13,0x88,0x0,0x1,0x13,0x88,0xd,0xac,
 0x4e,0x20,0x11,0x94,0x0,0x64,0xb,0xb7,0x0,0x64,0x1,0x2c,0x1,0x90,0x0,0x0,0x1f,0x40,0x0,0x96,
 0x0,0x32,0x3,0xe8,0x0,0x0,0x5,0x14,0x0,0x9c,0x0,0x6f,0x1,0xc2,0x0,0xc8,0x0,0xc8,0x5,0xdc,
 0x5,0xdc,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0x6d,0xb7,0x0,0xd,
 0x0,0x14,0x0,0x1c,0x0,0x23,0x0,0x28,0x0,0x1,0xff,0xff,0x0,0x19,0x0,0x4b,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x8,0x0,0x6,0x0,0x1e,0xff,0xf4,0x0,0xc,0x0,0x3c,
 0x0,0x3,0x0,0x2d,0x0,0x3,0x0,0x1,0x0,0x3c,0x0,0x8,0x0,0x3,0x0,0x5,0x0,0x1e,0x0,0x10,
 0x0,0xc,0x0,0x10,0x0,0x19,0x0,0x24,0x0,0x30,0x0,0x3c,0x0,0x49,0x0,0x48,0x0,0x2,0x0,0x5,
 0x0,0x2,0x0,0x2,0x0,0x5,0x0,0x5,0x9,0x60,0x5,0x64,0x6,0x72,0x8,0x34,0x9,0x60,0xc,0xa8,
 0xd,0xd4,0xf,0xf0,0x11,0x3a,0x4,0xce,0x7,0x8,0x0,0xa,0x0,0xa,0x0,0xf,0x0,0x8,0x0,0x9,
 0x0,0xa,0x0,0xb,0x0,0xc,0x0,0xd,0x0,0x1,0x0,0x5,0x0,0x1e,0x0,0x23,0x0,0x28,0x0,0x2d,
 0x0,0x32,0x0,0x37,0x0,0x1e,0x0,0x2,0x0,0xa,0x0,0x3c,0x0,0x0,0x0,0x3c,0x0,0x18,0x0,0xa,
 0x0,0x3c,0x0,0xa,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0
}
};
static const parameter_fireware_st_3840 bf52s =
{
0x22446688,
{{0x1,0x1,0x0,0x800,0x49fd},{0x5,0x1,0x800,0x100,0x4cc7},{0x4,0x1,0x900,0x100,0x30ff},{0x2,0x1,0xa00,0x100,0x30ff},{0xee,0x1,0xb00,0x400,0x8915},{0},{0},{0},{0},{0}},
{"bf52s","bf52s1"},
0xf00,
0x8ffb,
{0},
{0x0,0x0,0x0,0x73,0x2,0x17,0x2,0xd,0x2,0x1c,0x2,0x12,0x2,0x2b,0x2,0x21,0x2,0x35,0x2,0x2b,
 0x2,0x44,0x2,0x3a,0x2,0x4e,0x2,0x44,0x2,0x53,0x2,0x49,0x2,0x17,0x2,0xd,0x2,0x1c,0x2,0x12,
 0x2,0x2b,0x2,0x21,0x2,0x35,0x2,0x2b,0x2,0x44,0x2,0x3a,0x2,0x4e,0x2,0x44,0x2,0x53,0x2,0x49,
 0x2,0x17,0x2,0xd,0x2,0x1c,0x2,0x12,0x2,0x2b,0x2,0x21,0x2,0x35,0x2,0x2b,0x2,0x44,0x2,0x3a,
 0x2,0x4e,0x2,0x44,0x2,0x53,0x2,0x49,0x2,0x12,0x2,0x8,0x2,0x17,0x2,0xd,0x2,0x26,0x2,0x1c,
 0x2,0x30,0x2,0x26,0x2,0x3f,0x2,0x35,0x2,0x49,0x2,0x3f,0x2,0x4e,0x2,0x44,0x2,0x12,0x2,0x8,
 0x2,0x17,0x2,0xd,0x2,0x26,0x2,0x1c,0x2,0x30,0x2,0x26,0x2,0x3f,0x2,0x35,0x2,0x49,0x2,0x3f,
 0x2,0x4e,0x2,0x44,0x2,0x12,0x2,0x8,0x2,0x17,0x2,0xd,0x2,0x26,0x2,0x1c,0x2,0x30,0x2,0x26,
 0x2,0x3f,0x2,0x35,0x2,0x49,0x2,0x3f,0x2,0x4e,0x2,0x44,0x2,0x12,0x2,0x8,0x2,0x17,0x2,0xd,
 0x2,0x26,0x2,0x1c,0x2,0x30,0x2,0x26,0x2,0x3f,0x2,0x35,0x2,0x49,0x2,0x3f,0x2,0x4e,0x2,0x44,
 0x0,0xa,0x0,0xa5,0x0,0xf,0x0,0x82,0x0,0x15,0x0,0x48,0x0,0x15,0x0,0x48,0x0,0x15,0x0,0x32,
 0x0,0x16,0x0,0x29,0x0,0x1a,0x0,0x2c,0x15,0xb3,0x0,0x1,0x0,0x49,0x1,0xe0,0x1,0xdb,0x1,0xef,
 0x1,0xea,0x1,0xe0,0x1,0xdb,0x1,0xd6,0x1,0xd1,0x2,0x1c,0x2,0x12,0x1,0xe0,0x1,0xdb,0x1,0xea,
 0x1,0xe5,0x1,0xe0,0x1,0xdb,0x1,0xd6,0x1,0xd1,0x2,0x1c,0x2,0x12,0x1,0xe0,0x1,0xdb,0x1,0xea,
 0x1,0xe5,0x1,0xe0,0x1,0xdb,0x1,0xd6,0x1,0xd1,0x2,0x1c,0x2,0x12,0x1,0xe0,0x1,0xdb,0x1,0xea,
 0x1,0xe5,0x1,0xe0,0x1,0xdb,0x1,0xd6,0x1,0xd1,0x2,0x1c,0x2,0x12,0x1,0xe0,0x1,0xdb,0x1,0xea,
 0x1,0xe5,0x1,0xe0,0x1,0xdb,0x1,0xd6,0x1,0xd1,0x2,0x1c,0x2,0x12,0x1,0xe0,0x1,0xdb,0x1,0xe5,
 0x1,0xe0,0x1,0xe0,0x1,0xdb,0x1,0xd6,0x1,0xd1,0x2,0x1c,0x2,0x12,0x1,0xe0,0x1,0xdb,0x1,0xdb,
 0x1,0xd6,0x1,0xe0,0x1,0xdb,0x1,0xd6,0x1,0xd1,0x2,0x1c,0x2,0x12,0x15,0xb3,0x0,0x2,0x0,0x9d,
 0x1,0x9,0x0,0xf5,0x1,0x13,0x0,0xff,0x1,0x1d,0x1,0x9,0x1,0x27,0x1,0x13,0x1,0x31,0x1,0x1d,
 0x1,0x40,0x1,0x2c,0x1,0x45,0x1,0x31,0x1,0x4f,0x1,0x3b,0x1,0x59,0x1,0x45,0x1,0x9,0x0,0xf5,
 0x1,0x13,0x0,0xff,0x1,0x1d,0x1,0x9,0x1,0x27,0x1,0x13,0x1,0x31,0x1,0x1d,0x1,0x40,0x1,0x2c,
 0x1,0x45,0x1,0x31,0x1,0x4f,0x1,0x3b,0x1,0x59,0x1,0x45,0x1,0x13,0x0,0xff,0x1,0x1d,0x1,0x9,
 0x1,0x27,0x1,0x13,0x1,0x31,0x1,0x1d,0x1,0x31,0x1,0x1d,0x1,0x40,0x1,0x2c,0x1,0x45,0x1,0x31,
 0x1,0x4f,0x1,0x3b,0x1,0x59,0x1,0x45,0x1,0x18,0x0,0xfa,0x1,0x22,0x1,0x4,0x1,0x2c,0x1,0xe,
 0x1,0x36,0x1,0x18,0x1,0x36,0x1,0x18,0x1,0x45,0x1,0x27,0x1,0x4a,0x1,0x2c,0x1,0x54,0x1,0x36,
 0x1,0x5e,0x1,0x40,0x1,0x18,0x0,0xfa,0x1,0x22,0x1,0x4,0x1,0x2c,0x1,0xe,0x1,0x36,0x1,0x18,
 0x1,0x36,0x1,0x18,0x1,0x45,0x1,0x27,0x1,0x4a,0x1,0x2c,0x1,0x54,0x1,0x36,0x1,0x5e,0x1,0x40,
 0x1,0xe,0x0,0xf0,0x1,0x22,0x1,0x4,0x1,0x2c,0x1,0xe,0x1,0x36,0x1,0x18,0x1,0x40,0x1,0x22,
 0x1,0x45,0x1,0x27,0x1,0x4a,0x1,0x2c,0x1,0x54,0x1,0x36,0x1,0x5e,0x1,0x40,0x1,0xe,0x0,0xf0,
 0x1,0x18,0x0,0xfa,0x1,0x22,0x1,0x4,0x1,0x2c,0x1,0xe,0x1,0x36,0x1,0x18,0x1,0x3b,0x1,0x1d,
 0x1,0x45,0x1,0x27,0x1,0x4f,0x1,0x31,0x1,0x59,0x1,0x3b,0x0,0x9,0x0,0x13,0x0,0xc,0x0,0x1e,
 0x0,0x14,0x0,0x28,0x0,0x14,0x0,0x28,0x0,0x1b,0x0,0x2e,0x0,0x18,0x0,0x26,0x0,0x23,0x0,0x34,
 0x0,0xc3,0x0,0xaf,0x0,0xc3,0x0,0xaf,0x0,0xc3,0x0,0xaf,0x0,0xc8,0x0,0xaa,0x0,0xc8,0x0,0xaa,
 0x0,0xc8,0x0,0xaa,0x0,0xc8,0x0,0xaa,0x15,0xb3,0x0,0x3,0x0,0x88,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x3,0x0,0x3,0x0,0x4,0x0,0x4,0x0,0x6,0x0,0x6,0x0,0xc,
 0x0,0xc,0x0,0x10,0x0,0x10,0x0,0x12,0x0,0x12,0x0,0x18,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x4,0x0,0x4,0x0,0x6,0x0,0x6,0x0,0x6,0x0,0x9,0x0,0xc,0x0,0xc,
 0x0,0x12,0x0,0x12,0x0,0x18,0x0,0x18,0x0,0x1e,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x6,0x0,0x6,0x0,0x9,0x0,0x9,0x0,0xc,0x0,0xc,0x0,0x12,0x0,0x12,0x0,0x18,
 0x0,0x18,0x0,0x1e,0x0,0x1e,0x0,0x24,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0xa,0x0,0xa,0x0,0x10,0x0,0x10,0x0,0x16,0x0,0x16,0x0,0x1c,0x0,0x1e,0x0,0x24,0x0,0x2a,
 0x0,0x30,0x0,0x33,0x0,0x39,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x6,0x0,0x6,0x0,0xc,
 0x0,0xc,0x0,0x12,0x0,0x12,0x0,0x12,0x0,0x18,0x0,0x1e,0x0,0x39,0x0,0x39,0x0,0x39,0x0,0x3c,
 0x0,0x3c,0x0,0x3c,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0xc,0x0,0xc,0x0,0x18,0x0,0x18,
 0x0,0x1e,0x0,0x1e,0x0,0x24,0x0,0x24,0x0,0x2a,0x0,0x39,0x0,0x39,0x0,0x39,0x0,0x3c,0x0,0x3c,
 0x0,0x3c,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x12,0x0,0x12,0x0,0x1e,0x0,0x1e,0x0,0x24,
 0x0,0x24,0x0,0x2a,0x0,0x2a,0x0,0x2d,0x0,0x39,0x0,0x39,0x0,0x39,0x0,0x3c,0x0,0x3c,0x0,0x3c,
 0x15,0xb3,0x0,0x4,0x0,0x57,0x0,0xc,0x1,0x68,0x1,0x68,0x2,0x44,0x0,0x10,0x0,0xb4,0x0,0xb4,
 0x2,0x44,0x0,0x19,0x0,0x78,0x0,0x78,0x2,0x44,0x0,0x24,0x0,0x3c,0x0,0x3c,0x2,0x44,0x0,0x30,
 0x0,0x0,0x0,0x0,0x2,0x12,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0xc,0x2,0x1c,0x2,0x1c,0x2,0x44,0x0,0x10,0x1,0x68,0x1,0x68,0x2,0x44,0x0,0x19,
 0x0,0xf0,0x0,0xf0,0x2,0x44,0x0,0x24,0x0,0xb4,0x0,0xb4,0x2,0x44,0x0,0x30,0x0,0xb4,0x0,0xb4,
 0x2,0x44,0x0,0x3c,0x0,0x78,0x0,0x78,0x2,0x44,0x0,0x49,0x0,0x0,0x0,0x0,0x2,0x44,0x0,0xc,
 0x0,0xb4,0x0,0xb4,0x2,0x44,0x0,0x10,0x0,0x78,0x0,0x78,0x2,0x44,0x0,0x19,0x0,0x0,0x0,0x0,
 0x1,0xfe,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x15,0xb3,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0x0,0x16,0x0,0x0,0x0,0x3,0x30,0xc,0x4,0xf6,0x7,0x62,
 0x7,0xa,0x11,0x94,0x3,0x20,0x9,0x60,0x5,0xdc,0x13,0x88,0x0,0x10,0x8,0x98,0x1,0xf4,0x12,0x5c,
 0x2,0x96,0x3,0xe8,0x1,0xf4,0x3,0x20,0x4,0xb0,0x0,0x26,0x4,0xf8,0x2,0xbb,0x4,0xcb,0x2,0xe9,
 0x3,0x6b,0x4,0xb0,0x4,0x4c,0x3,0xe8,0x3,0x20,0x0,0x14,0x0,0x96,0x1,0x90,0x0,0x96,0x0,0x1,
 0x1,0x4a,0x0,0xc8,0x1,0x31,0x1,0x31,0x2,0x90,0x0,0x32,0x13,0x88,0x0,0x1,0x13,0x88,0xd,0xac,
 0x4e,0x20,0x11,0x94,0x0,0x64,0xb,0xb7,0x0,0x64,0x1,0x2c,0x1,0x90,0x0,0x0,0x1f,0x40,0x0,0x96,
 0x0,0x32,0x3,0xe8,0x0,0x0,0x5,0x14,0x0,0x9c,0x0,0x6f,0x1,0xc2,0x0,0xc8,0x0,0xc8,0x5,0xdc,
 0x5,0xdc,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0x6d,0xb7,0x0,0xd,
 0x0,0x14,0x0,0x1c,0x0,0x23,0x0,0x28,0x0,0x1,0xff,0xff,0x0,0x19,0x0,0x4b,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x8,0x0,0x6,0x0,0x1e,0xff,0xf4,0x0,0xc,0x0,0x3c,
 0x0,0x3,0x0,0x2d,0x0,0x3,0x0,0x1,0x0,0x3c,0x0,0x8,0x0,0x3,0x0,0x5,0x0,0x1e,0x0,0x10,
 0x0,0xc,0x0,0x10,0x0,0x19,0x0,0x24,0x0,0x30,0x0,0x3c,0x0,0x49,0x0,0x48,0x0,0x2,0x0,0x5,
 0x0,0x2,0x0,0x2,0x0,0x5,0x0,0x5,0x9,0x60,0x5,0x64,0x6,0x72,0x8,0x34,0x9,0x60,0xc,0xa8,
 0xd,0xd4,0xf,0xf0,0x11,0x3a,0x4,0xce,0x7,0x8,0x0,0xa,0x0,0xa,0x0,0xf,0x0,0x8,0x0,0x9,
 0x0,0xa,0x0,0xb,0x0,0xc,0x0,0xd,0x0,0x1,0x0,0x5,0x0,0x1e,0x0,0x23,0x0,0x28,0x0,0x2d,
 0x0,0x32,0x0,0x37,0x0,0x1e,0x0,0x2,0x0,0xa,0x0,0x3c,0x0,0x0,0x0,0x3c,0x0,0x18,0x0,0xa,
 0x0,0x3c,0x0,0xa,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
 0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0
}
};
parameter_fireware_st *model_parameters[3] = {
(parameter_fireware_st *)&bs52s,
(parameter_fireware_st *)&bf52s,
NULL
};
