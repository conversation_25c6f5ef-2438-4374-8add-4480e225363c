/*!
 * @file
 * @brief This file defines public constants, types and functions for the system timer.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stdint.h>
#include <stdbool.h>

#ifndef __SYSTEMTIMERMODULE_H__
#define __SYSTEMTIMERMODULE_H__

struct I_SystemTimer_Api_st;

typedef struct I_SystemTimer_st
{
    const struct I_SystemTimer_Api_st *Api;
} I_SystemTimer_st;

typedef struct Clock_st
{
    uint8_t u8_Day;
    uint8_t u8_Hour;
    uint8_t u8_Minute;
    uint8_t u8_Second;
    uint16_t u16_MSec;
} Clock_st;

typedef struct SystemTimer_st
{
    I_SystemTimer_st Interface;
    struct
    {
        Clock_st st_Clock;
        uint16_t u16_DayCount;
        uint16_t u16_HourCount;
        uint16_t u16_MinuteCount;
        uint16_t u16_SecondCount;
        uint16_t u16_MSecCount;
    } _private;
} SystemTimer_st;

typedef struct I_SystemTimer_Api_st
{
    uint16_t (*p_Get_DayCount)(void);
    uint16_t (*p_Get_HourCount)(void);
    uint16_t (*p_Get_MinuteCount)(void);
    uint16_t (*p_Get_SecondCount)(void);
    uint16_t (*p_Get_MSecCount)(void);
    uint16_t (*p_Get_DayElapsedTime)(uint16_t u16_dayStartTime);
    uint16_t (*p_Get_HourElapsedTime)(uint16_t u16_hourStartTime);
    uint16_t (*p_Get_MinuteElapsedTime)(uint16_t u16_minuteStartTime);
    uint16_t (*p_Get_SecondElapsedTime)(uint16_t u16_secondStartTime);
    uint16_t (*p_Get_MSecElapsedTime)(uint16_t u16_msecStartTime);

    void (*p_Shorten_Timer)(const uint16_t u16_minute);
    void (*p_Add_MSecCount)(void);
} I_SystemTimer_Api_st;

void Init_SystemTimer(void);
uint16_t Get_DayCount(void);
uint16_t Get_HourCount(void);
uint16_t Get_MinuteCount(void);
uint16_t Get_SecondCount(void);
uint16_t Get_MSecCount(void);
uint16_t Get_DayElapsedTime(uint16_t u16_dayStartTime);
uint16_t Get_HourElapsedTime(uint16_t u16_hourStartTime);
uint16_t Get_MinuteElapsedTime(uint16_t u16_minuteStartTime);
uint16_t Get_SecondElapsedTime(uint16_t u16_secondStartTime);
uint16_t Get_MSecElapsedTime(uint16_t u16_msecStartTime);
void Shorten_Timer(const uint16_t u16_minute);
void Add_MSecCount(void);

#endif /* __SYSTEMTIMERMODULE_H__ */
