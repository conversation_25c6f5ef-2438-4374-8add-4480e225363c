/*!
 * @file
 * @brief Clock adapter.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "sysctrl.h"
#include "flash.h"
/**
 * @brief 系统时钟初始化为RC48M
 * @param [in]  enRchFreq   RCH目标频率 @en_sysctrl_rch_freq_t
 * @retval None
 */
void App_SystemClkInit_RC48M(en_sysctrl_rc48m_freq_t enRc48mFreq)
{
    if(enRc48mFreq >= SysctrlRc48mFreq32MHz)
    {
        /* HCLK超过24M：此处设置FLASH读等待周期为1 cycle */
        Flash_WaitCycle(FlashWaitCycle1);
    }
    /* 加载目标频率的RC48M的数据 */
    Sysctrl_SetRC48MTrim(enRc48mFreq);

    Sysctrl_ClkSourceEnable(SysctrlClkRC48M, TRUE);

    Sysctrl_SysClkSwitch(SysctrlClkRC48M);

    if(enRc48mFreq < SysctrlRc48mFreq32MHz)
    {
        /* HCLK不超过24M：此处设置FLASH读等待周期为0 cycle */
        Flash_WaitCycle(FlashWaitCycle0);
    }
}

void Board_InitClock(void)
{
    App_SystemClkInit_RC48M(SysctrlRc48mFreq48MHz);
}
