/**
 ******************************************************************************
 * @file   opa.h
 *
 * @brief This file contains all the functions prototypes of the OPA driver
 *        library.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-03-15       MDAS            First version
 @endverbatim
 ******************************************************************************
 * Copyright (C) 2023, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 ******************************************************************************
 */

#ifndef __OPA_H__
#define __OPA_H__

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "ddl.h"

#ifdef __cplusplus
extern "C"
{
#endif

/**
 * @addtogroup HC32L186_DDL 驱动库
 * @{
 */

/**
 * @addtogroup DDL_OPA OPA模块驱动库
 * @{
 */


/*******************************************************************************
  Global function prototypes (definition in C source)
 ******************************************************************************/
/**
 * @defgroup OPA_Global_Types OPA全局类型定义
 * @{
 */

/**
 * @brief  OPA OUTx选择
 */
typedef enum
{
    OpaOen1  = 3u,            /*!< OUT1 */
    OpaOen2  = 4u,            /*!< OUT2 */
    OpaOen3  = 5u,            /*!< OUT3 */
    OpaOen4  = 6u             /*!< OUT4 */
} en_opa_oenx_t;
/**
 * @}
 */

/******************************************************************************
 * Global variable declarations ('extern', definition in C source)
 ******************************************************************************/

/******************************************************************************
 * Global function prototypes (definition in C source)
 ******************************************************************************/
/**
 * @addtogroup OPA_Global_Functions OPA全局函数定义
 * @{
 */
//OPA 通道使能
void Opa_Cmd(boolean_t NewStatus);
void Opa_CmdBuf(boolean_t NewStatus);

//使能输出OUTX
void Opa_CmdOenx(en_opa_oenx_t oenx, boolean_t NewState);
/**
 * @}
 */


/**
 * @}
 */

/**
 * @}
 */

#ifdef __cplusplus
#endif

#endif /* __OPA_H__ */
/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/


