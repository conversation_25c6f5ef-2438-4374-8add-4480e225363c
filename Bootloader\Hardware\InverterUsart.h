/*!
 * @file
 * @brief This file defines public constants, types and functions for INVERTER usart.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef INVERTER_USART_
#define INVERTER_USART_

#include <stdint.h>
#include <stdbool.h>
#include "FirewareComm.h"

#define U8_INVERTER_SEND_TIMEOUT_MILLISECOND    ((uint8_t)200)
#define U16_INVERTER_RECV_TIMEOUT_MILLISECOND    ((uint16_t)100)
#define U16_RECE_DATA_ERROR_TIME_WITH_100MS     ((uint16_t)600)

void Init_UartInverter(void);
void Handle_UartInverterOverTime(void);
void Handle_UartInverterSendData(void);
void Handle_UartInverterReceData(const uint8_t u8_rece_data);

#endif
