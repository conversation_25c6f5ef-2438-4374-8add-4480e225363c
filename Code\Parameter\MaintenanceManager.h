/*!
 * @file
 * @brief This file defines public constants, types and functions for the fan drive module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __MAINTENANCE_MANAGER__H
#define __MAINTENANCE_MANAGER__H

#include "Parameter.h"
#include "Driver_Flash.h"

typedef void (*maintenance_manager_state_func)(void);

#define UPLOAD_TIMEOUT_100MS 50
#define DOWNLOAD_TIMEOUT_100MS 50
#define UPLOAD_RETRY_TIMEOUT_100MS 80

typedef enum
{
    MAINTENANCE_MANAGER_STATE_UPLOAD,
    MAINTENANCE_MANAGER_STATE_DOWNLOAD,
    MAINTENANCE_MANAGER_STATE_WAIT,
    MAINTENANCE_MANAGER_STATE_MAX,
} maintenance_manager_state_e;


typedef struct{
    uint8_t section;
    uint16_t timercount;
    uint16_t mask;
    uint32_t total_version;
    uint32_t total_crc;
    bool uploaded;
    maintenance_manager_state_e state;
    maintenance_manager_state_func statefuncs[MAINTENANCE_MANAGER_STATE_MAX];
}maintenance_manager_st;

int GetMaintenanceParamVal(maintenance_parameter_e type, uint16_t *val);
int DownloadMaintenanceParam(uint8_t index, const char *paramstr);
int UploadMaintenanceParam(char *pResult);
void MaintenanceManagerInit(void);
void MaintenanceManagerRun(void);
#endif
