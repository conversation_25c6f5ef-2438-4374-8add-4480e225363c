/*!
 * @file
 * @brief This file defines public constants, types and functions for INVERTER usart.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef INVERTER_USART_
#define INVERTER_USART_

#include <stdint.h>
#include <stdbool.h>

#define U8_INVERTER_SEND_TIMEOUT_MILLISECOND ((uint8_t)100)
#define U8_INVERTER_RECV_TIMEOUT_MILLISECOND ((uint8_t)100)
#define U16_INVERTER_RECE_DATA_ERROR_TIME_WITH_100MS ((uint16_t)360)
#define U16_INVERTER_FCT_RECE_DATA_ERROR_TIME_WITH_100MS ((uint16_t)20)
#define U8_INVERTER_FRAME_HEAD ((uint8_t)0xA5)
#define U8_INVERTER_FRAME_END ((uint8_t)0x5A)
#define U8_INVERTER_FRAME_LEN_MAX ((uint8_t)128)
#define U8_RECE_FRAME_LENGTH_UART_INVERTER ((uint8_t)138)
#define U8_INVERTER_SEND_CYCLE_MILLISECOND ((uint16_t)1000)
#define U8_INVERTER_VERSION_CYCLE_COUNT ((uint8_t)4)

typedef enum
{
    INVERTER_APP_PARAM = 0x1,
    INVERTER_APP_FREQ = 0x2,
} inverter_subfunc_e;

typedef enum
{
    INVERTER_OTA_APP_VER = 0x1,
    INVERTER_OTA_RESET = 0x2,
    INVERTER_OTA_JUMP_BOOT = 0x3,
    INVERTER_OTA_LOAD_START = 0x4,
    INVERTER_OTA_LOAD_END = 0x5,
    INVERTER_OTA_CHECKSUM = 0x6,
    INVERTER_OTA_JUMP_APP = 0x7,
    INVERTER_OTA_LOAD_DATA = 0x8,
    INVERTER_OTA_RUN_STATE = 0x9,
    INVERTER_OTA_BOOT_VER = 0xa,
} inverter_ota_subfunc_e;


typedef enum
{
    FRAME_ADDR_NONE = 0x0,
    FRAME_MAIN_ADDR = 0x1,
    FRAME_INVERTER_ADDR = 0x5,
} frame_addr_e;

// 帧格式
typedef enum
{
    INVERTER_FRAME_HEAD = 0, // 报文头
    INVERTER_FRAME_SRC, // 报文源地址
    INVERTER_FRAME_DEST, // 报文目的地址
    INVERTER_FRAME_FUNCBYTE1, // 报文功能号1
    INVERTER_FRAME_FUNCBYTE2, // 报文功能号2
    INVERTER_FRAME_FUNCBYTE3, // 报文功能号3
    INVERTER_FRAME_LENGTH, // 报文长度
    INVERTER_FRAME_DATA, // 正常报文数据
    INVERTER_FRAME_OVER, // 完成
    INVERTER_FRAME_MAX,
} InverterRProtocolState_em;

typedef struct
{
    uint8_t head;
    uint8_t srcAddr;
    uint8_t destAddr;
    uint8_t fb1;
    uint8_t fb2;
    uint8_t fb3;
    uint8_t len;
    uint8_t data[U8_INVERTER_FRAME_LEN_MAX];
    uint8_t crch;
    uint8_t crcl;
    uint8_t end;
} InverterFrame_st;

typedef struct
{
    uint8_t *sendPos;
    uint8_t *recvPos;
    uint16_t u16_SendCrcValue;
    uint16_t u16_ReceCrcValue;
    InverterFrame_st sendPacket;
    InverterFrame_st recvPacket;
    uint8_t u8_packetCnt;
    uint8_t u8_SendDataState; // 发送数据状态
    uint8_t u8_ReceDataState; // 接收数据状态
    uint8_t u8_SendLength; // 发送长度
    uint8_t u8_SendCount; // 发送计数
    uint8_t u8_ReceLength; // 接收长度
    uint8_t u8_ReceCount; // 接收计数
    uint8_t u8_SendTimeOutCount; // 发送超时计数
    uint8_t u8_ReceTimeOutCount; // 接收超时计数
    uint16_t u8_RecvDataErrorCount;
    uint16_t u16_SendCycleCount;
    uint16_t u16_HwVersion;
    uint16_t u16_AppVersion;
    uint16_t u16_AppCrc;
    uint16_t u16_BootVersion;
    uint16_t u16_BootCrc;
    bool f_SendIE; // 发送允许
    bool f_HaveFrameToSend;
    bool f_FrameSending; // 报文发送
    bool f_FrameReceiving;
    bool f_FrameHandle; // 报文处理
    bool b_ReceiveError;
    bool f_FrameSendTimeout;
    bool f_FrameRecvTimeout;
    bool b_BootVersion;
    bool b_AppVersion;
} UartInverterParm_st;

void Init_UartInverter(void);
void Handle_UartInverterSendData(void);
void Handle_UartInverterReceData(const uint8_t u8_recedata);
void Handle_UartInverterFrame(void);
void Handle_UartInverterOverTime(void);
bool Get_InverterCommErr(void);
uint8_t Get_CompFeedbackFreq(void);
uint8_t Get_CompErrorState(void);
uint16_t Get_CompBusVoltage(void);
uint16_t Get_CompPower(void);
uint32_t Get_CompBootVersion(void);
uint32_t Get_CompBootCrc(void);
uint32_t Get_CompAppVersion(void);
uint32_t Get_CompAppCrc(void);

#endif
