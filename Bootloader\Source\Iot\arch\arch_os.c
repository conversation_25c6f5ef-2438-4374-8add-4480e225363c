
#include "arch_os.h"


#if (defined(USER_OS_ENABLE) && USER_OS_ENABLE)
void arch_os_mutex_get(arch_os_mutex* mutex)
{
#if (defined(USER_OS_ENABLE) && USER_OS_ENABLE)
	pthread_mutex_lock(mutex);
#endif
}

void arch_os_mutex_put(arch_os_mutex* mutex)
{
#if (defined(USER_OS_ENABLE) && USER_OS_ENABLE)
	pthread_mutex_unlock(mutex);
#endif
}

void arch_os_mutex_init(arch_os_mutex* mutex)
{
#if (defined(USER_OS_ENABLE) && USER_OS_ENABLE)
	pthread_mutex_init(mutex, NULL);
#endif
}

void arch_os_mutex_deinit(arch_os_mutex *mutex)
{
#if (defined(USER_OS_ENABLE) && USER_OS_ENABLE)
	pthread_mutex_destroy(mutex);
#endif
}
#endif
void  udelay(unsigned int us)
{
	int i ,t = 0;
    for (i =0; i < us;i++)
	{
		t++;
	}
}

void arch_os_switch_to_boot(void)
{
	unsigned char illegalInstruction = 0xFF;
	void(*dummy)(void) = (void (*)(void))&illegalInstruction;
	dummy();
}

void APP_LOG(char *pbuf, ...)
{
    //log_printf(0, "%c\n", pbuf);
}

void APP_LOG_IOT(char *pbuf, ...)
{
    //log_printf(0, "%c\n", pbuf);
}


