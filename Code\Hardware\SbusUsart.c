/*!
 * @file
 * @brief Initialize MCU.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "Adpt_Usart.h"
#include "Core_Types.h"
#include "Sbus_Core.h"

sbus_master_st smaster;

void Init_SbusUsart(void)
{
    smaster.sbus_id = SBUS_TYPE_ID0;
    smaster.ops.sendOneData = LpUart0_SendOneData;
    Sbus_Master_Register(&smaster);
}

void Handle_SbusReceData(uint8_t data)
{
    Sbus_ReceData(&smaster, data);
}


