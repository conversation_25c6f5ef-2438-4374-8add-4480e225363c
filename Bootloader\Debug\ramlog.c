#include <stdio.h>
#include <string.h>
#include <stdint.h>
#include "syslog.h"
#include "Driver_Flash.h"

#define RAMLOG_MAGIC_NUMBER    (0x12345678)
#define RAMLOG_ADDR            (0x20006000)
#define RAMLOG_SIZE            (0x1FE0)

struct ramlog_header_s
{
  uint32_t          rl_magic;
  volatile uint32_t rl_head;
  volatile uint32_t boot_count;
  char              rl_buffer[];
};

static struct ramlog_header_s *rlh= (struct ramlog_header_s *)RAMLOG_ADDR;

static void ramlog_init(void)
{
    if(rlh->rl_magic != RAMLOG_MAGIC_NUMBER)
    {
        memset((void *)RAMLOG_ADDR, 0, RAMLOG_SIZE);
        rlh->rl_magic = RAMLOG_MAGIC_NUMBER;
    }
    else
    {
        rlh->boot_count++;
       if(rlh->rl_head >= RAMLOG_SIZE - sizeof(struct ramlog_header_s))
       {
           rlh->rl_head = 0;
       }
    }
}

int fputc(int ch, FILE *stream)
{
    rlh->rl_buffer[rlh->rl_head++] = ch;
    if(rlh->rl_head >= RAMLOG_SIZE - sizeof(struct ramlog_header_s))
    {
        rlh->rl_head = 0;
    }

    rlh->rl_buffer[rlh->rl_head] = '\0';
    if(ch == '\n')
    {
        printf("[BOOT]");
    }
    return ch;
}

void Panic(void)
{
    SaveOfflineLog(rlh->rl_buffer, RAMLOG_SIZE - sizeof(struct ramlog_header_s));
}

uint32_t GetBootCount(void)
{
    return rlh->boot_count;
}

void Debug_Init(void)
{
    ramlog_init();
    printf("[BOOT]ramlog start record boot log for %d times boot\n", rlh->boot_count);
    printf("the boot ver is %s crc:%x\n", BOOT_VERSION, CalcBootCrc());
}