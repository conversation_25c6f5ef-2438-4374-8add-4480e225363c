/*!
 * @file
 * @brief This file defines public constants, types and functions for the fan drive module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __SBUS_NFC_H
#define __SBUS_NFC_H

#include "Sbus_Core.h"

#define NFC_EVENT_POLL_TIMER_MS 100
#define NFC_ERROR_EVENT_POLL_TIMER_MS 1000
#define NFC_BOOT_MS 2000
#define NFC_MTYPE_SYNC_MINUTES 30
#define NFC_WIFI_REQUEST_TIMEOUT_100MS 30

typedef struct
{
    uint8_t model[FIREWARE_PROPERTY_MODEL_LEN];
    uint8_t did[FIREWARE_PROPERTY_DID_LEN];
    uint8_t mac[FIREWARE_PROPERTY_MAC_LEN];
    uint32_t error;
    uint32_t pfault;
    uint8_t door_alarm;
    uint16_t door_state;
    uint8_t wifi_state;
    uint8_t wifi_restore;
    uint8_t wifi_factory;
    uint8_t wifi_match;
    uint8_t mtype;
    uint8_t state;
    uint8_t clean_state;
    uint8_t force_state;
    uint8_t food_alarm;
    uint16_t short_time;
    uint8_t init;
} nfc_param_st;

typedef enum
{
    NFC_PROPERTY_TYPE_MODEL = 0,
    NFC_PROPERTY_TYPE_DID,
    NFC_PROPERTY_TYPE_MAC,
    NFC_PROPERTY_TYPE_MACHINE,
    NFC_PROPERTY_TYPE_FAULT,
    NFC_PROPERTY_TYPE_DOOR_ALARM,
    NFC_PROPERTY_TYPE_DOOR_STATE,
    NFC_PROPERTY_TYPE_SYSTEM_STATE,
    NFC_PROPERTY_TYPE_PASSIVE_FAULT,
    NFC_PROPERTY_TYPE_WIFI_STATE,
    NFC_PROPERTY_TYPE_WIFI_RESTORE,
    NFC_PROPERTY_TYPE_WIFI_FACTORY,
    NFC_PROPERTY_TYPE_WIFI_MATCH,
    NFC_PROPERTY_TYPE_FROCE_STATE,
    NFC_PROPERTY_TYPE_CLEAN_STATE,
    NFC_PROPERTY_TYPE_FOOD_ALARM,
    NFC_PROPERTY_TYPE_SHORT_TIME,
    NFC_PROPERTY_TYPE_INIT,
    NFC_PROPERTY_TYPE_MAX
} nfc_property_type_e;

typedef struct
{
    sbus_slave_st slave;
    fireware_frame_st frame;
    uint16_t poll_count;
    uint16_t boot_count;
    uint16_t hwversion;
    uint16_t appversion;
    uint16_t appcrc;
    uint16_t bootVersion;
    uint16_t bootCrc;
    bool b_appversion;
    bool b_bootversion;
    bool dirty;
    bool response;
    bool init;
    bool alldirty;
    bool b_appsn;
    uint16_t mtype_timer;
    uint8_t wifi_timer;
} sbus_nfc_st;

void Init_SbusNfc(void);
void Handle_Nfc_Overtime(void);
void Update_Nfc_Param(void);
bool Get_NfcCommErr(void);
int8_t GetNfcPropertyValue(nfc_property_type_e type, void *data);
int8_t SetNfcPropertyValue(nfc_property_type_e type, void *data);
void Handle_Nfc_Request();
bool GetNfcPropertyState(nfc_property_type_e type);
uint32_t Get_NfcBootVersion(void);
uint32_t Get_NfcBootCrc(void);
uint32_t Get_NfcAppVersion(void);
uint32_t Get_NfcAppCrc(void);
sbus_slave_stats *Get_NfcPacketStats(void);
#endif
