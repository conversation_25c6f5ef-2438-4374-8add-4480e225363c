/*!
 * @file
 * @brief This file defines public constants, types and functions for the compressor drive module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef DRIVER_COMPFREQUENCY_H
#define DRIVER_COMPFREQUENCY_H

#include <stdint.h>
#include <stdbool.h>

#define U16_COMP_FREQINDEX_MIN (uint16_t)100
#define U16_COMP_FREQINDEX_MAX (uint16_t)500

#define U8_COMP_FREQUENCY_UPDATEINTERVAL_SECS (uint8_t)2
#define U8_TIME2MINUTE (uint8_t)(2 * 60)
#define U8_TIME4MINUTE (uint8_t)(4 * 60)

#define FREQ_0HZ (uint8_t)0
#define FREQ_31HZ (uint8_t)1  // 930
#define FREQ_41HZ (uint8_t)2  // 1230
#define FREQ_44HZ (uint8_t)3  // 1320
#define FREQ_51HZ (uint8_t)4  // 1530
#define FREQ_54HZ (uint8_t)5  // 1620
#define FREQ_61HZ (uint8_t)6  // 1830
#define FREQ_76HZ (uint8_t)7  // 2280
#define FREQ_108HZ (uint8_t)8 // 3240
#define FREQ_118HZ (uint8_t)9 // 3540
#define FREQ_132HZ (uint8_t)10 // 3960
#define FREQ_147HZ (uint8_t)11 // 4410
#define FREQ_MAX (uint8_t)12

typedef struct
{
    uint16_t u16_CompFreq;
    uint16_t u16_CompFreqAdjust;
    uint8_t u8_OnTimeSecs;
    uint8_t u8_CompFreqIndex;

    bool b_Init;
    bool b_ForceOff;
    bool b_WakeupFlag;
} DriveCompFreq_st;

void Driver_CompFreqInit(void);
void Driver_CompFreqForce(bool b_State);
void Driver_CompFreqSet(uint8_t u8_CompFreqIndex);
void Driver_CompAdjustFreqSet(uint16_t u16_CompFreq);
void Set_CompFreqMicroAdjustParm(uint16_t comp_freq_adjust_value);
uint16_t Get_CompFreqMicroAdjustParm(void);
uint16_t Get_CompFreq(void);
uint16_t Get_CompFreqStayMinutes(void);
void Clear_CompFreqStayMinutes(void);
#endif
