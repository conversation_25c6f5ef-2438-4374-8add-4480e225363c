/**
 *******************************************************************************
 * @file  adt.c
 * @brief This file provides - functions to manage the ADT.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-03-15       MADS            First version
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2022, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 *******************************************************************************
 */

/******************************************************************************
 * Include files
 ******************************************************************************/
#include "adt.h"

/**
 * @addtogroup HC32L186_DDL 驱动库
 * @{
 */

/**
 * @defgroup DDL_ADT ADT模块驱动库
 * @brief ADT Driver Library ADT模块驱动库
 * @{
 */


/******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/
#define IS_VALID_ADT_STATE(x)         (  AdtCMAF == (x)||\
                                         AdtCMBF == (x)||\
                                         AdtCMCF == (x)||\
                                         AdtCMDF == (x)||\
                                         AdtOVFF == (x)||\
                                         AdtUDFF == (x)||\
                                         AdtDTEF == (x)||\
                                         AdtCMSAUF == (x)||\
                                         AdtCMSADF == (x)||\
                                         AdtCMSBUF == (x)||\
                                         AdtCMSBDF == (x)||\
                                         AdtCntDir == (x) )
#define ADTIM_HW_STASTPCLR_EN            31u
#define ADTIM_HW_STASTPCLR_DIS           0x7FFFFFFFu
#define ADTIM_SS_TIM4                    1u
#define ADTIM_SS_TIM5                    2u
#define ADTIM_SS_TIM6                    4u
#define ADTIM_PORT_BKE_NUM               14u
/******************************************************************************
 * Global variable definitions (declared in header file with 'extern')        *
 ******************************************************************************/

/******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/

/******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/

/*****************************************************************************
 * Function implementation - global ('extern') and local ('static')
 *****************************************************************************/
/**
 * @defgroup ADT_Global_Functions ADT全局函数定义
 * @{
 */


/**
 * @brief 配置中断
 * @param [in]  ADTx        ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  enAdtIrq    中断类型 @ref en_adt_irq_type_t
 * @param [in]  bEn         TRUE:中断使能/FALSE:禁止
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - 其他       设定失败
 */
en_result_t Adt_CfgIrq(M0P_ADTIM_TypeDef *ADTx, en_adt_irq_type_t enAdtIrq, boolean_t bEn)
{
    uint32_t u32Val;

    u32Val = ADTx->ICONR;
    if (bEn)
    {
        u32Val |= 1u << enAdtIrq;
    }
    else
    {
        u32Val &= ~(1u << enAdtIrq);
    }
    ADTx->ICONR = u32Val;
    return Ok;
}

/**
 * @brief 获取中断标志
 * @param [in]  ADTx       ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  enAdtIrq   中断类型 @ref en_adt_irq_type_t
 * @retval      boolean_t
 *              - TRUE: 发生中断
 *              - FALSE: 未发生中断
 */
boolean_t Adt_GetIrqFlag(M0P_ADTIM_TypeDef *ADTx, en_adt_irq_type_t enAdtIrq)
{
    uint32_t u32Val;
    boolean_t bEn;

    ASSERT(IS_VALID_ADT_UNIT(enAdtUnit));

    u32Val = ADTx->IFR;
    bEn = (u32Val >> enAdtIrq) & 0x1u;

    return bEn;
}

/**
 * @brief 清除中断标志
 *
 * @param [in]  ADTx       ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  enAdtIrq   中断类型 @ref en_adt_irq_type_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - 其他       设定失败
 */
en_result_t Adt_ClearIrqFlag(M0P_ADTIM_TypeDef *ADTx, en_adt_irq_type_t enAdtIrq)
{
    ADTx->ICLR = ~(1u << enAdtIrq);
    return Ok;
}

/**
 * @brief 清除所有中断标志
 * @param [in]  ADTx    ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:   设定成功
 *              - 其他  设定失败
 */
en_result_t Adt_ClearAllIrqFlag(M0P_ADTIM_TypeDef *ADTx)
{
    ADTx->ICLR = 0u;
    return Ok;
}

/**
 * @brief 配置硬件递加事件
 * @param [in]  ADTx    ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  enAdtHwCntUp   硬件递加事件 @ref en_adt_hw_cnt_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - 其他       设定失败
 */
en_result_t Adt_CfgHwCntUp(M0P_ADTIM_TypeDef *ADTx, en_adt_hw_cnt_t enAdtHwCntUp)
{
    uint32_t u32Val;

    if (AdtHwCntMax <= enAdtHwCntUp)
    {
        return ErrorInvalidParameter;
    }

    u32Val = ADTx->HCUPR;
    ADTx->HCUPR = u32Val | (1u << enAdtHwCntUp);
    return Ok;
}

/**
 * @brief 清除硬件递加事件
 * @param [in]  ADTx     ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:    设定成功
 *              - 其他   设定失败
 */
en_result_t Adt_ClearHwCntUp(M0P_ADTIM_TypeDef *ADTx)
{
    ADTx->HCUPR = 0u;
    return Ok;
}


/**
 * @brief 配置硬件递减事件
 * @param [in]  ADTx     ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  enAdtHwCntDwn   硬件递减事件 @ref en_adt_hw_cnt_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_CfgHwCntDwn(M0P_ADTIM_TypeDef *ADTx, en_adt_hw_cnt_t enAdtHwCntDwn)
{
    uint32_t u32Val;

    if (AdtHwCntMax <= enAdtHwCntDwn)
    {
        return ErrorInvalidParameter;
    }

    u32Val = ADTx->HCDOR;
    ADTx->HCDOR = u32Val | (1u << enAdtHwCntDwn);
    return Ok;
}

/**
 * @brief 清除硬件递减事件
 * @param [in]  ADTx     ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:    设定成功
 *              - 其他   设定失败
 */
en_result_t Adt_ClearHwCntDwn(M0P_ADTIM_TypeDef *ADTx)
{
    ADTx->HCDOR = 0u;
    return Ok;
}

/**
 * @brief 配置硬件启动事件
 * @param [in]  ADTx         ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  enAdtHwStart 硬件启动事件 @ref en_adt_hw_trig_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_CfgHwStart(M0P_ADTIM_TypeDef *ADTx, en_adt_hw_trig_t enAdtHwStart)
{
    uint32_t u32Val;


    if (AdtHwTrigEnd <= enAdtHwStart)
    {
        return ErrorInvalidParameter;
    }
    u32Val = ADTx->HSTAR;
    ADTx->HSTAR = u32Val | (1u << enAdtHwStart);
    return Ok;
}

/**
 * @brief 清除硬件启动事件
 * @param [in]  ADTx     ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:    设定成功
 *              - 其他   设定失败
 */
en_result_t Adt_ClearHwStart(M0P_ADTIM_TypeDef *ADTx)
{
    ADTx->HSTAR = 0u;
    return Ok;
}

/**
 * @brief 使能硬件启动
 * @param [in]  ADTx      ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:     设定成功
 *              - 其他    设定失败
 */
en_result_t Adt_EnableHwStart(M0P_ADTIM_TypeDef *ADTx)
{
    uint32_t u32Val;

    u32Val = ADTx->HSTAR;
    ADTx->HSTAR = u32Val | (1u << ADTIM_HW_STASTPCLR_EN);
    return Ok;
}

/**
 * @brief 除能硬件启动
 * @param [in]  ADTx    ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:   设定成功
 *              - 其他  设定失败
 */
en_result_t Adt_DisableHwStart(M0P_ADTIM_TypeDef *ADTx)
{
    uint32_t u32Val;

    u32Val = ADTx->HSTAR;
    ADTx->HSTAR = u32Val & ADTIM_HW_STASTPCLR_DIS;
    return Ok;
}

/**
 * @brief 配置硬件停止事件
 *
 * @param [in]  ADTx    ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  enAdtHwStop  硬件停止事件 @ref en_adt_hw_trig_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_CfgHwStop(M0P_ADTIM_TypeDef *ADTx, en_adt_hw_trig_t enAdtHwStop)
{
    uint32_t u32Val;

    if (AdtHwTrigEnd <= enAdtHwStop)
    {
        return ErrorInvalidParameter;
    }

    u32Val = ADTx->HSTPR;
    ADTx->HSTPR = u32Val | (1u << enAdtHwStop);
    return Ok;
}

/**
 * @brief 清除硬件停止事件
 * @param [in]  ADTx    ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:   设定成功
 *              - 其他  设定失败
 */
en_result_t Adt_ClearHwStop(M0P_ADTIM_TypeDef *ADTx)
{
    ADTx->HSTPR = 0u;
    return Ok;
}

/**
 * @brief 使能硬件停止
 *
 * @param [in]  ADTx    ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:   设定成功
 *              - 其他  设定失败
 */
en_result_t Adt_EnableHwStop(M0P_ADTIM_TypeDef *ADTx)
{
    uint32_t u32Val;

    u32Val = ADTx->HSTPR;
    ADTx->HSTPR = u32Val | (1u << ADTIM_HW_STASTPCLR_EN);
    return Ok;
}

/**
 * @brief 除能硬件停止
 *
 * @param [in]  ADTx     ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:    设定成功
 *              - 其他   设定失败
 */
en_result_t Adt_DisableHwStop(M0P_ADTIM_TypeDef *ADTx)
{
    uint32_t u32Val;

    u32Val = ADTx->HSTPR;
    ADTx->HSTPR = u32Val & ADTIM_HW_STASTPCLR_DIS;
    return Ok;
}

/**
 * @brief 配置硬件清零事件
 *
 * @param [in]  ADTx         ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  enAdtHwClear 硬件清零事件 @ref en_adt_hw_trig_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_CfgHwClear(M0P_ADTIM_TypeDef *ADTx, en_adt_hw_trig_t enAdtHwClear)
{
    uint32_t u32Val;

    if (AdtHwTrigEnd <= enAdtHwClear)
    {
        return ErrorInvalidParameter;
    }

    u32Val = ADTx->HCELR;
    ADTx->HCELR = u32Val | (1u << enAdtHwClear);
    return Ok;
}

/**
 * @brief 清除硬件清零事件
 * @param [in]  ADTx     ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:    设定成功
 *              - 其他   设定失败
 */
en_result_t Adt_ClearHwClear(M0P_ADTIM_TypeDef *ADTx)
{
    ADTx->HCELR = 0u;
    return Ok;
}

/**
 * @brief 使能硬件清零
 * @param [in]  ADTx     ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:    设定成功
 *              - 其他   设定失败
 */
en_result_t Adt_EnableHwClear(M0P_ADTIM_TypeDef *ADTx)
{
    uint32_t u32Val;

    u32Val = ADTx->HCELR;
    ADTx->HCELR = u32Val | (1u << ADTIM_HW_STASTPCLR_EN);
    return Ok;
}

/**
 * @brief 除能硬件清零
 * @param [in]  ADTx     ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:    设定成功
 *              - 其他   设定失败
 */
en_result_t Adt_DisableHwClear(M0P_ADTIM_TypeDef *ADTx)
{
    uint32_t u32Val;

    u32Val = ADTx->HCELR;
    ADTx->HCELR = u32Val & ADTIM_HW_STASTPCLR_DIS;
    return Ok;
}

/**
 * @brief 配置硬件捕获A事件
 * @param [in]  ADTx         ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  enAdtHwCaptureA  硬件捕获A事件选择 @ref en_adt_hw_trig_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_CfgHwCaptureA(M0P_ADTIM_TypeDef *ADTx, en_adt_hw_trig_t enAdtHwCaptureA)
{
    uint32_t u32Val;

    if (AdtHwTrigEnd <= enAdtHwCaptureA)
    {
        return ErrorInvalidParameter;
    }

    u32Val = ADTx->HCPAR;
    ADTx->HCPAR = u32Val | (1u << enAdtHwCaptureA);
    ADTx->PCONR_f.CAPCA = 1u;
    return Ok;
}

/**
 * @brief 清除硬件捕获A事件
 * @param [in]  ADTx     ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:    设定成功
 *              - 其他   设定失败
 */
en_result_t Adt_ClearHwCaptureA(M0P_ADTIM_TypeDef *ADTx)
{
    ADTx->HCPAR = 0u;
    return Ok;
}

/**
 * @brief 配置硬件捕获B事件
 *
 * @param [in]  ADTx         ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  enAdtHwCaptureB   硬件捕获B事件选择 @ref en_adt_hw_trig_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_CfgHwCaptureB(M0P_ADTIM_TypeDef *ADTx, en_adt_hw_trig_t enAdtHwCaptureB)
{
    uint32_t u32Val;

    if (AdtHwTrigEnd <= enAdtHwCaptureB)
    {
        return ErrorInvalidParameter;
    }

    u32Val = ADTx->HCPBR;
    ADTx->HCPBR =  u32Val | (1u << enAdtHwCaptureB);
    ADTx->PCONR_f.CAPCB = 1u;
    return Ok;
}

/**
 * @brief 清除硬件捕获B事件
 * @param [in]  ADTx     ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:    设定成功
 *              - 其他:  设定失败
 */
en_result_t Adt_ClearHwCaptureB(M0P_ADTIM_TypeDef *ADTx)
{
    ADTx->HCPBR = 0u;
    return Ok;
}

/**
 * @brief 软件同步开始
 * @param [in]  pstcAdtSwSyncStart   软件同步开始指针 @ref stc_adt_sw_sync_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_SwSyncStart(stc_adt_sw_sync_t *pstcAdtSwSyncStart)
{
    uint32_t u32Val = 0u;

    if (NULL == pstcAdtSwSyncStart)
    {
        return ErrorInvalidParameter;
    }

    if (pstcAdtSwSyncStart->bAdTim4)
    {
        u32Val |= ADTIM_SS_TIM4;
    }
    if (pstcAdtSwSyncStart->bAdTim5)
    {
        u32Val |= ADTIM_SS_TIM5;
    }
    if (pstcAdtSwSyncStart->bAdTim6)
    {
        u32Val |= ADTIM_SS_TIM6;
    }

    M0P_ADTIM4->SSTAR = u32Val;
    return Ok;
}

/**
 * @brief 软件同步停止
 * @param [in]  pstcAdtSwSyncStop   软件同步停止指针 @ref stc_adt_sw_sync_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_SwSyncStop(stc_adt_sw_sync_t *pstcAdtSwSyncStop)
{
    uint32_t u32Val = 0u;

    if (NULL == pstcAdtSwSyncStop)
    {
        return ErrorInvalidParameter;
    }

    if (pstcAdtSwSyncStop->bAdTim4)
    {
        u32Val |= ADTIM_SS_TIM4;
    }
    if (pstcAdtSwSyncStop->bAdTim5)
    {
        u32Val |= ADTIM_SS_TIM5;
    }
    if (pstcAdtSwSyncStop->bAdTim6)
    {
        u32Val |= ADTIM_SS_TIM6;
    }

    M0P_ADTIM4->SSTPR = u32Val;
    return Ok;
}

/**
 * @brief 软件同步清零
 * @param [in]  pstcAdtSwSyncClear   软件同步清零指针 @ref stc_adt_sw_sync_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_SwSyncClear(stc_adt_sw_sync_t *pstcAdtSwSyncClear)
{
    uint32_t u32Val = 0u;

    if (NULL == pstcAdtSwSyncClear)
    {
        return ErrorInvalidParameter;
    }

    if (pstcAdtSwSyncClear->bAdTim4)
    {
        u32Val |= ADTIM_SS_TIM4;
    }
    if (pstcAdtSwSyncClear->bAdTim5)
    {
        u32Val |= ADTIM_SS_TIM5;
    }
    if (pstcAdtSwSyncClear->bAdTim6)
    {
        u32Val |= ADTIM_SS_TIM6;
    }

    M0P_ADTIM4->SCLRR = u32Val;
    return Ok;
}

/**
 * @brief 获取软件同步运行状态
 * @param [in]  pstcAdtSwSyncState    ADV Timer软件同步运行状态指针 @ref stc_adt_sw_sync_t
 * @retval      en_result_t
 *              - Ok:        获取成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_GetSwSyncState(stc_adt_sw_sync_t *pstcAdtSwSyncState)
{
    if (NULL == pstcAdtSwSyncState)
    {
        return ErrorInvalidParameter;
    }

    if (M0P_ADTIM4->SSTAR & ADTIM_SS_TIM4)
    {
        pstcAdtSwSyncState->bAdTim4 = TRUE;
    }
    else
    {
        pstcAdtSwSyncState->bAdTim4 = FALSE;
    }
    if (M0P_ADTIM4->SSTAR & ADTIM_SS_TIM5)
    {
        pstcAdtSwSyncState->bAdTim5 = TRUE;
    }
    else
    {
        pstcAdtSwSyncState->bAdTim5 = FALSE;
    }
    if (M0P_ADTIM4->SSTAR & ADTIM_SS_TIM6)
    {
        pstcAdtSwSyncState->bAdTim6 = TRUE;
    }
    else
    {
        pstcAdtSwSyncState->bAdTim6 = FALSE;
    }
    return Ok;
}

/**
 * @brief AOS触发配置
 * @param [in]  pstcAdtAosTrigCfg 触发配置指针 @ref stc_adt_aos_trig_cfg_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_AosTrigCfg(stc_adt_aos_trig_cfg_t *pstcAdtAosTrigCfg)
{
    if (NULL == pstcAdtAosTrigCfg)
    {
        return ErrorInvalidParameter;
    }

    M0P_ADTIM4->ITRIG_f.IAOS0S = pstcAdtAosTrigCfg->enAos0TrigSrc;
    M0P_ADTIM4->ITRIG_f.IAOS1S = pstcAdtAosTrigCfg->enAos1TrigSrc;
    M0P_ADTIM4->ITRIG_f.IAOS2S = pstcAdtAosTrigCfg->enAos2TrigSrc;
    M0P_ADTIM4->ITRIG_f.IAOS3S = pstcAdtAosTrigCfg->enAos3TrigSrc;
    return Ok;
}

/**
 * @brief 中断触发配置
 *
 * @param [in]  ADTx         ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  pstcAdtIrqTrigCfg   触发配置指针 @ref stc_adt_irq_trig_cfg_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_IrqTrigCfg(M0P_ADTIM_TypeDef *ADTx, stc_adt_irq_trig_cfg_t *pstcAdtIrqTrigCfg)
{
    if (NULL == pstcAdtIrqTrigCfg)
    {
        return ErrorInvalidParameter;
    }

    ADTx->CR_f.CMAE = pstcAdtIrqTrigCfg->bAdtCntMatchATrigEn;
    ADTx->CR_f.CMBE = pstcAdtIrqTrigCfg->bAdtCntMatchBTrigEn;
    ADTx->CR_f.CMCE = pstcAdtIrqTrigCfg->bAdtCntMatchCTrigEn;
    ADTx->CR_f.CMDE = pstcAdtIrqTrigCfg->bAdtCntMatchDTrigEn;
    ADTx->CR_f.OVFE = pstcAdtIrqTrigCfg->bAdtOverFlowTrigEn;
    ADTx->CR_f.UDFE = pstcAdtIrqTrigCfg->bAdtUnderFlowTrigEn;
    ADTx->CR_f.CMSAE = pstcAdtIrqTrigCfg->bAdtSpecilMatchATrigEn;
    ADTx->CR_f.CMSBE = pstcAdtIrqTrigCfg->bAdtSpecilMatchBTrigEn;
    ADTx->CR_f.DMA_G_CMA = pstcAdtIrqTrigCfg->bAdtCntMatchATrigDmaEn;
    ADTx->CR_f.DMA_G_CMB = pstcAdtIrqTrigCfg->bAdtCntMatchBTrigDmaEn;
    ADTx->CR_f.DMA_G_CMC = pstcAdtIrqTrigCfg->bAdtCntMatchCTrigDmaEn;
    ADTx->CR_f.DMA_G_CMD = pstcAdtIrqTrigCfg->bAdtCntMatchDTrigDmaEn;
    ADTx->CR_f.DMA_G_OVF = pstcAdtIrqTrigCfg->bAdtOverFlowTrigDmaEn;
    ADTx->CR_f.DMA_G_UDF = pstcAdtIrqTrigCfg->bAdtUnderFlowTrigDmaEn;
    ADTx->CR_f.DMA_S_CMA = pstcAdtIrqTrigCfg->bAdtSpecilMatchATrigDmaEn;
    ADTx->CR_f.DMA_S_CMB = pstcAdtIrqTrigCfg->bAdtSpecilMatchBTrigDmaEn;

    return Ok;
}

/**
 * @brief 端口触发配置
 * @param [in]  enAdtTrigPort       触发端口 @ref en_adt_trig_port_t
 * @param [in]  pstcAdtPortTrigCfg  触发配置指针 @ref stc_adt_port_trig_cfg_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_PortTrigCfg(en_adt_trig_port_t enAdtTrigPort,
                            stc_adt_port_trig_cfg_t *pstcAdtPortTrigCfg)
{
    if (NULL == pstcAdtPortTrigCfg)
    {
        return ErrorInvalidParameter;
    }

    switch (enAdtTrigPort)
    {
        case AdtTrigA:
            M0P_ADTIM4->TTRIG_f.TRIGAS = pstcAdtPortTrigCfg->enTrigSrc;
            M0P_ADTIM4->FCONR_f.NOFIENTA = pstcAdtPortTrigCfg->bFltEn;
            M0P_ADTIM4->FCONR_f.NOFICKTA = pstcAdtPortTrigCfg->enFltClk;
            break;

        case AdtTrigB:
            M0P_ADTIM4->TTRIG_f.TRIGBS = pstcAdtPortTrigCfg->enTrigSrc;
            M0P_ADTIM4->FCONR_f.NOFIENTB = pstcAdtPortTrigCfg->bFltEn;
            M0P_ADTIM4->FCONR_f.NOFICKTB = pstcAdtPortTrigCfg->enFltClk;
            break;

        case AdtTrigC:
            M0P_ADTIM4->TTRIG_f.TRIGCS = pstcAdtPortTrigCfg->enTrigSrc;
            M0P_ADTIM4->FCONR_f.NOFIENTC = pstcAdtPortTrigCfg->bFltEn;
            M0P_ADTIM4->FCONR_f.NOFICKTC = pstcAdtPortTrigCfg->enFltClk;
            break;

        case AdtTrigD:
            M0P_ADTIM4->TTRIG_f.TRIGDS = pstcAdtPortTrigCfg->enTrigSrc;
            M0P_ADTIM4->FCONR_f.NOFIENTD = pstcAdtPortTrigCfg->bFltEn;
            M0P_ADTIM4->FCONR_f.NOFICKTD = pstcAdtPortTrigCfg->enFltClk;
            break;

        default:
            return ErrorInvalidParameter;
    }

    return Ok;
}

/**
 * @brief CHxX端口配置
 * @param   [in]  ADTx       ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param   [in]  enAdtCHxXPort    CHxX端口 @ref en_adt_CHxX_port_t
 * @param   [in]  pstcAdtCHxXCfg   CHxX端口配置指针 @ref stc_adt_CHxX_port_cfg_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_CHxXPortCfg(M0P_ADTIM_TypeDef *ADTx,
                            en_adt_CHxX_port_t enAdtCHxXPort,
                            stc_adt_CHxX_port_cfg_t *pstcAdtCHxXCfg)
{
    if (NULL == pstcAdtCHxXCfg)
    {
        return ErrorInvalidParameter;
    }

    switch (enAdtCHxXPort)
    {
        case AdtCHxA:
            ADTx->PCONR_f.CAPCA = pstcAdtCHxXCfg->enCap;
            ADTx->PCONR_f.STACA = pstcAdtCHxXCfg->enStaOut;
            ADTx->PCONR_f.STPCA = pstcAdtCHxXCfg->enStpOut;
            ADTx->PCONR_f.STASTPSA = pstcAdtCHxXCfg->enStaStp;
            ADTx->PCONR_f.CMPCA = pstcAdtCHxXCfg->enCmpc;
            ADTx->PCONR_f.PERCA = pstcAdtCHxXCfg->enPerc;
            ADTx->PCONR_f.OUTENA = pstcAdtCHxXCfg->bOutEn;
            ADTx->PCONR_f.DISSELA = pstcAdtCHxXCfg->enDisSel;
            ADTx->PCONR_f.DISVALA = pstcAdtCHxXCfg->enDisVal;
            ADTx->FCONR_f.NOFIENGA = pstcAdtCHxXCfg->bFltEn;
            ADTx->FCONR_f.NOFICKGA = pstcAdtCHxXCfg->enFltClk;
            break;

        case AdtCHxB:
            ADTx->PCONR_f.CAPCB = pstcAdtCHxXCfg->enCap;
            ADTx->PCONR_f.STACB = pstcAdtCHxXCfg->enStaOut;
            ADTx->PCONR_f.STPCB = pstcAdtCHxXCfg->enStpOut;
            ADTx->PCONR_f.STASTPSB = pstcAdtCHxXCfg->enStaStp;
            ADTx->PCONR_f.CMPCB = pstcAdtCHxXCfg->enCmpc;
            ADTx->PCONR_f.PERCB = pstcAdtCHxXCfg->enPerc;
            ADTx->PCONR_f.OUTENB = pstcAdtCHxXCfg->bOutEn;
            ADTx->PCONR_f.DISSELB = pstcAdtCHxXCfg->enDisSel;
            ADTx->PCONR_f.DISVALB = pstcAdtCHxXCfg->enDisVal;
            ADTx->FCONR_f.NOFIENGB = pstcAdtCHxXCfg->bFltEn;
            ADTx->FCONR_f.NOFICKGB = pstcAdtCHxXCfg->enFltClk;
            break;

        default:
            return ErrorInvalidParameter;
    }

    return Ok;
}

/**
 * @brief 使能端口刹车
 * @param [in]  port         端口 @ref en_adt_ttrig_trigxs_t
 * @param [in]  pstcAdtBrkPtCfg   端口刹车配置指针 @ref stc_adt_break_port_cfg_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 * @note 在无效条件3配置函数Adt_Disable3Cfg()内被调用
 */
en_result_t Adt_EnableBrakePort(uint8_t port, stc_adt_break_port_cfg_t *pstcAdtBrkPtCfg)
{
    uint32_t u32Val;

    if (NULL == pstcAdtBrkPtCfg)
    {
        return ErrorInvalidParameter;
    }

    u32Val = M0P_ADTIM4->PTBKP;
    u32Val &= ~(1u << port);
    M0P_ADTIM4->PTBKP = u32Val | (pstcAdtBrkPtCfg->enPol << port);

    u32Val = M0P_ADTIM4->PTBKS;
    M0P_ADTIM4->PTBKS = u32Val | (1u << port);

    return Ok;
}

/**
 * @brief 清除端口刹车
 * @retval  none
 */
void Adt_ClearBrakePort(void)
{
    M0P_ADTIM4->PTBKS = 0u;
}

/**
 * @brief 无效条件3配置
 * @param [in]  pstcAdtDisable3   无效条件3配置指针 @ref stc_adt_disable_3_cfg_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_Disable3Cfg(stc_adt_disable_3_cfg_t *pstcAdtDisable3)
{
    uint8_t i;

    if (NULL == pstcAdtDisable3)
    {
        return ErrorInvalidParameter;
    }

    Adt_ClearBrakePort();
    for (i = 0u; i <= ADTIM_PORT_BKE_NUM; i++)
    {
        if (TRUE == pstcAdtDisable3->stcBrkPtCfg[i].bPortEn)
        {
            Adt_EnableBrakePort(i, &pstcAdtDisable3->stcBrkPtCfg[i]);
        }
    }

    M0P_ADTIM4->AOSSR_f.BFILTEN = pstcAdtDisable3->bFltEn;
    M0P_ADTIM4->AOSSR_f.BFILTS = pstcAdtDisable3->enFltClk;

    return Ok;
}

/**
 * @brief 软件刹车使能或禁止 (仅适用于无效条件3使能的情况下)
 * @param [in]  bSwBrk   TRUE:软件刹车使能/FALSE:禁止
 * @retval      en_result_t
 *              - Ok:    设定成功
 *              - 其他:  设定失败
 */
en_result_t Adt_SwBrake(boolean_t bSwBrk)
{
    M0P_ADTIM4->AOSSR_f.SOFTBK = bSwBrk;

    return Ok;
}

/**
 * @brief 获取端口刹车标志
 * @retval  boolean_t
 *          - TRUE:    端口刹车标志置起
 *          - FALSE:   端口刹车标志未置起
 */
boolean_t Adt_GetPortBrakeFlag(void)
{
    return M0P_ADTIM4->AOSSR_f.FBRAKE;
}

/**
 * @brief 清除端口刹车标志
 * @retval  none
 */
void Adt_ClearPortBrakeFlag(void)
{
    M0P_ADTIM4->AOSCL_f.FBRAKE = 0;
}

/**
 * @brief 无效条件1配置
 * @param [in]  pstcAdtDisable1   无效条件1配置指针@ref stc_adt_disable_1_cfg_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_Disable1Cfg(stc_adt_disable_1_cfg_t *pstcAdtDisable1)
{

    if (NULL == pstcAdtDisable1)
    {
        return ErrorInvalidParameter;
    }

    M0P_ADTIM4->AOSSR_f.SMH2 = pstcAdtDisable1->bTim6OutSH;
    M0P_ADTIM4->AOSSR_f.SMH1 = pstcAdtDisable1->bTim5OutSH;
    M0P_ADTIM4->AOSSR_f.SMH0 = pstcAdtDisable1->bTim4OutSH;
    M0P_ADTIM4->AOSSR_f.SML2 = pstcAdtDisable1->bTim6OutSL;
    M0P_ADTIM4->AOSSR_f.SML1 = pstcAdtDisable1->bTim5OutSL;
    M0P_ADTIM4->AOSSR_f.SML0 = pstcAdtDisable1->bTim4OutSL;

    return Ok;
}

/**
 * @brief 获取同高同低刹车标志
 * @retval  boolean_t
 *          - TRUE:    同高同低刹车标志置起
 *          - FALSE:   同高同低刹车标志未置起
 */
boolean_t Adt_GetSameBrakeFlag(void)
{
    return M0P_ADTIM4->AOSSR_f.FSAME;
}

/**
 * @brief 清除同高同低刹车标志
 * @retval  none
 */
void Adt_ClearSameBrakeFlag(void)
{
    M0P_ADTIM4->AOSCL_f.FSAME = 0u;
}

/**
 * @brief PWM展频配置
 * @param [in]  ADTx         ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  pstcAdtPwmDitherCfg   PWM展频配置指针 @ref stc_adt_pwm_dither_cfg_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_PwmDitherCfg(M0P_ADTIM_TypeDef *ADTx, stc_adt_pwm_dither_cfg_t *pstcAdtPwmDitherCfg)
{


    if (NULL == pstcAdtPwmDitherCfg)
    {
        return ErrorInvalidParameter;
    }

    ADTx->CR_f.DITENS = pstcAdtPwmDitherCfg->enAdtPDType;
    ADTx->CR_f.DITENB = pstcAdtPwmDitherCfg->bTimxBPDEn;
    ADTx->CR_f.DITENA = pstcAdtPwmDitherCfg->bTimxAPDEn;

    return Ok;
}

/**
 * @brief ADT初始化
 *
 * @param [in]  ADTx    ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  pstcAdtBaseCntCfg   计数配置指针 @ref stc_adt_basecnt_cfg_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_Init(M0P_ADTIM_TypeDef *ADTx, stc_adt_basecnt_cfg_t *pstcAdtBaseCntCfg)
{


    if (NULL == pstcAdtBaseCntCfg)
    {
        return ErrorInvalidParameter;
    }

    if (AdtTriangleModeB < pstcAdtBaseCntCfg->enCntMode)
    {
        return ErrorInvalidParameter;
    }

    ADTx->GCONR_f.MODE = pstcAdtBaseCntCfg->enCntMode;
    ADTx->GCONR_f.DIR = pstcAdtBaseCntCfg->enCntDir;
    ADTx->GCONR_f.CKDIV = pstcAdtBaseCntCfg->enCntClkDiv;

    return Ok;
}

/**
 * @brief ADT Deinit
 * @param [in]  ADTx     ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:    设定成功
 *              - 其他:  设定失败
 */
en_result_t Adt_DeInit(M0P_ADTIM_TypeDef *ADTx)
{
    ADTx->GCONR_f.START = 0u;
    ADTx->CNTER = 0u;
    ADTx->PCONR = 0u;
    ADTx->GCONR = 0x00000100u;
    ADTx->DCONR = 0u;
    ADTx->ICONR = 0u;
    ADTx->BCONR = 0u;
    ADTx->FCONR = 0u;
    ADTx->VPERR = 0u;
    ADTx->PERAR = 0xFFFFu;
    ADTx->PERBR = 0xFFFFu;
    ADTx->GCMAR = 0xFFFFu;
    ADTx->GCMBR = 0xFFFFu;
    ADTx->GCMCR = 0xFFFFu;
    ADTx->GCMDR = 0xFFFFu;
    ADTx->DTDAR = 0xFFFFu;
    ADTx->DTUAR = 0xFFFFu;
    ADTx->HSTAR = 0u;
    ADTx->HSTPR = 0u;
    ADTx->HCELR = 0u;
    ADTx->HCPAR = 0u;
    ADTx->HCPBR = 0u;
    ADTx->HCUPR = 0u;
    ADTx->HCDOR = 0u;
    ADTx->SSTAR = 0u;
    ADTx->SSTPR = 0u;
    ADTx->SCLRR = 0u;
    ADTx->IFR = 0u;
    ADTx->CR = 0x00000300u;
    ADTx->AOSSR = 0u;
    ADTx->AOSCL = 0u;
    ADTx->PTBKS = 0u;
    ADTx->PTBKP = 0u;
    ADTx->TTRIG = 0u;
    ADTx->ITRIG = 0u;

    return Ok;
}

/**
 * @brief 定时器开始计数
 * @param [in]  ADTx     ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:    设定成功
 *              - 其他:  设定失败
 */
en_result_t Adt_StartCount(M0P_ADTIM_TypeDef *ADTx)
{

    ADTx->GCONR_f.START = 1u;

    return Ok;
}

/**
 * @brief 定时器停止计数
 * @param [in]  ADTx     ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:    设定成功
 *              - 其他:  设定失败
 */
en_result_t Adt_StopCount(M0P_ADTIM_TypeDef *ADTx)
{
    ADTx->GCONR_f.START = 0u;

    return Ok;
}

/**
 * @brief 设置计数值
 * @param [in]  ADTx     ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  u16Value 计数值
 * @retval      en_result_t
 *              - Ok:    设定成功
 *              - 其他:  设定失败
 */
en_result_t Adt_SetCount(M0P_ADTIM_TypeDef *ADTx, uint16_t u16Value)
{
    ADTx->CNTER_f.CNT = u16Value;
    return Ok;
}

/**
 * @brief 获取计数值
 * @param [in]  ADTx      ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      u16Value  当前计数值
 *
 */
uint16_t Adt_GetCount(M0P_ADTIM_TypeDef *ADTx)
{
    uint16_t u16Value;

    ASSERT(IS_VALID_ADT_UNIT(enAdtUnit));

    u16Value = ADTx->CNTER_f.CNT;

    return u16Value;
}

/**
 * @brief 清除计数值
 * @param [in]  ADTx    ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:   设定成功
 *              - 其他: 设定失败
 */
en_result_t Adt_ClearCount(M0P_ADTIM_TypeDef *ADTx)
{
    ADTx->CNTER_f.CNT = 0u;
    return Ok;
}

/**
 * @brief 获取有效周期计数值
 * @param [in]  ADTx       ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      u8TempCnt  有效周期值
 */
uint8_t Adt_GetVperNum(M0P_ADTIM_TypeDef *ADTx)
{
    uint8_t u8TempCnt;


    ASSERT(IS_VALID_ADT_UNIT(enAdtUnit));

    u8TempCnt = ADTx->STFLR_f.VPERNUM;

    return u8TempCnt;
}

/**
 * @brief 获取状态标志
 *
 * @param [in]  ADTx     ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  enstate  状态标志类型 @ref en_adt_state_type_t
 * @retval  boolean_t
 *          - TRUE:    状态标志置起
 *          - FALSE:   状态标志未置起
 */
boolean_t Adt_GetState(M0P_ADTIM_TypeDef *ADTx, en_adt_state_type_t enstate)
{


    ASSERT(IS_VALID_ADT_UNIT(enAdtUnit));
    ASSERT(IS_VALID_ADT_STATE(enstate));

    return GetBit(((uint32_t)&ADTx->STFLR), enstate);
}

/**
 * @brief 配置计数周期
 *
 * @param [in]  ADTx       ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  u16Period  计数周期值
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - 其他:      设定失败
 */
en_result_t Adt_SetPeriod(M0P_ADTIM_TypeDef *ADTx, uint16_t u16Period)
{
    ADTx->PERAR = u16Period;

    return Ok;
}

/**
 * @brief 配置计数周期缓冲
 *
 * @param [in]  ADTx          ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  u16PeriodBuf  计数周期缓冲值
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - 其他:      设定失败
 */
en_result_t Adt_SetPeriodBuf(M0P_ADTIM_TypeDef *ADTx, uint16_t u16PeriodBuf)
{
    ADTx->PERBR = u16PeriodBuf;
    ADTx->BCONR_f.BENP = 1u;

    return Ok;
}

/**
 * @brief 清除计数周期缓冲
 * @param [in]  ADTx        ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - 其他:      设定失败
 */
en_result_t Adt_ClearPeriodBuf(M0P_ADTIM_TypeDef *ADTx)
{
    ADTx->BCONR_f.BENP = 0u;
    ADTx->PERBR = 0u;

    return Ok;
}

/**
 * @brief 配置有效计数周期
 *
 * @param [in]  ADTx                ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  pstcAdtValidPerCfg  有效计数周期配置指针 @ref stc_adt_validper_cfg_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_SetValidPeriod(M0P_ADTIM_TypeDef *ADTx,
                               stc_adt_validper_cfg_t *pstcAdtValidPerCfg)
{


    if (NULL == pstcAdtValidPerCfg)
    {
        return ErrorInvalidParameter;
    }

    ADTx->VPERR_f.PCNTS = pstcAdtValidPerCfg->enValidCnt;
    ADTx->VPERR_f.PCNTE = pstcAdtValidPerCfg->enValidCdt;
    ADTx->VPERR_f.GEPERID = pstcAdtValidPerCfg->bPeriodD;
    ADTx->VPERR_f.GEPERIC = pstcAdtValidPerCfg->bPeriodC;
    ADTx->VPERR_f.GEPERIB = pstcAdtValidPerCfg->bPeriodB;
    ADTx->VPERR_f.GEPERIA = pstcAdtValidPerCfg->bPeriodA;

    return Ok;
}

/**
 * @brief 配置比较输出计数基准值
 * @param [in]  ADTx         ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  enAdtCompare 通用比较基准寄存器 @ref en_adt_compare_t
 * @param [in]  u16Compare   比较基准值
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_SetCompareValue(M0P_ADTIM_TypeDef *ADTx,
                                en_adt_compare_t enAdtCompare,
                                uint16_t u16Compare)
{
    if (AdtCompareA == enAdtCompare)
    {
        ADTx->GCMAR = u16Compare;
    }
    else if (AdtCompareB == enAdtCompare)
    {
        ADTx->GCMBR = u16Compare;
    }
    else if (AdtCompareC == enAdtCompare)
    {
        ADTx->GCMCR = u16Compare;
    }
    else if (AdtCompareD == enAdtCompare)
    {
        ADTx->GCMDR = u16Compare;
    }
    else
    {
        return ErrorInvalidParameter;
    }

    return Ok;
}

/**
 * @brief 配置专用比较计数基准值
 *
 * @param [in]  ADTx         ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  enAdtSpclCmp 专用比较基准值寄存器 @ref en_adt_special_compare_t
 * @param [in]  u16SpclCmp   比较基准值
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_SetSpecilCompareValue(M0P_ADTIM_TypeDef *ADTx,
                                      en_adt_special_compare_t enAdtSpclCmp,
                                      uint16_t u16SpclCmp)
{
    if (AdtSpclCompA == enAdtSpclCmp)
    {
        ADTx->SCMAR_f.SCMA = u16SpclCmp;
    }
    else if (AdtSpclCompB == enAdtSpclCmp)
    {
        ADTx->SCMBR_f.SCMB = u16SpclCmp;
    }
    else
    {
        return ErrorInvalidParameter;
    }

    return Ok;
}

/**
 * @brief 配置通用比较值/捕获值缓存传送
 * @param [in]  ADTx            ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  enAdtCHxXPort   TIMxX通道 @ref en_adt_CHxX_port_t
 * @param [in]  bCompareBufEn   TRUE:使能/FALSE:禁止
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_EnableValueBuf(M0P_ADTIM_TypeDef *ADTx,
                               en_adt_CHxX_port_t enAdtCHxXPort,
                               boolean_t bCompareBufEn)
{
    if (AdtCHxA == enAdtCHxXPort)
    {
        ADTx->BCONR_f.BENA = bCompareBufEn;
    }
    else if (AdtCHxB == enAdtCHxXPort)
    {
        ADTx->BCONR_f.BENB = bCompareBufEn;
    }
    else
    {
        return ErrorInvalidParameter;
    }

    return Ok;
}

/**
 * @brief 清除比较输出计数值/捕获值缓存
 * @param [in]  ADTx           ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  enAdtCHxXPort  TIMxX通道 @ref en_adt_CHxX_port_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_ClearValueBuf(M0P_ADTIM_TypeDef *ADTx, en_adt_CHxX_port_t enAdtCHxXPort)
{
    if (AdtCHxA == enAdtCHxXPort)
    {
        ADTx->GCMCR = 0u;
    }
    else if (AdtCHxB == enAdtCHxXPort)
    {
        ADTx->GCMDR = 0u;
    }
    else
    {
        return ErrorInvalidParameter;
    }

    return Ok;
}

/**
 * @brief 获取捕获值
 * @param [in]  ADTx           ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  enAdtCHxXPort  TIMxX通道 @ref en_adt_CHxX_port_t
 * @param [in]  pu16Capture    捕获值指针
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_GetCaptureValue(M0P_ADTIM_TypeDef *ADTx,
                                en_adt_CHxX_port_t enAdtCHxXPort,
                                uint16_t *pu16Capture)
{
    if (AdtCHxA == enAdtCHxXPort)
    {
        *pu16Capture = ADTx->GCMAR_f.GCMA;
    }
    else if (AdtCHxB == enAdtCHxXPort)
    {
        *pu16Capture = ADTx->GCMBR_f.GCMB;
    }
    else
    {
        return ErrorInvalidParameter;
    }
    return Ok;
}

/**
 * @brief 获取捕获缓存值
 * @param [in]  ADTx            ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  enAdtCHxXPort   TIMxX通道 @ref en_adt_CHxX_port_t
 * @param [in]  pu16CaptureBuf  捕获缓存值指针
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - ErrorInvalidParameter:  无效参数
 */
en_result_t Adt_GetCaptureBuf(M0P_ADTIM_TypeDef *ADTx,
                              en_adt_CHxX_port_t enAdtCHxXPort,
                              uint16_t *pu16CaptureBuf)
{
    if (AdtCHxA == enAdtCHxXPort)
    {
        *pu16CaptureBuf = ADTx->GCMCR_f.GCMC;
    }
    else if (AdtCHxB == enAdtCHxXPort)
    {
        *pu16CaptureBuf = ADTx->GCMDR_f.GCMD;
    }
    else
    {
        return ErrorInvalidParameter;
    }
    return Ok;
}

/**
 * @brief 设置向上计数死区时间基准值
 * @param [in]  ADTx         ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  u16Value     死区时间上基准值
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - 其他:      设定失败
 */
en_result_t Adt_SetDTUA(M0P_ADTIM_TypeDef *ADTx, uint16_t u16Value)
{
    ADTx->DTUAR = u16Value;

    return Ok;
}

/**
 * @brief 设置向下计数死区时间基准值
 *
 * @param [in]  ADTx         ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  u16Value     死区时间下基准值
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - 其他:      设定失败
 */
en_result_t Adt_SetDTDA(M0P_ADTIM_TypeDef *ADTx, uint16_t u16Value)
{
    ADTx->DTDAR = u16Value;

    return Ok;
}

/**
 * @brief 配置死区时间功能
 * @param [in]  ADTx    ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  bDTEn   TRUE:死区功能使能/FALSE:禁止
 * @param [in]  bEqual  TRUE:DTDAR的值和DTUAR的值自动相等/FALSE:DTDAR的值和DTUAR的值分别设定
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - 其他:      设定失败
 */
en_result_t Adt_CfgDT(M0P_ADTIM_TypeDef *ADTx, boolean_t bDTEn, boolean_t bEqual)
{
    ADTx->DCONR_f.DTCEN = bDTEn;
    ADTx->DCONR_f.SEPA = bEqual;

    return Ok;
}

/**
 * @brief Z相输入屏蔽设置
 * @param [in]  ADTx              ADV Timer通道选择(M0P_ADTIM4/M0P_ADTIM5/M0P_ADTIM6)
 * @param [in]  pstcAdtZMaskCfg   Z相输入屏蔽功能配置指针 @ref stc_adt_zmask_cfg_t
 * @retval      en_result_t
 *              - Ok:        设定成功
 *              - 其他:      设定失败
 */
en_result_t Adt_CfgZMask(M0P_ADTIM_TypeDef *ADTx, stc_adt_zmask_cfg_t *pstcAdtZMaskCfg)
{
    if (NULL == pstcAdtZMaskCfg)
    {
        return ErrorInvalidParameter;
    }

    ADTx->GCONR_f.ZMSK = pstcAdtZMaskCfg->enZMaskCycle;
    ADTx->GCONR_f.ZMSKPOS = pstcAdtZMaskCfg->bFltPosCntMaksEn;
    ADTx->GCONR_f.ZMSKREV = pstcAdtZMaskCfg->bFltRevCntMaksEn;

    return Ok;
}

/**
 * @}
 */

/**
 * @}
 */

/**
 * @}
 */

/******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/

