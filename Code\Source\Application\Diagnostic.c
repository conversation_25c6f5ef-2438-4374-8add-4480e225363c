/*!
 * @file
 * @brief This module covers the complete fan functionality.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

 #include "Diagnostic.h"
 #include "FridgeRunner.h"
 #include "ResolverDevice.h"
 #include "CoolingCycle.h"
 #include "SystemManager.h"

bool CalcFrzDeforstHearterCondtion(void);
uint16_t CalcFrzDeforstHearterPower(void);
bool CalcRefDeforstHearterCondtion(void);
uint16_t CalcRefDeforstHearterPower(void);

static uint8_t diagnostic_power_load_result[DIAGNOSTIC_POWER_LOAD_COUNT / 8 + 1] = {0};
static uint8_t diagnostic_power_load_count[DIAGNOSTIC_POWER_LOAD_COUNT] = {0};
static const diagnostic_power_load_st diagnostic_power_loads[DIAGNOSTIC_POWER_LOAD_COUNT] = 
{ 
    {420, 580, false, CalcFrzDeforstHearterCondtion, CalcFrzDeforstHearterPower},
    {490, 530, false, CalcRefDeforstHearterCondtion, CalcRefDeforstHearterPower}
};

bool CalcFrzDeforstHearterCondtion(void)
{
    if(eFridge_Running == Get_FridgeState() &&
       eRunning_Defrosting == Get_RunningState() &&
       Get_ResolvedDeviceStatus(DEVICE_FrzDefHeater) == DS_On)
    {
        return true;
    }
    return false;
}

uint16_t CalcFrzDeforstHearterPower(void)
{
    return Get_220VAdValue();
}

bool CalcRefDeforstHearterCondtion(void)
{
    return false;

    if(eFridge_Running == Get_FridgeState() &&
       eRunning_Defrosting == Get_RunningState() &&
       Get_ResolvedDeviceStatus(DEVICE_RefDefHeater) == DS_On)
    {
        return true;
    }
    return false;
}

uint16_t CalcRefDeforstHearterPower(void)
{
   return Get_220VAdValue(); 
}

static void SetDiagnosticPowerLoadFault(diagnostic_power_load_e load)
{
    if(load >= DIAGNOSTIC_POWER_LOAD_COUNT)
    {
        return;
    }

    diagnostic_power_load_result[load / 8] |=  1 << (load % 8);
}

static void ClearDiagnosticPowerLoadFault(diagnostic_power_load_e load)
{
    if(load >= DIAGNOSTIC_POWER_LOAD_COUNT)
    {
        return;
    }

    diagnostic_power_load_result[load / 8] &=  ~(1 << (load % 8));
}

static void DiagnosticPowerLoad(void)
{
    uint8_t index = 0;
    uint16_t loadpower = 0;
    const diagnostic_power_load_st *dpl = NULL;

    for(; index < DIAGNOSTIC_POWER_LOAD_COUNT; index++)
    {
        dpl = &diagnostic_power_loads[index];
        if(dpl->calc_load_condtion())
        {
            loadpower = dpl->calc_load_power();
            if(dpl->inner_range == false &&
              (loadpower <= dpl->min_power || loadpower >= dpl->max_power))
            {
                diagnostic_power_load_count[index] = 0;
                ClearDiagnosticPowerLoadFault(index);
            }
            else if(dpl->inner_range == true &&
                    (loadpower > dpl->min_power && loadpower < dpl->max_power))
            {
                diagnostic_power_load_count[index] = 0;
                ClearDiagnosticPowerLoadFault(index);
            }
            else
            {
                diagnostic_power_load_count[index]++;
                if(diagnostic_power_load_count[index] > DIAGNOSTIC_POWER_LOAD_COUNT_MAX)
                {
                    SetDiagnosticPowerLoadFault(index);
                }
            }
        }
    }
}

 void DiagnosticInit(void)
 {

 }

void DiagnosticRun(void)
{
    DiagnosticPowerLoad();
}

uint8_t IsDiagnosticPowerLoadFault(diagnostic_power_load_e load)
{
    if(load >= DIAGNOSTIC_POWER_LOAD_COUNT)
    {
        return 0;
    }

    if((diagnostic_power_load_result[load / 8]) & (1 << (load % 8)))
    {
        return 1;
    }
    return 0;
}