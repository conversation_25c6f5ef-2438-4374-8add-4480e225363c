/*!
 * @file
 * @brief Manages all the state variables of the cooling controller.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "Crc16_CCITT_FALSE.h"
#include "Parameter_TemperatureZone.h"
#include "SystemTimerModule.h"
#include "DisplayInterface.h"
#include "Driver_DoorSwitch.h"
#include "ParameterManager.h"
#include "SystemManager.h"
#include "CoolingCycle.h"
#include "ResolverDevice.h"
#include "Driver_Flash.h"
#include "Sbus_Nfc.h"
#include "Sbus_IceMaker.h"
#include "FaultCode.h"
#include "Iot_Spec.h"
#include "miio_api.h"
#include "syslog.h"
#include "Diagnostic.h"
#include "Driver_Emulator.h"

nfc_param_st nfc_param = {
    .mtype = MACHINE_TYPE_UNKOWN,
    .init = 1,
};

sbus_nfc_st nfc;

static void sync_poweron(property_reponse_e result)
{
    if(PROPERTY_RESPONSE_SUCCESS == result)
    {
        nfc.init = true;
        nfc_param.init = 0;
    }
}

static bool update_wifi_state(void)
{
    uint8_t state = 0;

    state = get_dev_net_state();
    if(nfc_param.wifi_state != state)
    {
        nfc_param.wifi_state = state;
        return true;
    }
    return false;
}

static bool update_door_state(void)
{
    uint16_t state = 0;

    if(Get_DoorSwitchState(DOOR_REF_LEFT) == true)
    {
        state |= 1;
    }

    if(Get_DoorSwitchState(DOOR_REF_RIGHT) == true)
    {
        state |= 2;
    }

    if(Get_DoorSwitchState(DOOR_FRZ_LEFT) == true)
    {
        state |= 4;
        state |= 0x40;
    }

    if(Get_DoorSwitchState(DOOR_FRZ_RIGHT) == true)
    {
        state |= 8;
        state |= 0x40;
    }

    if(nfc_param.door_state != state)
    {
        nfc_param.door_state = state;
        return true;
    }
    return false;
}

static bool update_door_alarm(void)
{
    uint8_t door_alarm = 0;
    uint8_t DoorStateRL = Get_DoorSwitchflagState(DOOR_REF_LEFT);
    uint8_t DoorStateRR = Get_DoorSwitchflagState(DOOR_REF_RIGHT);
    uint8_t DoorStateFL = Get_DoorSwitchflagState(DOOR_FRZ_LEFT);
    uint8_t DoorStateFR = Get_DoorSwitchflagState(DOOR_FRZ_RIGHT);

    if(DoorStateRL & 0x08)
    {
        door_alarm |= 1 << 1;
    }

    if(DoorStateRR & 0x08)
    {
        door_alarm |= 1 << 2;
    }

    if(DoorStateFL & 0x08)
    {
        door_alarm |= 1 << 3;
    }

    if(DoorStateFR & 0x08)
    {
        door_alarm |= 1 << 4;
    }

    if(nfc_param.door_alarm != door_alarm)
    {
        nfc_param.door_alarm = door_alarm;
        return true;
    }
    return false;
}

static bool update_fault_code(void)
{
    uint32_t error = 0;
    uint32_t error0 = Get_FaultCodeByte(eFCode_IotByte0);
    uint32_t error1 = Get_FaultCodeByte(eFCode_IotByte1);
    uint32_t error2 = Get_FaultCodeByte(eFCode_IotByte2);
    uint32_t error3 = Get_FaultCodeByte(eFCode_IotByte3);

    error = error0 + (error1 << 8) + (error2 << 16) + (error3 << 24);
    if(nfc_param.error != error)
    {
        nfc_param.error = error;
        return true;
    }
    return false;
}

static bool update_machine_type(void)
{
    uint8_t mt = GetMachineType();

    if(mt == MACHINE_TYPE_UNKOWN)
    {
        return false;
    }

    if(nfc_param.mtype != mt)
    {
        nfc.mtype_timer = Get_MinuteCount();
        nfc_param.mtype = mt;
        return true;
    }

    if(Get_MinuteElapsedTime(nfc.mtype_timer) > NFC_MTYPE_SYNC_MINUTES)
    {
        nfc.mtype_timer = Get_MinuteCount();
        return true;
    }
    return false;
}

static bool update_system_state(void)
{
    uint8_t state = Get_FridgeState();

    if(nfc_param.state != state)
    {
        nfc_param.state = state;
        return true;
    }
    return false;
}

static bool update_wifi_match(void)
{
    uint8_t match;

    if(is_wifi_arch_platform())
    {
        match = 0;
    }
    else
    {
        match = 1;
    }
    if(nfc_param.wifi_match != match)
    {
        nfc_param.wifi_match = match;
        return true;
    }
    return false;
}

static bool update_pfault_state(void)
{
    uint32_t pfault = 0;
    bool err_state = Get_SensorError((uint8_t)SENSOR_HUMIDITY);
    bool whearter_state = GetIceMakerWaterPipeHearterErr();
    bool wbox_sensor = GetIceMakerWaterBoxSensorErr();

    if(err_state)
    {
        pfault |= 1 << 0;
    }

    if(IsDiagnosticPowerLoadFault(DIAGNOSTIC_POWER_LOAD_FRZ_DEFORST_HEARTER))
    {
        pfault |= 1 << 6; 
    }

    if(whearter_state)
    {
        pfault |= 1 << 7; 
    }

    if(IsDiagnosticPowerLoadFault(DIAGNOSTIC_POWER_LOAD_REF_DEFORST_HEARTER))
    {
        pfault |= 1 << 8; 
    }

    if(nfc_param.pfault != pfault)
    {
        nfc_param.pfault = pfault;
        return true;
    }
    return false;
}

fireware_property_st nfc_propertys[NFC_PROPERTY_TYPE_MAX] = {
    { PROPERTY_VAL_MODEL, 1, 2, nfc_param.model, PROPERTY_DIR_OUT | PROPERTY_ALLDIRITY_FILTER, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_DID, 1, 3, nfc_param.did, PROPERTY_DIR_OUT | PROPERTY_ALLDIRITY_FILTER, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_MAC, 1, 9, nfc_param.mac, PROPERTY_DIR_OUT | PROPERTY_ALLDIRITY_FILTER, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 250, 250, &nfc_param.mtype, PROPERTY_DIR_OUT | PROPERTY_ALLDIRITY_FILTER, false, NULL, NULL, update_machine_type, false, 0 },
    { PROPERTY_VAL_UINT32, 2, 1, &nfc_param.error, PROPERTY_DIR_OUT, false, NULL, NULL, update_fault_code, false, 0 },
    { PROPERTY_VAL_UINT8, 2, 3, &nfc_param.door_alarm, PROPERTY_DIR_OUT, false, NULL, NULL, update_door_alarm, false, 0 },
    { PROPERTY_VAL_UINT16, 10, 11, &nfc_param.door_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_door_state, false, 0 },
    { PROPERTY_VAL_UINT8, 10, 13, &nfc_param.state, PROPERTY_DIR_OUT, false, NULL, NULL, update_system_state, false, 0 },
    { PROPERTY_VAL_UINT32, 10, 36, &nfc_param.pfault, PROPERTY_DIR_OUT, false, NULL, NULL, update_pfault_state, false, 0 },
    { PROPERTY_VAL_UINT8, 11, 1, &nfc_param.wifi_state, PROPERTY_DIR_OUT, false, NULL, NULL, update_wifi_state, false, 0 },
    { PROPERTY_VAL_UINT8, 11, 3, &nfc_param.wifi_restore, PROPERTY_DIR_INOUT, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 11, 4, &nfc_param.wifi_factory, PROPERTY_DIR_INOUT, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 11, 5, &nfc_param.wifi_match, PROPERTY_DIR_OUT, false, NULL, NULL, update_wifi_match, false, 0 },
    { PROPERTY_VAL_UINT8, 20, 4, &nfc_param.force_state, PROPERTY_DIR_OUT, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 21, 3, &nfc_param.clean_state, PROPERTY_DIR_OUT, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 9, 1, &nfc_param.food_alarm, PROPERTY_DIR_IN, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT16, 20, 10, &nfc_param.short_time, PROPERTY_DIR_OUT, false, NULL, NULL, NULL, false, 0 },
    { PROPERTY_VAL_UINT8, 7, 2, &nfc_param.init, PROPERTY_DIR_INOUT, false, NULL, sync_poweron, NULL, false, 0 },
};

static int32_t Nfc_recvframe(fireware_frame_st *data)
{
    uint8_t *rdata;
    uint16_t rlen = 0;
    fireware_property_st *prop;
    uint8_t index;

    if(nfc.poll_count == 0)
    {
        nfc.poll_count = NFC_EVENT_POLL_TIMER_MS;
        if(Is_Sbus_Slave_Err(&nfc.slave) == true)
        {
            nfc.poll_count = NFC_ERROR_EVENT_POLL_TIMER_MS;
        }
    }

    if(NULL == data)
    {
        if(nfc.dirty && nfc.frame.fb1 == FIREWARE_PROPERTY_FUNC && nfc.frame.fb2 == PROPERTY_SUBFUNC_IOT)
        {
            nfc.dirty = false;
            if(nfc.init == false)
            {
                nfc.b_appversion = false;
                nfc.alldirty = true;
            }
        }
        return -1;
    }
    else
    {
        if(data->srcAddr != FIREWARE_NFC_ADDR)
        {
            err("(%d, %d)not nfc frame\n", data->srcAddr, data->fb1);
            return -1;
        }

        if(data->fb1 == FIREWARE_PROPERTY_FUNC)
        {
            if(data->fb2 == PROPERTY_SUBFUNC_QUERY_RESPONSE)
            {
                if(data->len > 0)
                {
                    rdata = Parse_PropertyData(data->data, data->len, &rlen, nfc_propertys, NFC_PROPERTY_TYPE_MAX);
                    if(rlen != 0)
                    {
                        Edit_Property_ResponseFrame(FIREWARE_NFC_ADDR, &nfc.frame, rdata, rlen);
                        Sbus_Slave_Request(&nfc.slave, SBUS_PKT_PRI_LEVEL7);
                        nfc.response = true;
                        nfc.dirty = false;
                    }
                }
                if(nfc_param.init == 1)
                {
                    nfc.b_bootversion = false;
                    nfc.b_appversion = false;
                    nfc_param.init = 0;
                    nfc_param.mtype = MACHINE_TYPE_UNKOWN;
                    nfc.init = false;
                    nfc.dirty = false;
                    nfc.alldirty = true;
                    nfc.b_appsn = false;
                }
            }
            else if(data->fb2 == PROPERTY_SUBFUNC_RESPONSE)
            {
                if(data->len > 0)
                {
                    Parse_PropertyResponse(data->data, data->len, nfc_propertys, NFC_PROPERTY_TYPE_MAX);
                }
                nfc.dirty = false;
            }
        }
        else if(data->fb1 == FIREWARE_OTA_FUNC)
        {
            if(data->fb2 == FIREWARE_OTA_QUERY_VER)
            {
                nfc.hwversion = data->data[0] << 8 | data->data[1];
                nfc.appversion = data->data[2] << 8 | data->data[3];
                nfc.appcrc = data->data[4] << 8 | data->data[5];
                nfc.b_appversion = true;
            }

            if(data->fb2 == FIREWARE_OTA_BOOT_VER)
            {
                nfc.bootVersion = data->data[0] << 8 | data->data[1];
                nfc.bootCrc = data->data[2] << 8 | data->data[3];
                nfc.b_bootversion = true;
            }
        }
    }
    return 0;
}

static fireware_frame_st *Nfc_sendframe(sbus_pkt_pri_e pri)
{
    if(pri == SBUS_PKT_PRI_LEVEL6)
    {
        if(nfc.b_appversion == false)
        {
            Edit_OtaControlFrame(&nfc.frame,
                nfc.slave.addr,
                FIREWARE_OTA_QUERY_VER);
        }
        else
        {
            if(nfc.init == true)
            {
                Edit_Property_QueryFrame(nfc.slave.addr, &nfc.frame);
            }
            else
            {
                return NULL;
            }
        }
    }
    else if(pri == SBUS_PKT_PRI_LEVEL5)
    {
        if(nfc.dirty == true)
        {
            Edit_Property_Frame(nfc.slave.addr, &nfc.frame, nfc_propertys, NFC_PROPERTY_TYPE_MAX);
        }
        else
        {
            return NULL;
        }
    }
    else if(pri == SBUS_PKT_PRI_LEVEL4)
    {
        if(nfc.b_bootversion == false)
        {
            Edit_OtaControlFrame(&nfc.frame,
                nfc.slave.addr,
                FIREWARE_OTA_BOOT_VER);
        }
        else
        {
            return NULL;
        }
    }
    else if(pri == SBUS_PKT_PRI_LEVEL7)
    {
        if(nfc.response == true)
        {
            nfc.response = false;
        }
        else
        {
            return NULL;
        }
    }

    return &nfc.frame;
}

void Init_SbusNfc(void)
{
    uint8_t index;
    fireware_property_st *prop;

    nfc.slave.addr = FIREWARE_NFC_ADDR;
    nfc.slave.ops.recvFrame = Nfc_recvframe;
    nfc.slave.ops.sendFrame = Nfc_sendframe;
    nfc.poll_count = NFC_EVENT_POLL_TIMER_MS;
    nfc.init = false;
    nfc.dirty = false;
    nfc.alldirty = true;
    nfc.b_appsn = false;
    nfc.mtype_timer = Get_MinuteCount();
    Sbus_Slave_Register(SBUS_TYPE_ID0, &nfc.slave);
}

void Update_Nfc_Param(void)
{
    bool update;
    uint8_t index;
    fireware_property_st *prop;

    if(nfc.dirty)
    {
        return;
    }

    for(index = 0; index < NFC_PROPERTY_TYPE_MAX; index++)
    {
        prop = &nfc_propertys[index];
        if((prop->flags & PROPERTY_DIR_OUT) && prop->dirty1 == true)
        {
            prop->dirty1 = false;
            prop->dirty = true;
        }
        if((prop->flags & PROPERTY_DIR_OUT) &&
            prop->update_property)
        {
            update = prop->update_property();
            if(update == true)
            {
                prop->dirty = true;
            }
        }
        if((prop->flags & PROPERTY_DIR_OUT) &&
            (prop->dirty == true || ((prop->flags & PROPERTY_ALLDIRITY_FILTER) == 0 && nfc.alldirty == true)))
        {
            prop->dirty = true;
            nfc.dirty = true;
        }
    }

    if(nfc.alldirty == true)
    {
        nfc.alldirty = false;
    }

    if(nfc.dirty)
    {
        Sbus_Slave_Request(&nfc.slave, SBUS_PKT_PRI_LEVEL5);
    }
}

void Handle_Nfc_Overtime(void)
{
    if(nfc.poll_count > 0)
    {
        nfc.poll_count--;
        if(nfc.poll_count == 0)
        {
            Sbus_Slave_Request(&nfc.slave, SBUS_PKT_PRI_LEVEL6);
            if(Is_Sbus_Slave_Err(&nfc.slave) == true)
            {
                Update_Nfc_Param();
            }
        }
    }

    if(Is_Sbus_Slave_Err(&nfc.slave) == false)
    {
        Update_Nfc_Param();
    }

    if(nfc.b_bootversion == false && nfc.boot_count++ >= NFC_BOOT_MS)
    {
        nfc.boot_count = 0;
        Sbus_Slave_Request(&nfc.slave, SBUS_PKT_PRI_LEVEL4);
    }
}

void Handle_Nfc_Request()
{
    uint8_t val;

    if(nfc_param.wifi_restore)
    {
        nfc.wifi_timer++;
        if(nfc.wifi_timer > NFC_WIFI_REQUEST_TIMEOUT_100MS || execute_wifi_cmd_async(WIFI_CMD_RESTORE, NULL) == 0)
        {
            val = 0;
            nfc.wifi_timer = 0;
            SetNfcPropertyValue(NFC_PROPERTY_TYPE_WIFI_RESTORE, &val);
        }
    }
    else if(nfc_param.wifi_factory)
    {
        nfc.wifi_timer++;
        if(nfc.wifi_timer > NFC_WIFI_REQUEST_TIMEOUT_100MS || execute_wifi_cmd_async(WIFI_CMD_FACTORY, NULL) == 0)
        {
            val = 0;
            nfc.wifi_timer = 0;
            SetNfcPropertyValue(NFC_PROPERTY_TYPE_WIFI_FACTORY, &val);
        }
    }
}

int8_t GetNfcPropertyValue(nfc_property_type_e type, void *data)
{
    if(type >= NFC_PROPERTY_TYPE_MAX || data == NULL)
    {
        return FIREWARE_ERROR;
    }
    memcpy(data, nfc_propertys[type].data, get_Property_Size(nfc_propertys[type].val));
    return FIREWARE_SUCCESS;
}

int8_t SetNfcPropertyValue(nfc_property_type_e type, void *data)
{
    if(type >= NFC_PROPERTY_TYPE_MAX || data == NULL)
    {
        return FIREWARE_ERROR;
    }
    memcpy(nfc_propertys[type].data, data, get_Property_Size(nfc_propertys[type].val));
    if(nfc.dirty == true)
    {
        nfc_propertys[type].dirty1 = true;
    }
    else
    {
        nfc_propertys[type].dirty = true;
    }
    return FIREWARE_SUCCESS;
}

bool GetNfcPropertyState(nfc_property_type_e type)
{
    if(type >= NFC_PROPERTY_TYPE_MAX)
    {
        return false;
    }

    return (nfc_propertys[type].dirty || nfc_propertys[type].dirty1);
}

__EMULATOR__FUNCITON bool Get_NfcCommErr(void)
{
    return (Is_Sbus_Master_Err(&nfc.slave) || Is_Sbus_Slave_Err(&nfc.slave));
}

uint32_t Get_NfcBootVersion(void)
{
    if(nfc.b_bootversion)
    {
        return nfc.bootVersion;
    }

    return 0;
}

uint32_t Get_NfcBootCrc(void)
{
    if(nfc.b_bootversion)
    {
        return nfc.bootCrc;
    }

    return 0;
}

uint32_t Get_NfcAppVersion(void)
{
    if(nfc.b_appversion)
    {
        return nfc.appversion;
    }

    return 0;
}

uint32_t Get_NfcAppCrc(void)
{
    if(nfc.b_appversion)
    {
        return nfc.appcrc;
    }

    return 0;
}

sbus_slave_stats *Get_NfcPacketStats(void)
{
    return &nfc.slave.stats;
}

