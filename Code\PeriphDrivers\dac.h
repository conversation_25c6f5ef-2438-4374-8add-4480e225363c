/**
 ******************************************************************************
 * @file   dac.h
 *
 * @brief This file contains all the functions prototypes of the DAC driver
 *        library.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-03-15       MDAS            First version
 @endverbatim
 ******************************************************************************
 * Copyright (C) 2023, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 ******************************************************************************
 */

#ifndef __DAC_H__
#define __DAC_H__

/******************************************************************************/
/* Include files                                                              */
/******************************************************************************/
#include "ddl.h"

/* C binding of definitions if building with C++ compiler */
#ifdef __cplusplus
extern "C"
{
#endif

/**
 * @addtogroup HC32L186_DDL 驱动库
 * @{
 */

/**
 * @addtogroup DDL_DAC DAC模块驱动库
 * @{
 */

/******************************************************************************
 ** Global type definitions
 *****************************************************************************/
/**
 * @defgroup DAC_Global_Types DAC全局类型定义
 * @{
 */

/**
 * @brief  DAC使能或者禁止指令
 */
typedef enum
{
    DacDisable  = 0u,         /*!< 禁止 */
    DacEnable   = 1u          /*!< 使能 */
} en_en_state_t;

/**
 * @brief  使能或者禁止DAC通道输出缓冲器
 */
typedef enum
{
    DacBoffEnable  = 0u,    /*!< 使能DAC通道输出缓冲器 */
    DacBoffDisable = 1u     /*!< 禁止DAC通道输出缓冲器 */
} en_dac_boff_t;

/**
 * @brief  使能或者禁止DAC通道触发使能
 */
typedef enum
{
    DacTenDisable = 0u,     /*!< 禁止DAC通道触发 */
    DacTenEnable  = 1u      /*!< 使能DAC通道触发 */
} en_dac_ten_t;

/**
 * @brief  DAC通道触发选择
 */
typedef enum
{
    DacTim0Tradc = 0u,      /*!< TIM0_TRADC触发 */
    DacTim1Tradc = 1u,      /*!< TIM1_TRADC触发 */
    DacTim2Tradc = 2u,      /*!< TIM2_TRADC触发 */
    DacTim3Tradc = 3u,      /*!< TIM3_TRADC触发 */
    DacTim4Tradc = 4u,      /*!< TIM4_TRADC触发 */
    DacTim5Tradc = 5u,      /*!< TIM5_TRADC触发 */
    DacSwTriger  = 6u,      /*!< 软件触发 */
    DacExPortTriger = 7u    /*!< 外部端口触发 */
} en_dac_tsel_t;

/**
 * @brief  DAC通道噪声/三角波生产使能或禁止
 */
typedef enum
{
    DacWaveDisable  = 0u,   /*!< 禁止生产波 */
    DacNoiseEnable  = 1u,   /*!< 使能生成噪声波 */
    DacTrWaveEnable = 2u    /*!< 使能生产三角波 */
} en_dac_wave_t;

/**
 * @brief  DAC通道掩码/振幅选择器
 */
typedef enum
{
    DacMemp01   = 0u,       /*!< 不屏蔽LFSR的位0 / 三角波振幅等于1 */
    DacMenp03   = 1u,       /*!< 不屏蔽LFSR的位[1:0] / 三角波振幅等于3 */
    DacMenp07   = 2u,       /*!< 不屏蔽LFSR的位[2:0] / 三角波振幅等于7 */
    DacMenp15   = 3u,       /*!< 不屏蔽LFSR的位[3:0] / 三角波振幅等于15 */
    DacMenp31   = 4u,       /*!< 不屏蔽LFSR的位[4:0] / 三角波振幅等于31 */
    DacMenp63   = 5u,       /*!< 不屏蔽LFSR的位[5:0] / 三角波振幅等于63 */
    DacMenp127  = 6u,       /*!< 不屏蔽LFSR的位[6:0] / 三角波振幅等于127 */
    DacMenp255  = 7u,       /*!< 不屏蔽LFSR的位[7:0] / 三角波振幅等于255 */
    DacMenp511  = 8u,       /*!< 不屏蔽LFSR的位[8:0] / 三角波振幅等于511 */
    DacMenp1023 = 9u,       /*!< 不屏蔽LFSR的位[9:0] / 三角波振幅等于1023 */
    DacMenp2047 = 10u,      /*!< 不屏蔽LFSR的位[10:0] / 三角波振幅等于2047 */
    DacMenp4095 = 11u       /*!< 不屏蔽LFSR的位[11:0] / 三角波振幅等于4095 */
} en_dac_mamp_t;

/**
 * @brief  DAC参考电压选择
 */
typedef enum
{
    DacVoltage1V5   = 0u,   /*!< 内部1.5V */
    DacVoltage2V5   = 1u,   /*!< 内部2.5V */
    DacVoltageExRef = 2u,   /*!< 外部参考电压ExRef(PB01) */
    DacVoltageAvcc  = 3u    /*!< AVCC电压 */
} en_dac_sref_t;

/**
 * @brief  DAC端口触发选择
 */
typedef enum
{
    DacPortTrigPA9 = 0u,    /*!< 触发端口为PA9 */
    DacPortTrigPB9 = 1u,    /*!< 触发端口为PB9 */
    DacPortTrigPC9 = 2u,    /*!< 触发端口为PC9 */
    DacPortTrigPD9 = 3u,    /*!< 触发端口为PD9 */
    DacPortTrigPE9 = 4u,    /*!< 触发端口为PE9 */
    DacPortTrigPF9 = 5u     /*!< 触发端口为PF9 */
} en_port_trig_t;

/**
 * @brief  DAC数据对齐方式
 */
typedef enum
{
    DacRightAlign = 0u,        /*!< 右对齐 */
    DacLeftAlign  = 1u         /*!< 左对齐 */
} en_align_t;

/**
 * @brief  DAC数据位数
 */
typedef enum
{
    DacBit8  = 0u,              /*!< 8位 */
    DacBit12 = 1u               /*!< 12位 */
} en_bitno_t;

/**
 * @brief  DAC初始化配置结构体
 */
typedef struct
{
    en_dac_boff_t  boff_t;          /*!< DAC输出缓冲器使能 @ref en_dac_boff_t */
    en_dac_ten_t   ten_t;           /*!< DAC触发使能 @ref en_dac_ten_t */
    en_dac_tsel_t  tsel_t;          /*!< DAC通道触发器选择 @ref en_dac_tsel_t */
    en_dac_wave_t  wave_t;          /*!< DAC通道噪声 / 三角波生成使能 @ref en_dac_wave_t */
    en_dac_mamp_t  mamp_t;          /*!< DAC通道掩码 / 振幅选择 @ref en_dac_mamp_t */
    en_dac_sref_t  sref_t;          /*!< DAC参考电压选择 @ref en_dac_sref_t */
    en_port_trig_t port_trig_t;     /*!< DAC端口触发选择 @ref en_port_trig_t */
    en_align_t     align;           /*!< DAC数据对齐方式 @ref en_align_t */
    uint16_t       dhr12;           /*!< DAC 12位输出数据 */
    uint8_t        dhr8;            /*!< DAC 8位输出数据 */
} stc_dac_cfg_t;

/**
 * @}
 */

/******************************************************************************
 * Global variable definitions ('extern')
 ******************************************************************************/

/******************************************************************************
 * Global function prototypes (definition in C source)
 ******************************************************************************/
/**
 * @addtogroup DAC_Global_Functions DAC全局函数定义
 * @{
 */
void Dac_DmaCmd(boolean_t NewState);
void Dac_DmaITCfg(boolean_t NewState);
boolean_t Dac_GetITStatus(void);
void Dac_Cmd(boolean_t NewState);
void Dac_SoftwareTriggerCmd(void);
void Dac_Init(stc_dac_cfg_t *DAC_InitStruct);
void Dac_SetChannelData(en_align_t DAC_Align, en_bitno_t DAC_Bit, uint16_t Data);
uint16_t Dac_GetDataOutputValue(void);
/**
 * @}
 */


/**
 * @}
 */

/**
 * @}
 */


#ifdef __cplusplus
}
#endif
#endif //__DAC_H__

/******************************************************************************/
/* EOF (not truncated)                                                        */
/******************************************************************************/
