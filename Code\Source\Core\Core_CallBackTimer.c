/*!
 * @file
 * @brief Enables timers to be used with callback functions and provides a way for
 * timers to be used without having to poll them.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "Core_TimeBase.h"
#include "Core_CallBackTimer.h"
#include "CORE_Assert.h"
#include "List.h"

static ST_List ast_CallBackTimerPools[eCallbackTimer_Priority_Max];

#if(true == CALLBACK_TIMER_NORMAL_PRIORITY_ENABLED)
static st_CoreCallbackTimer *
    apst_TimerPoolBuffer_Normal[U8_CORE_CALLBACK_TIMER_POOL_SIZE_NORMAL];
#endif

#if(true == CALLBACK_TIMER_HIGH_PRIORITY_ENABLED)
static st_CoreCallbackTimer *
    apst_TimerPoolBuffer_High[U8_CORE_CALLBACK_TIMER_POOL_SIZE_HIGH];
#endif

static const st_CoreCallbackTimer *pst_EmptyTimerPoolElement =
    (st_CoreCallbackTimer *)(NULL);

static bool ProcessTimer(st_CoreCallbackTimer *pst_CallBackTimer);

#if(true == CALLBACK_TIMER_HIGH_PRIORITY_ENABLED)
static void ProcessHighPriorityTimers(void);
#endif

#if(true == CALLBACK_TIMER_NORMAL_PRIORITY_ENABLED)
static void ProcessNormalPriorityTimers(void);
#endif

void Core_CallbackTimer_Init(void);

void Core_CallbackTimer_Update(void);

void Core_CallbackTimer_TimerStart(
    st_CoreCallbackTimer *pst_CallBackTimer,
    fpTimerCallBackFunction fp_CallBackFunction,
#if(true == CALLBACK_TIMER_CALLBACK_DATA_ENABLED)
    void *p_CallBackData,
#endif
    uint16_t u16_DurationSeconds,
    uint16_t u16_DurationMilliSeconds,
    EN_Core_CallbackTimer_Type en_TimerType,
    EN_Core_CallbackTimer_Priority en_Priority);

void Core_CallbackTimer_TimerStop(
    st_CoreCallbackTimer *pst_CallBackTimer);

bool Core_CallbackTimer_B_IsTimerRunning(
    st_CoreCallbackTimer *pst_CallBackTimer);

void Core_CallbackTimer_TimeRemaining(
    st_CoreCallbackTimer *pst_CallBackTimer, uint16_t *pu16_Seconds, uint16_t *pu16_Milliseconds);

static bool ProcessTimer(st_CoreCallbackTimer *pst_CallBackTimer)
{
    bool b_ActiveTimerProcessed = false;
    fpTimerCallBackFunction fp_CallBackFunction;
#if(true == CALLBACK_TIMER_CALLBACK_DATA_ENABLED)
    void *p_CallBackData;
#endif

    if((st_CoreCallbackTimer *)(NULL) != pst_CallBackTimer)
    {
        b_ActiveTimerProcessed = true;

        if(true == Core_TimerLib_IsTimerExpired(&(pst_CallBackTimer->st_Timer)))
        {
            fp_CallBackFunction = pst_CallBackTimer->fp_CallBackFunction;
#if(true == CALLBACK_TIMER_CALLBACK_DATA_ENABLED)
            p_CallBackData = pst_CallbackTimer->p_CallBackData;
#endif

            if((uint8_t)(eCallbackTimer_Type_Periodic) ==
                pst_CallBackTimer->u8_TimerType)
            {
                Core_TimerLib_TimerStart(&(pst_CallBackTimer->st_Timer),
                    pst_CallBackTimer->u16_DurationSeconds,
                    pst_CallBackTimer->u16_DurationMilliSeconds);
            }
            else
            {
                Core_CallbackTimer_TimerStop(pst_CallBackTimer);
            }

#if(true == CALLBACK_TIMER_CALLBACK_DATA_ENABLED)
            fp_CallBackFunction(p_CallBackData);
#else
            fp_CallBackFunction();
#endif
        }
    }

    return (b_ActiveTimerProcessed);
}

#if(true == CALLBACK_TIMER_HIGH_PRIORITY_ENABLED)
static void ProcessHighPriorityTimers(void)
{
    st_CoreCallbackTimer *pst_CallBackTimer;
    uint8_t u8_Index;

    for(u8_Index = 0; u8_Index < NUM_ELEMENTS(apst_TimerPoolBuffer_High);
        u8_Index++)
    {
        List_B_Access(&ast_CallBackTimerPools[eCallbackTimer_Priority_High],
                      (uint16_t)(u8_Index),
                      (void *)&psCallbackTimer));
        (void)ProcessTimer(pst_CallBackTimer);
    }
}
#endif

#if(true == CALLBACK_TIMER_NORMAL_PRIORITY_ENABLED)
static void ProcessNormalPriorityTimers(void)
{
    static uint8_t u8_Index = 0;
    uint8_t u8_NumberTimersChecked = 0;
    bool b_ActiveTimerProcessed = false;
    st_CoreCallbackTimer *pst_CallBackTimer;

    do
    {
        u8_Index = u8_Index % (NUM_ELEMENTS(apst_TimerPoolBuffer_Normal));
        pst_CallBackTimer = (st_CoreCallbackTimer *)(NULL);
        ENSURE(List_B_Access(
            &ast_CallBackTimerPools[eCallbackTimer_Priority_Normal],
            (uint16_t)(u8_Index),
            (void *)&pst_CallBackTimer));
        b_ActiveTimerProcessed = ProcessTimer(pst_CallBackTimer);
        u8_NumberTimersChecked++;
        u8_Index++;
    } while((false == b_ActiveTimerProcessed) &&
        (u8_NumberTimersChecked < NUM_ELEMENTS(apst_TimerPoolBuffer_Normal)));
}
#endif

void Core_CallbackTimer_Init(void)
{
#if(true == CALLBACK_TIMER_NORMAL_PRIORITY_ENABLED)
    ENSURE(List_B_Init(&ast_CallBackTimerPools[eCallbackTimer_Priority_Normal],
        (void *)apst_TimerPoolBuffer_Normal,
        NUM_ELEMENTS(apst_TimerPoolBuffer_Normal),
        ELEMENT_SIZE(apst_TimerPoolBuffer_Normal),
        (void *)&pst_EmptyTimerPoolElement));
#endif
#if(true == CALLBACK_TIMER_HIGH_PRIORITY_ENABLED)
    ENSURE(List_B_Init(&ast_CallBackTimerPools[eCallbackTimer_Priority_High],
        (void *)apst_TimerPoolBuffer_High,
        NUM_ELEMENTS(apst_TimerPoolBuffer_High),
        ELEMENT_SIZE(apst_TimerPoolBuffer_High),
        (void *)&pst_EmptyTimerPoolElement));
#endif
}

void Core_CallbackTimer_Update(void)
{
#if(true == CALLBACK_TIMER_HIGH_PRIORITY_ENABLED)
    ProcessHighPriorityTimers();
#endif
#if(true == CALLBACK_TIMER_NORMAL_PRIORITY_ENABLED)
    ProcessNormalPriorityTimers();
#endif
}

void Core_CallbackTimer_TimerStart(
    st_CoreCallbackTimer *pst_CallBackTimer,
    fpTimerCallBackFunction fp_CallBackFunction,
#if(true == CALLBACK_TIMER_CALLBACK_DATA_ENABLED)
    void *p_CallBackData,
#endif
    uint16_t u16_DurationSeconds,
    uint16_t u16_DurationMilliSeconds,
    EN_Core_CallbackTimer_Type en_TimerType,
    EN_Core_CallbackTimer_Priority en_Priority)
{
    ENSURE(en_TimerType < eCallbackTimer_Type_Max);
    ENSURE(en_Priority < eCallbackTimer_Priority_Max);
    ENSURE(pst_CallBackTimer != (st_CoreCallbackTimer *)(NULL));

    pst_CallBackTimer->u16_DurationSeconds = u16_DurationSeconds;
    pst_CallBackTimer->u16_DurationMilliSeconds = u16_DurationMilliSeconds;

    pst_CallBackTimer->u8_TimerType = (uint8_t)en_TimerType;

    pst_CallBackTimer->u8_Priority = (uint8_t)en_Priority;

    pst_CallBackTimer->fp_CallBackFunction = fp_CallBackFunction;
#if(true == CALLBACK_TIMER_CALLBACK_DATA_ENABLED)
    pst_CallBackTimer->p_CallBackData = p_CallBackData;
#endif

    ENSURE(List_B_Insert(
        &ast_CallBackTimerPools[pst_CallBackTimer->u8_Priority],
        (void *)&pst_CallBackTimer));

    Core_TimerLib_TimerStart(&(pst_CallBackTimer->st_Timer),
        pst_CallBackTimer->u16_DurationSeconds,
        pst_CallBackTimer->u16_DurationMilliSeconds);
}

void Core_CallbackTimer_TimerStop(
    st_CoreCallbackTimer *pst_CallBackTimer)
{
    if(pst_CallBackTimer->u8_Priority < (uint8_t)eCallbackTimer_Priority_Max)
    {
        List_B_Remove(&ast_CallBackTimerPools[pst_CallBackTimer->u8_Priority],
            (void *)&pst_CallBackTimer);
    }
}

bool Core_CallbackTimer_B_IsTimerRunning(
    st_CoreCallbackTimer *pst_CallBackTimer)
{
    bool b_TimerIsRunning = false;

    if(((st_CoreCallbackTimer *)(NULL) != pst_CallBackTimer) &&
        (pst_CallBackTimer->u8_Priority < (uint8_t)eCallbackTimer_Priority_Max))
    {
        b_TimerIsRunning = List_B_Contains(
            &ast_CallBackTimerPools[pst_CallBackTimer->u8_Priority],
            (void *)&pst_CallBackTimer);
    }

    return (b_TimerIsRunning);
}

void Core_CallbackTimer_TimeRemaining(
    st_CoreCallbackTimer *pst_CallBackTimer, uint16_t *pu16_Seconds, uint16_t *pu16_Milliseconds)
{
    if(((uint16_t *)NULL != pu16_Seconds) &&
        ((uint16_t *)NULL != pu16_Milliseconds))
    {
        if(true ==
            Core_CallbackTimer_B_IsTimerRunning(pst_CallBackTimer))
        {
            *pu16_Seconds = pst_CallBackTimer->st_Timer.st_Duration.u16_Seconds;
            *pu16_Milliseconds =
                pst_CallBackTimer->st_Timer.st_Duration.u16_Ticks /
                COREUSER_TIMEBASE_NUM_TICKS_PER_MILLISECOND;
        }
        else
        {
            *pu16_Seconds = 0;
            *pu16_Milliseconds = 0;
        }
    }
}
