/*!
 * @file
 * @brief Manages all the state variables of the cooling controller.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "OtaManager.h"
#include "Driver_Flash.h"
#include "Adpt_Usart.h"
#include "Adpt_Iwdg.h"
#include "Init_Mcu.h"
#include "syslog.h"

static void OtaManagerRest(void);
static bool OtaCheckHeader(void);
static bool OtaFirewareDownload(void);

static ota_mannager_st otamanager;
static uint16_t ota_manager_interval_ticks;
static const char down_update_fw_str[] = "down update_fw";
static const char switch_wifi_update[] = "down switch_wifi";
static const char ota_done_fw_str[] = "done\r";

static inline int xmodem_recv_byte(unsigned char *c, int timeout_ms)
{
    return miio_xmodem_recv_byte(otamanager.xmodem_ch, c, timeout_ms);
}

static int xmodem_recv_str(unsigned char *str, int timeout_ms)
{
    return miio_xmodem_recv_str(otamanager.xmodem_ch, str, XMODEM_PACK_LEN, timeout_ms);
}

static inline int xmodem_send_byte(unsigned char c)
{
    return miio_uart_send_byte(otamanager.xmodem_ch, c);
}

static inline int xmodem_send_str(const char *str)
{
    return miio_uart_send_str(otamanager.xmodem_ch, str);
}

static unsigned short crc16_ccitt_xmodem(const unsigned char *buf, int len)
{
    unsigned short crc = 0;
    while(len--)
    {
        int i;
        crc ^= *buf++ << 8;
        for(i = 0; i < 8; ++i)
        {
            if(crc & 0x8000)
            {
                crc = (crc << 1) ^ 0x1021;
            }
            else
            {
                crc = crc << 1;
            }
        }
    }
    return crc;
}

static bool xmodem_check_packno(ota_mannager_st *x)
{
    uint8_t no = 0;
    uint8_t no_r = 0;
    uint8_t packno;
    uint32_t packlen;
    unsigned char *pbuf;

    if(NULL == x)
    {
        return false;
    }

    pbuf = x->xbuff;
    packno = x->pack_no;
    no = pbuf[XMODEM_PACKNO_POS];
    no_r = pbuf[XMODEM_PACKNO_POS + 1];

    switch(x->type)
    {
        case XMODEM:
            packlen = XMODEM_PACK_LEN;
            break;
        case XMODEM_1K:
            packlen = XMODEM_1K_PACK_LEN;
            break;
        default:
            return false;
    }
    return ((packno == no) && (((no + no_r) & 0xff) == 0xff) && (x->xlen == packlen));
}

static bool xmodem_check_crc(ota_mannager_st *x, uint16_t crc)
{
    unsigned char *pbuf;
    uint8_t crc_h = 0;
    uint8_t crc_l = 0;

    if(NULL == x)
    {
        return false;
    }
    pbuf = x->xbuff;

    switch(x->type)
    {
        case XMODEM:
        {
            crc_h = pbuf[XMODEM_CRC_POS];
            crc_l = pbuf[XMODEM_CRC_POS + 1];
        }
        break;
        case XMODEM_1K:
        {
            crc_h = pbuf[XMODEM_1K_CRC_POS];
            crc_l = pbuf[XMODEM_1K_CRC_POS + 1];
        }
        break;
        default:
            break;
    }
    return (crc == ((((uint16_t)crc_h << 8) & 0xff00) | ((uint16_t)crc_l & 0x00ff)));
}

static unsigned int xmodem_cal_data_len(unsigned char *xbuff)
{
    unsigned char pos = XMODEM_CRC_POS - 1;

    while(pos >= XMODEM_DATA_POS && xbuff[pos] == 0xFF)
        pos--;

    if(pos == XMODEM_DATA_POS - 1 || pos == XMODEM_CRC_POS - 1)
    {
        return XMODEM_MTU;
    }
    else if(xbuff[pos] == CTRLZ)
    {
        return pos - XMODEM_DATA_POS;
    }
    else
    {
        return XMODEM_MTU;
    }
}

static void xmodem_check_packet(void)
{
    unsigned short ccrc;
    otamanager.pack_no++;

    if(!xmodem_check_packno(&otamanager))
    {
        otamanager.pack_retry++;
        if(otamanager.pack_retry >= PACK_RETRY_MAX)
        {
            xmodem_send_byte(CAN);
            xmodem_send_byte(CAN);
            xmodem_send_byte(CAN);
            otamanager.state = OTA_STATE_FAIL;
            err("ota manager xmodem can not recv data too much packno errors!\n");
            return;
        }
        else
        {
            warn("ota manager xmodem check packno(%d-%d) fail!\n",
                otamanager.pack_no,
                otamanager.xbuff[XMODEM_PACKNO_POS]);
            otamanager.pack_no--;
            if(xmodem_check_packno(&otamanager))
            {
                xmodem_send_byte(ACK);
            }
            else
            {
                xmodem_send_byte(NAK);
            }
            return;
        }
    }

    otamanager.pack_retry = 0;

    ccrc = crc16_ccitt_xmodem(&(otamanager.xbuff[3]), otamanager.xlen - 5);
    if(!xmodem_check_crc(&otamanager, ccrc))
    {
        otamanager.crc_retry++;
        if(otamanager.crc_retry > 10)
        {
            xmodem_send_byte(CAN);
            xmodem_send_byte(CAN);
            xmodem_send_byte(CAN);
            err("ota manager xmodem can not recv data too much crc errors!\n");
            otamanager.state = OTA_STATE_FAIL;
            return;
        }
        else
        {
            warn("ota manager xmodem check packno(%d) crc fail!\n", otamanager.pack_no);
            otamanager.pack_no--;
            xmodem_send_byte(NAK);
            return;
        }
    }

    otamanager.crc_retry = 0;
    otamanager.sum_data_bytes += XMODEM_MTU;
    if(!otamanager.header_checked)
    {
        if(!OtaCheckHeader())
        {
            goto fail;
        }
    }
    else if(!OtaFirewareDownload())
    {
        goto fail;
    }
    xmodem_send_byte(ACK);
    return;
fail:
    otamanager.state = OTA_STATE_FAIL;
    return;
}

static void xmodem_recv_process(void)
{
    int data_len;
    unsigned char c;

    switch(otamanager.xbuff[0])
    {
        case SOH:
        {
            otamanager.type = XMODEM;
            otamanager.trans_retry = 0;
            xmodem_check_packet();
            return;
        }
        case STX:
        {
            otamanager.type = XMODEM_1K;
            otamanager.trans_retry = 0;
            xmodem_check_packet();
            return;
        }
        case EOT:
        {
            if(otamanager.xlen > 1)
            {
                goto fault;
            }
            otamanager.trans_retry = 0;
            xmodem_send_byte(ACK);
            data_len = xmodem_cal_data_len(otamanager.xbuff);
            info("data len:%d\n", data_len);
            if(data_len > 0)
            {
                err("ota package is not 128 bytes algin\n");
                otamanager.state = OTA_STATE_FAIL;
            }
            else
            {
                otamanager.state = OTA_STATE_CHECKCRC;
            }
            return;
        }
        case CAN:
        {
            if(otamanager.xlen > 1)
            {
                goto fault;
            }
            otamanager.trans_retry = 0;
            xmodem_recv_byte(&c, 100);
            /* canceled by remote */
            if(c == CAN)
            {
                xmodem_send_byte(ACK);
                otamanager.state = OTA_STATE_FAIL;
            }
            return;
        }
        default:
fault:
        {
            if(++otamanager.trans_retry > TRANS_RETRY_MAX)
            {
                xmodem_send_byte(CAN);
                xmodem_send_byte(CAN);
                xmodem_send_byte(CAN);
                otamanager.state = OTA_STATE_FAIL;
            }
            else
            {
                xmodem_send_byte(NAK);
            }
            return;
        }
    }
}

static bool OtaCheckHeader(void)
{
    uint32_t index;
    fireware_comm_st *fw;
    fireware_version_st version;

    memcpy(&otamanager.header, &otamanager.xbuff[3], sizeof(ota_imageheader_st));
    otamanager.header.total_version = convert_from_bigendian32(otamanager.header.total_version);
    for(index = 0; index < OTA_FIREWARE_MAX; index++)
    {
        IWDG_Refesh();
        otamanager.header.fhs[index].id = convert_from_bigendian16(otamanager.header.fhs[index].id);
        otamanager.header.fhs[index].version = convert_from_bigendian32(otamanager.header.fhs[index].version);
        otamanager.header.fhs[index].offset = convert_from_bigendian32(otamanager.header.fhs[index].offset);
        otamanager.header.fhs[index].length = convert_from_bigendian32(otamanager.header.fhs[index].length);
        otamanager.header.fhs[index].crc = convert_from_bigendian16(otamanager.header.fhs[index].crc);

        if(FIREWARE_ADDR_NONE == otamanager.header.fhs[index].id)
        {
            break;
        }

        otamanager.ota_fws[index].id = otamanager.header.fhs[index].id;
        if(FIREWARE_MAIN_ADDR == otamanager.header.fhs[index].id)
        {
            otamanager.ota_fws[index].start = otamanager.header.fhs[index].offset;
            otamanager.ota_fws[index].end = otamanager.header.fhs[index].offset + otamanager.header.fhs[index].length;
            otamanager.ota_fwsum++;
            continue;
        }

        fw = FirewareGetByAddr(otamanager.header.fhs[index].id);
        if(NULL == fw)
        {
            err("Not support fireware(%d)\n", otamanager.header.fhs[index].id);
            return false;
        }
        otamanager.ota_fws[index].fw = fw;
        otamanager.ota_fws[index].start = otamanager.header.fhs[index].offset;
        otamanager.ota_fws[index].end = otamanager.header.fhs[index].offset + otamanager.header.fhs[index].length;
        otamanager.ota_fwsum++;
        FirewareEnable(fw);
        if(FirewareQueryAppVersion(fw, &version) < 0)
        {
            err("FirewareQueryAppVersion failed\n");
            otamanager.ota_fws[index].skip = 1;
            otamanager.incomplete = 1;
            continue;
        }

        if(otamanager.header.fhs[index].version == version.appVersion &&
            otamanager.header.fhs[index].crc == version.appCrc)
        {
            if(FIREWARE_SUCCESS == FirewareCheckAppCrc(fw))
            {
                otamanager.ota_fws[index].skip = 1;
            }
        }

        if(!otamanager.ota_fws[index].skip && FirewareStartOtaDownload(fw) < 0)
        {
            otamanager.ota_fws[index].skip = 1;
            otamanager.incomplete = 1;
            continue;
        }
    }

    otamanager.header_checked = 1;
    info("total_version:%d\n", otamanager.header.total_version);
    return true;
}

static bool OtaFirewareDownload(void)
{
    uint8_t index = otamanager.ota_fw;
    uint32_t start;
    uint32_t end;
    uint32_t offset;
    uint32_t id;

    start = otamanager.ota_fws[index].start;
    end = otamanager.ota_fws[index].end;
    offset = otamanager.sum_data_bytes;
    id = otamanager.ota_fws[index].id;

    if(offset > start && offset <= end && id == FIREWARE_MAIN_ADDR)
    {
        if(ImageDownload(OTA_IMAGE_APP, &otamanager.xbuff[XMODEM_DATA_POS], XMODEM_MTU) < 0)
        {
            return false;
        }
    }
    else if(offset > start && offset <= end && id != FIREWARE_MAIN_ADDR)
    {
        if(!otamanager.ota_fws[index].skip)
        {
            if(FirewareOtaDownload(otamanager.ota_fws[index].fw, &otamanager.xbuff[XMODEM_DATA_POS], XMODEM_MTU) < 0)
            {
                return false;
            }
        }
    }
    else
    {
        /*last package 1A FF FF*/
        info("sum:%d\n", otamanager.sum_data_bytes);
        return true;
    }

    if(offset == end)
    {
        otamanager.ota_fw++;
        if(id != FIREWARE_MAIN_ADDR)
        {
            if(!otamanager.ota_fws[index].skip &&
                FirewareStopOtaDownload(otamanager.ota_fws[index].fw) < 0)
            {
                return false;
            }
        }
    }
    return true;
}

static void OtaStartShake(void)
{
    if(otamanager.timeout++ > otamanager.init_timeout)
    {
        otamanager.timeout = 0;
        if(miio_uart_send_str_wait_ack(otamanager.xmodem_ch, "result \"ready\"\r",
           otamanager.wait_timeout) != UART_RECV_ACK_ERROR)
        {
            otamanager.state = OTA_STATE_READY;
        }
        else
        {
            otamanager.initcount++;
            if(otamanager.initcount > XMODEM_INIT_RETRY_MAX)
            {
                otamanager.state = OTA_STATE_FAIL;
            }
        }
    }
}

static void OtaReadyShake(void)
{
    xmodem_send_str("C");
    otamanager.panic = true;
    otamanager.state = OTA_STATE_DOWNLOAD;
}

static void OtaDownloadShake(void)
{
    int n_read;

    n_read = xmodem_recv_str(otamanager.xbuff, otamanager.recv_timeout);
    if(n_read <= 0 && ++otamanager.trans_retry > TRANS_RETRY_MAX)
    {
        xmodem_send_byte(CAN);
        xmodem_send_byte(CAN);
        xmodem_send_byte(CAN);
        err("ota manager xmodem can not recv data too much times!\n");
        otamanager.state = OTA_STATE_FAIL;
        return;
    }

    if(n_read > 0)
    {
        otamanager.xlen = n_read;
        xmodem_recv_process();
    }
}

static void OtaFailShake(void)
{
    uint8_t failcount = otamanager.failsend;

    if(otamanager.panic)
    {
        Panic();
        otamanager.panic = false;
    }

    if (otamanager.timeout++ > otamanager.init_timeout)
    {
        info("ota manager enter fail state!\n");
        otamanager.timeout = 0;
        if(failcount++ > XMODEM_FAIL_RETRY_MAX)
        {
            otamanager.failsend = 0;
            otamanager.panic = true;
            otamanager.state = OTA_STATE_FAIL_ACK;
            return;
        }
        ImageDownloadRest();
        OtaManagerRest();
        otamanager.failsend = failcount;
        otamanager.panic = false;
        otamanager.state = OTA_STATE_FAIL;
        if(FirewareAllToBoot() < 0)
        {
            err("FirewareAllToBoot failed\n");
            return;
        }

        if(FirewareAllInBoot() < 0)
        {
            err("FirewareAllInBoot failed\n");
            return;
        }
        otamanager.failsend = 0;
        otamanager.panic = true;
        otamanager.state = OTA_STATE_FAIL_ACK;
    }
}

static void OtaFailAckShake(void)
{
    int ret;

    if(otamanager.timeout++ > otamanager.fail_timeout)
    {
        ret = (UART_RECV_ACK_ERROR == miio_uart_send_str_wait_ack(otamanager.xmodem_ch,
               "mcu_boot fail\r", otamanager.wait_timeout));

        if(ret == 0 || otamanager.failsend++ > XMODEM_INIT_RETRY_MAX)
        {
            otamanager.failsend = 0;
            otamanager.state = OTA_STATE_RETRY;
        }

        otamanager.timeout = 0;
    }
}

static void OtaRetryShake(void)
{
    int len;
    unsigned char *pbuf = NULL;
    fireware_comm_st *fw = NULL;

    if(otamanager.timeout++ > otamanager.interval_timeout)
    {
        otamanager.retry_count++;
        otamanager.timeout = 0;
        len = miio_uart_recv_str_aync(otamanager.xmodem_ch, &pbuf);
        if(len <= 0)
        {
            miio_uart_send_str(otamanager.xmodem_ch, "get_down\r");
            return;
        }
        pbuf[len] = '\0';
        if(strncmp((char *)pbuf, down_update_fw_str, sizeof(down_update_fw_str) - 1) == 0)
        {
            otamanager.state = OTA_STATE_INIT;
            otamanager.retry_count = 0;
        }
        else if(strncmp((char *)pbuf, switch_wifi_update, sizeof(switch_wifi_update) - 1) == 0)
        {
            if(IsTestUartOtaFlag())
            {
                otamanager.xmodem_ch = UART_CH_WIFI;
            }
        }
        else if((otamanager.retry_count * otamanager.interval_timeout) > OTA_RETRY_TIMEOUT_MS)
        {
            fw = FirewareGetByAddr(FIREWARE_INVERTER_ADDR);
            if(CheckImageCrc(OTA_IMAGE_APP) == 0 && FirewareCheckAppCrc(fw) == 0)
            {
                FirewareAllTryToApp();
                JumpToApp();
            }
        }
    }
}

static void OtaChecksumShake(void)
{
    fireware_version_st version;
    uint8_t index;
    uint32_t id;
    uint32_t hversion;
    uint32_t hcrc;

    otamanager.state = OTA_STATE_FAIL;
    for(index = 0; index < otamanager.ota_fwsum; index++)
    {
        IWDG_Refesh();
        id = otamanager.ota_fws[index].id;
        hversion = otamanager.header.fhs[index].version;
        hcrc = otamanager.header.fhs[index].crc;
        if(id == FIREWARE_MAIN_ADDR)
        {
            if(CheckImageVersionCrc(OTA_IMAGE_APP, hversion, hcrc) < 0)
            {
                err("ota main header check crc failed\n");
                return;
            }
        }
        else
        {
            if(otamanager.ota_fws[index].skip)
            {
                continue;
            }

            if(FirewareCheckAppCrc(otamanager.ota_fws[index].fw) < 0 ||
                FirewareQueryAppVersion(otamanager.ota_fws[index].fw, &version) < 0)
            {
                err("ota fw(%d) header check crc failed\n", id);
                return;
            }

            if(hcrc != version.appCrc || hversion != version.appVersion)
            {
                err("fw(%d) check version error:\n", id);
                err("hversion:%d  oversion:%d\n", hversion, version.appVersion);
                err("hcrc:%x    ota_crc:%x\n", hcrc, version.appCrc);
                return;
            }
        }
    }

    otamanager.state = OTA_STATE_DONE;
    return;
}

static void OtaDoneShake(void)
{
    if(otamanager.timeout++ > otamanager.init_timeout)
    {
        info("ota manager done, reset to app!\n");
        otamanager.timeout = 0;

        if(otamanager.failsend++ > 5 || otamanager.incomplete)
        {
            otamanager.failsend = 0;
            otamanager.state = OTA_STATE_FAIL;
            return;
        }

        if(FirewareAllToApp() < 0)
        {
            err("FirewareAllToApp failed\n");
        }

        IWDG_Refesh();
        
        if(FirewareAllInApp() < 0)
        {
            err("FirewareAllInApp failed\n");
            return;
        }

        if(IsTestUartOtaFlag())
        {
            miio_uart_send_str_wait_ack(otamanager.xmodem_ch, ota_done_fw_str, REMOTE_XMODEM_WAIT_TIMEOUT_MS);
        }

        SetOtaDone(otamanager.header.total_version);
        ClearAppPromoteCount();
        BoardReset();
    }
}

static void OtaManagerSchedule()
{
    otamanager.schedule = true;
    otamanager.interval_ticks = 0;
}

static void OtaManagerYield()
{
    otamanager.interval_ticks = 0;
    otamanager.schedule = false;
}

static void OtaManagerRest(void)
{
    memset(&otamanager, 0, sizeof(otamanager));
    otamanager.otafuncs[OTA_STATE_INIT] = OtaStartShake;
    otamanager.otafuncs[OTA_STATE_READY] = OtaReadyShake;
    otamanager.otafuncs[OTA_STATE_DOWNLOAD] = OtaDownloadShake;
    otamanager.otafuncs[OTA_STATE_FAIL] = OtaFailShake;
    otamanager.otafuncs[OTA_STATE_FAIL_ACK] = OtaFailAckShake;
    otamanager.otafuncs[OTA_STATE_RETRY] = OtaRetryShake;
    otamanager.otafuncs[OTA_STATE_CHECKCRC] = OtaChecksumShake;
    otamanager.otafuncs[OTA_STATE_DONE] = OtaDoneShake;
    otamanager.xmodem_ch = UART_CH_WIFI;
    otamanager.recv_timeout = XMODEM_RECV_TIMEOUT;
    otamanager.init_timeout = XMODEM_INIT_TIMEOUT;
    otamanager.fail_timeout = XMODEM_FAIL_INTERVAL_MS;
    otamanager.wait_timeout = XMODEM_WAIT_TIMEOUT_MS;
    otamanager.interval_timeout = XMODEM_INTERVAL_MS;
    if(IsTestUartOtaFlag())
    {
        otamanager.xmodem_ch = UART_CH_TESTUART;
        otamanager.recv_timeout = REMOTE_XMODEM_RECV_TIMEOUT;
        otamanager.init_timeout = REMOTE_XMODEM_INIT_TIMEOUT;
        otamanager.fail_timeout = REMOTE_XMODEM_FAIL_INTERVAL_MS;
        otamanager.wait_timeout = REMOTE_XMODEM_WAIT_TIMEOUT_MS;
        otamanager.interval_timeout = REMOTE_XMODEM_INTERVAL_MS;
    }
    OtaManagerSchedule();
    FirewareAllDisable();
    if (Is_PowerOnReset() == false)
    {
        otamanager.panic = true;
    }
}

void HandlerOtaScheduleInterval(void)
{
    if(!otamanager.schedule &&
        ++otamanager.interval_ticks >= OTA_POLL_INTERVAL_MS)
    {
        OtaManagerSchedule();
    }
}

void OtaManagerRun(void)
{
    if(otamanager.schedule)
    {
        if(otamanager.state >= OTA_STATE_INIT && otamanager.state <= OTA_STATE_DONE)
        {
            otamanager.otafuncs[otamanager.state]();
        }
        OtaManagerYield();
    }
}

void OtaManagerInit(void)
{
    int waitcnt = 3;
    fireware_version_st version;

    if(IsTestUartOtaFlag())
    {
        Board_InitTestUsart();
    }

    OtaManagerRest();
    if(IsOtaFlag())
    {
        info("do ota start....\n");
        ClearAppPromoteCount();
retry:
        if(waitcnt-- == 0)
        {
            return;
        }

        if(FirewareAllToBoot() < 0)
        {
            err("FirewareAllToBoot failed\n");
            goto retry;
        }

        if(FirewareAllInBoot() < 0)
        {
            err("FirewareAllInBoot failed\n");
            goto retry;
        }
        return;
    }
    else
    {
        info("not ota going to app....\n");
        if(CheckImageCrc(OTA_IMAGE_APP) < 0 || GetAppPromoteCount() == APP_PROMOTE_TIMES)
        {
            goto fail;
        }
        IncreaseAppPromoteCount();
        FirewareAllTryToApp();
        JumpToApp();
    }

fail:
    otamanager.state = OTA_STATE_FAIL;
    return;
}
