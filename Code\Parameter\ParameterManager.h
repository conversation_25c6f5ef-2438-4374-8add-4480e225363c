/*!
 * @file
 * @brief This file defines public constants, types and functions for the fan drive module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __PARAMETER_MANAGER__H
#define __PARAMETER_MANAGER__H

#include "Parameter.h"
#include "Driver_Flash.h"
#include "FirewareComm.h"

#define HAN<PERSON>HAKE_TIMEOUT_100MS 50
#define SYNC_TIMEOUT_100MS 50
#define CHECK_GAP_100MS 30
#define CHECK_TIMEOUT_100MS 20
#define FAULT_TIMEOUT_100MS 100
#define MODEL_TIMEOUT_100MS 5
#define MODEL_PRODUCT_ID_LEN 8
#define PARAM_OPERATE_ERROR_COUNT 5
#define PARAM_PACKET_LEN 128
#define SN_ERROR_CYCLE 2
#define PARAM_CHECK_CYCLE 3
#define WIFI_MODEL_RETRY_COUNT 10

struct sn_fireware;

typedef void (*parameter_manager_state_func)(void);

typedef enum
{
    SN_FIREWARE_MAIN,
    SN_FIREWARE_WIFI,
    SN_FIREWARE_MAX,
} sn_fireware_e;

typedef enum
{
    PARAMETER_MANAGER_PARAM_CHECKCRC,
    PARAMETER_MANAGER_PARAM_READCRC,
    PARAMETER_MANAGER_PARAM_WRITEPARAM,
    PARAMETER_MANAGER_PARAM_READPARAM,
    PARAMETER_MANAGER_PARAM_RECHECKCRC
} sn_fireware_state_e;

typedef struct sn_fireware {
    uint8_t insn[PRODUCT_SN_SIZE + 1];
    uint8_t outsn[PRODUCT_SN_SIZE + 1];
    uint8_t packno;
    uint8_t errorcnt;
    bool complete;
    bool checkerror;
    sn_fireware_state_e state;
    int8_t (*get_sn)(struct sn_fireware *sf);
    int8_t (*set_sn)(struct sn_fireware *sf);
    int8_t (*read_param)(struct sn_fireware *sf);
    int8_t (*write_param)(struct sn_fireware *sf);
    int8_t (*read_param_crc)(struct sn_fireware *sf);
    int8_t (*check_param_crc)(struct sn_fireware *sf);
    int8_t (*cancle_fireware_async)(struct sn_fireware *sf);
    bool b_sn_error;
    bool b_param_error;
}sn_fireware_st;

typedef enum
{
    PARAMETER_MANAGER_STATE_HANDSHAKE,
    PARAMETER_MANAGER_STATE_SYNC,
    PARAMETER_MANAGER_STATE_PROBE,
    PARAMETER_MANAGER_STATE_UPLOAD,
    PARAMETER_MANAGER_STATE_PARAM,
    PARAMETER_MANAGER_STATE_CHECK,
    PARAMETER_MANAGER_STATE_FAULT,
    PARAMETER_MANAGER_STATE_MAX,
} parameter_manager_state_e;

typedef enum
{
    PARAMETER_MANAGER_SN_NONE,
    PARAMETER_MANAGER_SN_CONFLICT_ERROR,
    PARAMETER_MANAGER_SN_INVAILD_ERROR,
    PARAMETER_MANAGER_SN_MATCH_ERROR,
} parameter_manager_fault_e;

typedef struct{
    uint8_t model[PRODUCT_MODEL_BYTES+1];
    uint8_t pid[MODEL_PRODUCT_ID_LEN];
    uint8_t mtype;
    uint8_t capacity;
}model_pid_st;

typedef struct{
    parameter_fireware_st *device_parameter;
    parameter_fireware_st *data_parameter;
    sn_fireware_st sn_firewares[SN_FIREWARE_MAX];
    uint8_t latest_sn[PRODUCT_SN_SIZE + 1];
    uint8_t model[FIREWARE_PROPERTY_MODEL_LEN];
    uint8_t did[FIREWARE_PROPERTY_DID_LEN];
    uint8_t mac[FIREWARE_PROPERTY_MAC_LEN];
	uint8_t data[FIREWARE_PARAM_PACKET_LEN];
    bool match;
    bool complete;
    bool b_sn_error;
    bool b_sn_confilit;
    bool b_sn_force;
    bool b_model_complete;
    bool b_pid_complete;
    bool b_mac_complete;
    bool b_did_complete;
    uint16_t timercount;
    uint16_t checktimer;
    uint16_t modeltimer;
    uint8_t pindex;
    uint32_t size;
    uint8_t sn_sync_cycle;
    uint8_t param_check_cycle;
    model_pid_st *pm;
    parameter_manager_fault_e fcode;
    parameter_manager_state_e state;
    parameter_manager_state_func statefuncs[PARAMETER_MANAGER_STATE_MAX];
}parameter_manager_st;

typedef enum
{
    MACHINE_TYPE_CROSS = 0,
    MACHINE_TYPE_FRENCH,
    MACHINE_TYPE_FRENCH1,
    MACHINE_TYPE_CROSS_WITH_ICEMAKER,
    MACHINE_TYPE_NORMAL,
    MACHINE_TYPE_UNKOWN
} machine_type_e;

typedef enum
{
    MACHINE_CAPACITY_NORMAL = 0,
    MACHINE_CAPACITY_ICEMAKER = 1 << 0,
    MACHINE_CAPACITY_MICROICE = 1 << 1,
    MACHINE_CAPACITY_SN = 1 << 2,
} machine_capacity_e;

uint8_t GetMachineType(void);
uint8_t *GetMachinePid(void);
void ParameterSnUpdate(void);
void ParameterManagerInit(void);
void ParameterManagerRun(void);
bool IsParameterManagerReady(void);
bool IsParameterManagerUploadReady(void);
bool IsParameterManagerFault(void);
bool IsParameterManagerSnFault(void);
void GetParameterManagerFault(uint8_t *fault, uint8_t size);
void GetParameterSn(sn_fireware_e index, uint8_t *sn, uint8_t size);
uint16_t GetParameterFaultCode(void);
uint16_t GetParameterSnFaultCode(void);
uint32_t GetMachineCapacity(void);
parameter_section_st *GetParameterSection(parameter_section_e sid);
#endif
