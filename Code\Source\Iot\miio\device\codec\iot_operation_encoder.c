#include "iot_operation_encoder.h"

/* ==============  Public functions  ============== */

int iot_operation_encode_end(char out[], size_t size)
{
    if(strlen(out) > size - 1)
        return MIIO_ERROR_PARAM;

    str_n_cat(out, 1, "\r");

    return MIIO_OK;
}


void iot_operation_value_encode(IotGeneralPropVal_t * pValue, char out[], size_t size)
{
		char integer_buf[INT_VALUE_MAX_LEN+1] = {0};


        switch(pValue->propType) {

            case IOT_TYPE_BOOL : {
                if(pValue->lValue == false) {
                    str_n_cat(out, 2, "false", " ");
                } else {
                    str_n_cat(out, 2, "true", " ");
                }
            }
                break;

            case IOT_TYPE_STRING : {
                str_n_cat(out, 2, pValue->sValue, " ");
            }
                break;

            case IOT_TYPE_LONG : 
                sprintf(integer_buf, "%ld", pValue->lValue);
                str_n_cat(out, 2, integer_buf, " ");
            	break;
			case IOT_TYPE_WORD:
            case IOT_TYPE_SHORT:
			case IOT_TYPE_BYTE:
			{
                sprintf(integer_buf, "%d", (int)pValue->lValue);
                str_n_cat(out, 2, integer_buf, " ");			
            }
             break;

            case IOT_TYPE_FLOAT :
                sprintf(integer_buf, "%f", pValue->fValue);
                str_n_cat(out, 2, integer_buf, " ");				
                break;
        }



}



int iot_event_operation_encode(TByte siid, TByte eiid, char out[], size_t size)
{
    char siid_buf[ID_MAX_LEN+1] = {0};
    char eiid_buf[ID_MAX_LEN+1] = {0};

    if (out == NULL || size <= 0) {
        return MIIO_ERROR_PARAM;
    }	

    str_n_cat(out, 2, "event_occured", " ");

    sprintf(siid_buf, "%d", siid);
    sprintf(eiid_buf, "%d", eiid);

    str_n_cat(out, 4, siid_buf, " ", eiid_buf, " ");

    return MIIO_OK;

}


int iot_action_operation_encode(IotAction_result_t *opt,  char out[], size_t size)
{
    char siid_buf[ID_MAX_LEN+1] = { 0 };
    char aiid_buf[ID_MAX_LEN+1] = { 0 };
    char code_buf[ERROR_CODE_MAX_LEN+1] = { 0 };

    if (out == NULL || size <= 0 || opt == NULL) {
        return MIIO_ERROR_PARAM;
    }

	str_n_cat(out, 1, "result ");	

    sprintf(siid_buf, "%d", opt->siid);
    sprintf(aiid_buf, "%d", opt->aiid);
    sprintf(code_buf, "%d", map_result_to_iot_code(opt->code));

    str_n_cat(out, 6, siid_buf, " ", aiid_buf, " ", code_buf, " ");		


    return MIIO_OK;	
}

int iot_changed_operation_encode(IotPropOper_result_t *opt, char out[], size_t size)
{
	char siid_buf[ID_MAX_LEN+1] = {0};
	char piid_buf[ID_MAX_LEN+1] = {0};


	sprintf(siid_buf, "%d", opt->siid);
	sprintf(piid_buf, "%d", opt->piid);

	str_n_cat(out, 4, (const char*)(&siid_buf), " ", (const char*)(&piid_buf), " ");

	iot_operation_value_encode(opt->pValue, out, size);

    return MIIO_OK;	
}


int iot_property_operation_encode(IotPropOper_result_t *pIotResult, char out[], size_t size, bool set_value)
{
    char siid_buf[ID_MAX_LEN+1] = {0};
    char piid_buf[ID_MAX_LEN+1] = {0};
    char code_buf[ERROR_CODE_MAX_LEN+1] = {0};

    if (out == NULL || size <= 0 || pIotResult == NULL) {
        return MIIO_ERROR_PARAM;
    }


	snprintf(siid_buf, ID_MAX_LEN, "%d", pIotResult->siid);
	snprintf(piid_buf, ID_MAX_LEN, "%d", pIotResult->piid);
	snprintf(code_buf, ERROR_CODE_MAX_LEN, "%d", map_result_to_iot_code(pIotResult->code));
    str_n_cat(out, 6, siid_buf, " ", piid_buf, " ", code_buf, " ");
	
    if(!set_value) {

		if(!is_result_successed((IotExecuteRet_e)(pIotResult->code))) {
			str_n_cat(out, 1, " ");
			return MIIO_OK;
		}
		
		iot_operation_value_encode(pIotResult->pValue, out, size);	
    }

    return MIIO_OK;
}

