/**
 * <AUTHOR>
 * @date    2019
 * @par     Copyright (c):
 *
 *    Copyright 2019 MIoT,MI
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */


#ifndef __ARCH_DEFINE_H__
#define __ARCH_DEFINE_H__

/* add ***all*** user header files here */
/* and all header files will be included in ***miio_define.h*** */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdarg.h>
#include <stddef.h>
#include <ctype.h>
#include <stdint.h>

#ifndef bool
typedef unsigned char bool;
#endif

#ifndef BOOL
#define BOOL							bool
#endif

#define arch_usleep(us)					udelay(us)
#define arch_msleep(ms)					arch_usleep(ms*1000)

#define arch_memset(str, val, len)		memset(str, val, len)
#define arch_memcpy(dst, src, len)		memcpy(dst, src, len)

#define arch_malloc(len)				malloc(len)
#define arch_calloc(num, len)			calloc(num, len)

#define arch_strtok(str, temp)			strtok(str, temp)
void  udelay(uint32_t us);

#ifndef true
#define true 1
#endif

#ifndef false
#define false 0
#endif

#endif /* __ARCH_DEFINE_H__ */
