/*!
 * @file
 * @brief This file defines public constants, types and functions for the fct.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef FUNCTIONAL_CIRCUIT_TEST_H
#define FUNCTIONAL_CIRCUIT_TEST_H

#include <stdint.h>
#include <stdbool.h>

typedef enum
{
    DEVICE_All_Check = 0, // 全部负载
    DEVICE_Comp_Check, // 1 压缩机
    DEVICE_FrzFan_Check, // 2 冷冻风机
    DEVICE_CoolFan_Check, // 3 冷却风机
    DEVICE_DoubleDamper_Check, // 4 冷藏双风门
    DEVICE_RefSingleDamper_Check, // 5 冷藏单风门
    DEVICE_VarSingleDamper_Check, // 6 变温单风门
    DEVICE_VerticalBeamHeater_Check, // 7 纵梁H
    DEVICE_FrzDefHeater_Check, // 8 冷冻化霜H
    DEVICE_RefLamp_Check, // 9 冷藏灯
    DEVICE_FrzLamp_Check, // 10 冷冻灯
    DEVICE_RefVarLamp_Check, // 11 变温灯
    DEVICE_RefFan_Check, // 12 冷藏风机
    DEVICE_Valve_Check, // 13 电动阀
    DEVICE_IonGenerator_Check, // 14 离子发生器
    DEVICE_VarDamperHearter_Check, // 15 变温风门加热丝
    DEVICE_FrzIonGenerator_Check, // 16 冷冻离子发生器
    DEVICE_RefDefHeater_Check,     // 17 冷藏化霜H
    DEVICE_IceMakerWaterPump_Check,     // 18 制冰机进水泵
    DEVICE_IceMakerFlip_Check,     // 19 制冰机脱冰电机
    DEVICE_IceMakerWaterHeater_Check,     // 20 水管加热器
    DEVICE_Exit_Check = 0xFF
} Device_ID_CHECK;

void FunctionalCircuitTest_Init(void);
void FunctionalCircuitTest_Exit(void);
void Set_SelfCheckDeviceIDAndValue(uint8_t entryNumber, uint8_t entryValue);
#endif
