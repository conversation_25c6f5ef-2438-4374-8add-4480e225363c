/*=====================================================================@@ms=*/
/*                                   IOT                                    */
/*                                                                          */
/*=====================================================================@@mi=*/
/* author    : ZW                                                           */
/*---------------------------------------------------------------------@@md-*/
/* Description :                                                            */
/*                                                                          */
/*                                                                          */
/*---------------------------------------------------------------------@@mu-*/
/* last modification:                                                       */
/*                                                                          */
/*=====================================================================@@me=*/

#ifndef IOT_H
#define IOT_H

/*=====================================================================@@gs=*/
/*                                 Includes                                 */
/*=====================================================================@@ge=*/

/*=====================================================================@@gb=*/

/*=====================================================================@@gs=*/
/*                             Type definitions                             */
/*=====================================================================@@ge=*/

/*=====================================================================@@gb=*/

/*=====================================================================@@gs=*/
/*                   External variable declarations (extern)                */
/*=====================================================================@@ge=*/


/*=====================================================================@@gs=*/
/*                     Function declarations (prototypes)                   */
/*=====================================================================@@ge=*/

/*=====================================================================@@gs=*/
/*             Macro definitions and manifest constants (#define)           */
/*=====================================================================@@ge=*/
#define IOT_DEVICE_REPORT_TIME_1MIN           1
#define IOT_DEVICE_REPORT_TIME_20MIN          20
#define IOT_DEVICE_REPORT_TIME_30MIN          30
#define IOT_DEVICE_REPORT_TIME_60MIN          60
#define IOT_DEVICE_REPORT_TIME_24H            (60 * 24)

extern void IOT_Func(void);


#endif
/*=====================================================================@@mb=*/

