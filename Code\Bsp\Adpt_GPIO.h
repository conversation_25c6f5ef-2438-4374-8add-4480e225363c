/*!
 * @file
 * @brief This file defines public constants, types and functions for the GPIO adapter.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef _Adpt_GPIO_H_
#define _Adpt_GPIO_H_

#include <stdint.h>
#include "gpio.h"

// AC Load
#define IO_AC_REF_DEFROST_HEATER_ENABLE Gpio_SetIO(GpioPortF, GpioPin4)
#define IO_AC_REF_DEFROST_HEATER_DISABLE Gpio_ClrIO(GpioPortF, GpioPin4)

#define IO_FRZ_DEFROST_HEATER_ENABLE Gpio_SetIO(GpioPortF, GpioPin5)
#define IO_FRZ_DEFROST_HEATER_DISABLE Gpio_ClrIO(GpioPortF, GpioPin5)

// DC Load
#define IO_REF_LEFT_DOOR_IN Gpio_GetInputIO(GpioPortA, GpioPin12)
#define IO_REF_RIGHT_DOOR_IN Gpio_GetInputIO(GpioPortF, GpioPin6)
#define IO_FRZ_RIGHT_DOOR_IN Gpio_GetInputIO(GpioPortF, GpioPin7)
#define IO_FRZ_LEFT_DOOR_IN Gpio_GetInputIO(GpioPortA, GpioPin15)

#define IO_HEARTBEAT_LED_ENABLE Gpio_SetIO(GpioPortC, GpioPin10)
#define IO_HEARTBEAT_LED_DISABLE Gpio_ClrIO(GpioPortC, GpioPin10)

#define IO_FRZ_LED_ENABLE Gpio_SetIO(GpioPortB, GpioPin14)
#define IO_FRZ_LED_DISABLE Gpio_ClrIO(GpioPortB, GpioPin14)

#define IO_TEST_CYCLE_VDAMHT_ENABLE Gpio_SetIO(GpioPortB, GpioPin5)
#define IO_TEST_CYCLE_VDAMHT_DISABLE Gpio_ClrIO(GpioPortB, GpioPin5)

#define IO_V_TOP_LED_ENABLE Gpio_SetIO(GpioPortB, GpioPin0)
#define IO_V_TOP_LED_DISABLE Gpio_ClrIO(GpioPortB, GpioPin0)

#define IO_LZ_LED_ENABLE Gpio_SetIO(GpioPortC, GpioPin4)
#define IO_LZ_LED_DISABLE Gpio_ClrIO(GpioPortC, GpioPin4)

#define IO_CBX_LED_ENABLE Gpio_SetIO(GpioPortC, GpioPin5)
#define IO_CBX_LED_DISABLE Gpio_ClrIO(GpioPortC, GpioPin5)

#define IO_FRZ_FAN_FB_IN Gpio_GetInputIO(GpioPortD, GpioPin2)
#define IO_REF_FAN_FB_IN Gpio_GetInputIO(GpioPortA, GpioPin9)
#define IO_COND_FAN_FB_IN Gpio_GetInputIO(GpioPortC, GpioPin12)
#define IO_COND_FAN_ENABLE Gpio_SetIO(GpioPortA, GpioPin11)
#define IO_COND_FAN_DISABLE Gpio_ClrIO(GpioPortA, GpioPin11)

#define IO_REF_DAMPER_ENABLE Gpio_SetIO(GpioPortA, GpioPin8)
#define IO_REF_DAMPER_DISABLE Gpio_ClrIO(GpioPortA, GpioPin8)
// #define IO_REF_DAMPER_IN1_HIGH Gpio_SetIO(GpioPortC, GpioPin9)
// #define IO_REF_DAMPER_IN1_LOW Gpio_ClrIO(GpioPortC, GpioPin9)
// #define IO_REF_DAMPER_IN2_HIGH Gpio_SetIO(GpioPortC, GpioPin8)
// #define IO_REF_DAMPER_IN2_LOW Gpio_ClrIO(GpioPortC, GpioPin8)
#define IO_REF_DAMPER_IN1_HIGH Gpio_SetIO(GpioPortC, GpioPin8)
#define IO_REF_DAMPER_IN1_LOW Gpio_ClrIO(GpioPortC, GpioPin8)
#define IO_REF_DAMPER_IN2_HIGH Gpio_SetIO(GpioPortC, GpioPin9)
#define IO_REF_DAMPER_IN2_LOW Gpio_ClrIO(GpioPortC, GpioPin9)

#define IO_DOUBLE_DAMPER_ENABLE Gpio_SetIO(GpioPortA, GpioPin8)
#define IO_DOUBLE_DAMPER_DISABLE Gpio_ClrIO(GpioPortA, GpioPin8)
#define IO_DOUBLE_DAMPER_IN1_HIGH Gpio_SetIO(GpioPortC, GpioPin9)
#define IO_DOUBLE_DAMPER_IN1_LOW Gpio_ClrIO(GpioPortC, GpioPin9)
#define IO_DOUBLE_DAMPER_IN2_HIGH Gpio_SetIO(GpioPortC, GpioPin8)
#define IO_DOUBLE_DAMPER_IN2_LOW Gpio_ClrIO(GpioPortC, GpioPin8)

#define IO_VALVE_A1_ON Gpio_SetIO(GpioPortB, GpioPin9)
#define IO_VALVE_A1_OFF Gpio_ClrIO(GpioPortB, GpioPin9)
#define IO_VALVE_A2_ON Gpio_SetIO(GpioPortB, GpioPin7)
#define IO_VALVE_A2_OFF Gpio_ClrIO(GpioPortB, GpioPin7)
#define IO_VALVE_B1_ON Gpio_SetIO(GpioPortB, GpioPin8)
#define IO_VALVE_B1_OFF Gpio_ClrIO(GpioPortB, GpioPin8)
#define IO_VALVE_B2_ON Gpio_SetIO(GpioPortB, GpioPin6)
#define IO_VALVE_B2_OFF Gpio_ClrIO(GpioPortB, GpioPin6)

#define IO_RDAMP_HEATER_ENABLE Gpio_SetIO(GpioPortB, GpioPin4)
#define IO_RDAMP_HEATER_DISABLE Gpio_ClrIO(GpioPortB, GpioPin4)

#define IO_VDAMP_HEATER_ENABLE Gpio_SetIO(GpioPortB, GpioPin5)
#define IO_VDAMP_HEATER_DISABLE Gpio_ClrIO(GpioPortB, GpioPin5)

void Board_InitPins(void);
void Board_InitSwdGpio(void);

#endif
