/*!
 * @file
 * @brief This is the header file for the initialization module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __CORE_INIT_H__
#define __CORE_INIT_H__

/*!
 * @brief This defines the type for the functions listed in the TS_Core_Init_Item
 * Basically, it is just a function with no parameters and no return value.
 * @note This is just like the schedule table.
 */
typedef void (*T_Core_Init_Function)(void);

/*!
 * @brief This is the structure for each init table entry. pfInitFunction is the address of
 * the initialization function.
 * @note There the table could be a just an array of function pointers rather than an
 * array of this structure, but using this structure keeps it consistent with the Schedule
 * Table structure and require no overhead.
 */
typedef struct
{
    T_Core_Init_Function pfInitFunction;
} st_CoreInitItem;

/*!
 * @brief This function when called will loop through the functions in the CoreUser_InitTable
 * and call each.  It is intended that this is only called once by Core_Main_Execute, or
 * possibly in the case where the firmware wants to reinitialize every module in the system.
 *@note It is expected that any module is ready to operate after the initialization function
 *is called.
 */
extern void Core_Init_Execute(void);

#endif
