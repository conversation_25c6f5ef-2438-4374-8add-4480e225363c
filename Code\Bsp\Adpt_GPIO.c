/*!
 * @file
 * @brief GPIO adapter.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "Adpt_GPIO.h"
#include "sysctrl.h"

stc_gpio_cfg_t stcGpioCfg = {
    .bOutputVal = FALSE,
    .enDir = GpioDirOut,
    .enDrv = GpioDrvH,
    .enPu = GpioPuDisable,
    .enPd = GpioPdDisable,
    .enOD = GpioOdDisable,
    .enCtrlMode = GpioFastIO,
};

void Board_InitPins(void)
{
    // 打开GPIO外设时钟门控
    Sysctrl_SetPeripheralGate(SysctrlPeripheralGpio, TRUE);

    // PIN1:     VCAP
    // PIN2:     PC13 未使用，普通IO输出，低
    Gpio_Init(GpioPortC, GpioPin13, &stcGpioCfg);
    // PIN3:     PC14 未使用，普通IO输出，低
    Gpio_Init(GpioPortC, GpioPin14, &stcGpioCfg);
    // PIN4:     PC15 未使用，普通IO输出，低
    Gpio_Init(GpioPortC, GpioPin15, &stcGpioCfg);
    // PIN52:    PC11  未使用，普通IO输出，低
    Gpio_Init(GpioPortC, GpioPin11, &stcGpioCfg);

    // PIN5:     PF00 II2C0_SDA		HUM_SDA
    // PIN6:     PF01 II2C0_SCL      AMB_SCL

    // PIN7:     RESET

    // PIN8:    PC00  AIN10     HUM_SNR
    Gpio_SetAnalogMode(GpioPortC, GpioPin0);
    // PIN9:    PC01  AIN11     AMB_SNR
    Gpio_SetAnalogMode(GpioPortC, GpioPin1);
    // PIN10:   PC02  AIN12     F_SNR
    Gpio_SetAnalogMode(GpioPortC, GpioPin2);
    // PIN11:   PC03  AIN13     FD_SNR
    Gpio_SetAnalogMode(GpioPortC, GpioPin3);

    // PIN12:     AVSS
    // PIN13:     AVCC

    // PIN14:    PA00  AIN0     R_SNR
    Gpio_SetAnalogMode(GpioPortA, GpioPin0);
    // PIN15:    PA01  AIN1     VV_SNR
    Gpio_SetAnalogMode(GpioPortA, GpioPin1);
    // PIN16:    PA02  AIN2     V_SNR
    Gpio_SetAnalogMode(GpioPortA, GpioPin2);

    // PIN17:    PA03  UART1_RXD     BP_RX
    stcGpioCfg.enDir = GpioDirIn;
    Gpio_Init(GpioPortA, GpioPin3, &stcGpioCfg);
    Gpio_SetAfMode(GpioPortA, GpioPin3, GpioAf1);

    // PIN18:     PF04 普通IO输出，   LC_Heater
    stcGpioCfg.enDir = GpioDirOut;
    Gpio_Init(GpioPortF, GpioPin4, &stcGpioCfg);
    // PIN19:     PF05 普通IO输出，   FD_Heater
    Gpio_Init(GpioPortF, GpioPin5, &stcGpioCfg);

    // // PIN20:    PA04  PCA_CH4
    // Gpio_Init(GpioPortA, GpioPin4, &stcGpioCfg);
    // Gpio_SetAfMode(GpioPortA, GpioPin4, GpioAf3);
    // PIN20:    PA04  UART1_TXD
    Gpio_Init(GpioPortA, GpioPin4, &stcGpioCfg);
    Gpio_SetAfMode(GpioPortA, GpioPin4, GpioAf2);

    // PIN21:    PA05  AIN5     AC_AD
    Gpio_SetAnalogMode(GpioPortA, GpioPin5);
    // PIN22:    PA06  AIN6     12V_AD
    Gpio_SetAnalogMode(GpioPortA, GpioPin6);

    // PIN23:    PA07  TIM2_CHA，   MCU_RL_TOP_LED
    Gpio_Init(GpioPortA, GpioPin7, &stcGpioCfg);
    Gpio_SetAfMode(GpioPortA, GpioPin7, GpioAf5);

    // PIN24:     PC04 普通IO输出，   MCU_LZ_LED
    stcGpioCfg.enDir = GpioDirOut;
    Gpio_Init(GpioPortC, GpioPin4, &stcGpioCfg);
    // PIN25:     PC05 普通IO输出，   MCU_CBX_LED
    Gpio_Init(GpioPortC, GpioPin5, &stcGpioCfg);
    // PIN26:    PB00   普通IO输出，   MCU_V_TOP_LED
    Gpio_Init(GpioPortB, GpioPin0, &stcGpioCfg);

    // PIN27:    PB01   PWM输出，TIM3_CH2B    MCU_R_PWM
    stcGpioCfg.bOutputVal = TRUE;
    Gpio_Init(GpioPortB, GpioPin1, &stcGpioCfg);
    Gpio_SetAfMode(GpioPortB, GpioPin1, GpioAf4);
    stcGpioCfg.bOutputVal = FALSE;

    // PIN28:    PB02   普通IO输出，   FM2_IN2
    Gpio_Init(GpioPortB, GpioPin2, &stcGpioCfg);
    // PIN29:    PB10  普通IO输出，    FM2_IN1
    Gpio_Init(GpioPortB, GpioPin10, &stcGpioCfg);

    // PIN30:    PB11  LPUART0 RX     DIS_RX
    stcGpioCfg.enDir = GpioDirIn;
    Gpio_Init(GpioPortB, GpioPin11, &stcGpioCfg);
    Gpio_SetAfMode(GpioPortB, GpioPin11, GpioAf3);

    // PIN31:  DVSS
    // PIN32:  DVCC

    // PIN33:  PB12 LPUART0 TX     DIS_TX
    stcGpioCfg.enDir = GpioDirOut;
    Gpio_Init(GpioPortB, GpioPin12, &stcGpioCfg);
    Gpio_SetAfMode(GpioPortB, GpioPin12, GpioAf3);

    // PIN34:  PB13 普通IO输出，   FM2_EN
    Gpio_Init(GpioPortB, GpioPin13, &stcGpioCfg);
    // PIN35:  PB14 普通IO输出，   MCU_FZ_TOP_LED
    Gpio_Init(GpioPortB, GpioPin14, &stcGpioCfg);
    // PIN36:  PB15 普通IO输出，   LED2
    Gpio_Init(GpioPortB, GpioPin15, &stcGpioCfg);

    // PIN37:    PC06  UART3_RXD     MCU_WIFI_RXD
    stcGpioCfg.enDir = GpioDirIn;
    Gpio_Init(GpioPortC, GpioPin6, &stcGpioCfg);
    Gpio_SetAfMode(GpioPortC, GpioPin6, GpioAf6);
    // PIN38:    PC07  UART3_TXD     MCU_WIFI_TXD
    stcGpioCfg.enDir = GpioDirOut;
    Gpio_Init(GpioPortC, GpioPin7, &stcGpioCfg);
    Gpio_SetAfMode(GpioPortC, GpioPin7, GpioAf6);

    // PIN39:  PC08 普通IO输出，   FM_IN2
    Gpio_Init(GpioPortC, GpioPin8, &stcGpioCfg);
    // PIN40:  PC09 普通IO输出，   FM_IN1
    Gpio_Init(GpioPortC, GpioPin9, &stcGpioCfg);
    // PIN41:  PA08 普通IO输出，   FM_EN
    Gpio_Init(GpioPortA, GpioPin8, &stcGpioCfg);

    // PIN42:  PA09  普通IO输入，   MCU_C_FB_PWM
    stcGpioCfg.enDir = GpioDirIn;
    Gpio_Init(GpioPortA, GpioPin9, &stcGpioCfg);

    // PIN43: PA10 TIM3_CH2A，   MCU_F_FAN
    stcGpioCfg.enDir = GpioDirOut;
    Gpio_Init(GpioPortA, GpioPin10, &stcGpioCfg);
    Gpio_SetAfMode(GpioPortA, GpioPin10, GpioAf2);
    
    // PIN44: PA11 TIM4_CHB, MCU_C_FAN
    stcGpioCfg.enDir = GpioDirOut;
    Gpio_Init(GpioPortA, GpioPin11, &stcGpioCfg);
    Gpio_SetAfMode(GpioPortA, GpioPin11, GpioAf7);

    // PIN45: PA12  普通IO输入，   RL_SW
    stcGpioCfg.enDir = GpioDirIn;
    Gpio_Init(GpioPortA, GpioPin12, &stcGpioCfg);

    // PIN46:  PA13/SWDIO, 调试口SWDIO

    // PIN47: PF06  普通IO输入，   RR_SW
    Gpio_Init(GpioPortF, GpioPin6, &stcGpioCfg);
    // PIN48: PF07  普通IO输入，   FL_SW
    Gpio_Init(GpioPortF, GpioPin7, &stcGpioCfg);

    // PIN49: PA14/SWCLK, 调试口SWCLK

    // PIN50:PA15 普通IO输入，   FR_SW
    Gpio_Init(GpioPortA, GpioPin15, &stcGpioCfg);

    // PIN51:    PC10  Led
    stcGpioCfg.enDir = GpioDirOut;
    Gpio_Init(GpioPortC, GpioPin10, &stcGpioCfg);

    stcGpioCfg.enDir = GpioDirIn;
    // PIN53: PC12  普通IO输入，   FengDao/CF_FB
    Gpio_Init(GpioPortC, GpioPin12, &stcGpioCfg);
    // PIN54: PD02  普通IO输入，   MCU_F_FB
    Gpio_Init(GpioPortD, GpioPin2, &stcGpioCfg);
    // PIN55: PB03  普通IO输入，   KEY
    Gpio_Init(GpioPortB, GpioPin3, &stcGpioCfg);

    // PIN56:PB04 普通IO输出  MCU_RDAM_HT
    stcGpioCfg.enDir = GpioDirOut;
    Gpio_Init(GpioPortB, GpioPin4, &stcGpioCfg);
    // PIN57:PB05 普通IO输出  MCU_VDAM_HT
    Gpio_Init(GpioPortB, GpioPin5, &stcGpioCfg);

    
    // PIN58:PB06 普通IO输出  Value_B-
    stcGpioCfg.enDir = GpioDirOut;
    Gpio_Init(GpioPortB, GpioPin6, &stcGpioCfg);
    // PIN59:PB07  普通IO输出  Vqlue_A-
    Gpio_Init(GpioPortB, GpioPin7, &stcGpioCfg);

    // PIN60:BOOT0

    // PIN61:PB08  普通IO输出  Valve_B+
    stcGpioCfg.enDir = GpioDirOut;
    Gpio_Init(GpioPortB, GpioPin8, &stcGpioCfg);
    // PIN62:PB09  普通IO输出  Valve_A+
    Gpio_Init(GpioPortB, GpioPin9, &stcGpioCfg);
}

void Board_InitSwdGpio(void)
{
    Sysctrl_SetFunc(SysctrlSWDUseIOEn, TRUE);

    // PIN46:    PA13  UART0_RXD     TEST_RXD
    stcGpioCfg.enDir = GpioDirIn;
    Gpio_Init(GpioPortA, GpioPin13, &stcGpioCfg);
    Gpio_SetAfMode(GpioPortA, GpioPin13, GpioAf2);
    // PIN49:    PA14  UART0_TXD     TEST_TXD
    stcGpioCfg.enDir = GpioDirOut;
    Gpio_Init(GpioPortA, GpioPin14, &stcGpioCfg);
    Gpio_SetAfMode(GpioPortA, GpioPin14, GpioAf2);
}
