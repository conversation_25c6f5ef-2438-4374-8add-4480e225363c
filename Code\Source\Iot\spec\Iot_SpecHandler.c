/*
 * Iot_SpecHandler.c
 *
 *  Created on: 2023�?7�?3�?
 *      Author: Mi
 */

#include <string.h>
#include "Iot_SpecHandler.h"
#include "iot_operation_encoder.h"
#include "iot_operation_decoder.h"
#include "arch_os.h"
#include "util.h"
#include "miio_api.h"
#include "Sbus_Display.h"
#include "Sbus_IceMaker.h"



#define REPORT_PROP_CHANGED_MAX_NUM		15

static TLong elapse_ms_after_connect = 0;
static TLong elapse_ms_after_washing = 0;
static TBool iot_operated = false;

TLong current_date=0; 


void receive_plugin_connect(void)
{
	set_plugin_connected();

	elapse_ms_after_connect = 0;
}


void receive_plugin_disconnect(void)
{
	set_plugin_disconnected();
	reset_all_prop_updated_by_plugin();
}

void countdown_for_plugin_link(void)
{
	elapse_ms_after_connect++;

	if ((elapse_ms_after_connect > PLUGIN_LINK_TIMEOUT) && (is_plugin_connected() == TRUE))
	{
		receive_plugin_disconnect();
	}
}

void trig_system_reset(void)
{    
    trig_fridge_event(EVENT_RESET);
}

void trig_ice_clean_finish(void)
{
    uint8_t u8_alarm = 0;
    static uint8_t u8_alarmBck = 0;

    GetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_CLEAN_STATE, &u8_alarm);
    
    if (u8_alarm != u8_alarmBck)
    {
        u8_alarmBck = u8_alarm;
        if (u8_alarmBck == 1)
        {
            trig_fridge_event(EVENT_ICE_CLEAN_FINISH);
        }
    }
}

void trig_food_remind_notice(void)
{
    uint8_t u8_alarm = 0;
    static uint8_t u8_alarmBck = 0;

    GetDisplayPropertyValue(DISPLAY_PROPERTY_TYPE_FOOD_ALARM, &u8_alarm);
    
    if (u8_alarm != u8_alarmBck)
    {
        u8_alarmBck = u8_alarm;
        trig_fridge_event(EVENT_FOOD_REMIND_NOTICE);
    }
}

void trig_data_logging(void)
{
	trig_fridge_event(EVENT_DATA_LOGGING);
}


//void trig_start_wash(void)
//{
//	elapse_ms_after_washing = 0;
//	set_dev_washing(TRUE);
//	trig_fridge_event(EVENT_START_WASH);
//}

//void trig_end_wash(TByte reason)
//{
//	set_dev_washing(FALSE);

//	if (reason == 0)
//	{
//		trig_fridge_event(EVENT_STOP_WASH);
//	}
//}

//void trig_end_cavity_clean(void)
//{
//	trig_fridge_event(EVENT_CLEAN_COMPLETED);
//}



//void update_wash_duration(void)
//{
//	static int factory_run_ms = 0;

//	if (is_dev_washing())
//	{
//		elapse_ms_after_washing++;
//	}

//	if (factory_run_ms++ >= 5000)
//	{
//		factory_run_ms = 0;

//		trig_fridge_event(EVENT_TRIG_FACTORY_DATA);		
//	}
//}

TBool need_reset_device_timeout(void)
{
	TBool reset = iot_operated;

	iot_operated = false;

	return reset;
}


void set_network_state(const char* pState)
{
	net_state_e net_state_cur;
	net_state_e net_state_prev;

	if (pState == NULL)
		return;

	net_state_prev = get_dev_net_state();

	if (strncmp(pState, NET_STATE_OFFLINE, strlen(NET_STATE_OFFLINE)) == 0)
	{
		net_state_cur = ZM_APP_NET_STATE_OFFLINE;
	}
	else if (strncmp(pState, NET_STATE_LOCAL, strlen(NET_STATE_LOCAL)) == 0)
	{
		net_state_cur = ZM_APP_NET_STATE_LOCAL;
	}
	else if (strncmp(pState, NET_STATE_CLOUD, strlen(NET_STATE_CLOUD)) == 0)
	{
		net_state_cur = ZM_APP_NET_STATE_CLOUD;
	}
	else if (strncmp(pState, NET_STATE_UPDATING, strlen(NET_STATE_UPDATING)) == 0)
	{
		net_state_cur = ZM_APP_NET_STATE_UPDATING;
	}
	else if (strncmp(pState, NET_STATE_UAP, strlen(NET_STATE_UAP)) == 0)
	{
		net_state_cur = ZM_APP_NET_STATE_UAP;
	}
	else if (strncmp(pState, NET_STATE_UNPROV, strlen(NET_STATE_UNPROV)) == 0)
	{
		net_state_cur = ZM_APP_NET_STATE_UNPROV;
	}
	else if (strncmp(pState, NET_STATE_UPDATING_AUTO, strlen(NET_STATE_UPDATING_AUTO)) == 0)
	{
		net_state_cur = ZM_APP_NET_STATE_UPDATING_AUTO;
	}
	else if (strncmp(pState, NET_STATE_UPDATING_FORCE, strlen(NET_STATE_UPDATING_FORCE)) == 0)
	{
		net_state_cur = ZM_APP_NET_STATE_UPDATING_FORCE;
	}
	else
	{
		return;
	}

	if (net_state_prev < ZM_APP_NET_STATE_CLOUD && net_state_cur == ZM_APP_NET_STATE_CLOUD)
	{
		if (!is_dev_has_inited())
		{
			set_dev_inited();
			trig_fridge_event(EVENT_FIRST_CONNECT_NETWORK);
		}
		else
		{
			trig_fridge_event(EVENT_CONNECT_NETWORK);
		}
	}
	else if (net_state_prev == ZM_APP_NET_STATE_CLOUD && net_state_cur != ZM_APP_NET_STATE_CLOUD)
	{
		trig_fridge_event(EVENT_DISCONNECT_NETWORK);
	}

	switch(net_state_prev)
	{
		case ZM_APP_NET_STATE_UAP:
		case ZM_APP_NET_STATE_UNPROV:
		case ZM_APP_NET_STATE_NONE:
			break;
		default:
		{
//			if (net_state_cur == ZM_APP_NET_STATE_UAP)			
//			{
//				set_plugin_disconnected();
//				reset_all_prop_to_unreported();
//				APP_LOG_IOT("wifi reset, set all prop to unreported!\r");
//				//TODO: clear user config data

//				CustomSetting_ResetCustomSettingFlashData();
//				ServiceTest_ResetServiceFlashData();
//			}

		}
			
	}
	
	set_dev_net_state(net_state_cur);
}


void update_current_date(const char* pDate)
{
    int len = 0;
	char factory_report_date[DATE_INFO_LENGTH+1];	
	char* ptemp = factory_report_date;
	TLong date = 0;

	date = 0; 	

	memset(factory_report_date,0, DATE_INFO_LENGTH+1);

	strncpy(factory_report_date, pDate, DATE_INFO_LENGTH);
	
    while( (*ptemp != '\0') && (*ptemp != ' ') )
    {
    	if (len >= DATE_TO_INT_LENGTH)
			break;
    
    	if (*ptemp != '-')
    	{
    		date = date*10 + *ptemp - '0';  
        	len++;
    	}
		
        ptemp++;
    }

	APP_LOG("formated time value is:%ld\r", date);	

	if (date > MIN_CURRENT_DATE)
	{
		current_date = date;
	}

}

void set_wifi_arch_platform_state(const char* pState)
{
	if (strncmp(pState, WIFI_ARCH_PLATFORM, strlen(WIFI_ARCH_PLATFORM)) == 0)
	{
		set_wifi_arch_platform();
	}
}

TBool is_local_date_valid(void)
{
	if (current_date != 0)
		return TRUE;

	return FALSE;
}


void sync_fridge_params_set_by_iot(callbackSetFridgeParam callback)
{
	IotGeneralPropVal_t genPropVal;
	IotExecuteRet_e result = IOT_EXECUTE_OK;
	TByte response_num = get_fridge_param_response_nums();
	IotPropOper_result_t* p_propResult;

	for (TByte id = 0; id < response_num; id++)
	{
		p_propResult = get_fridge_param_response(id);

		//if (p_propResult->code == IOT_EXECUTE_OK && (!is_fridge_param_ready(id)))
		if (need_sync_fridge_param_to_dev((IotExecuteRet_e)p_propResult->code, p_propResult->prop) && (!is_fridge_param_ready(id)))
		{
			if (p_propResult->code == IOT_EXECUTE_OK)
			{
				result = get_fridge_param_setby_iot(p_propResult->prop, &genPropVal);
			}
			else
			{
				result = get_fridge_param_from_dev(p_propResult->prop, &genPropVal);
			}

			if (result == IOT_EXECUTE_OK)
			{
				if (callback != NULL)
				{
					//prop type set to DWORD, maybe changed in other products
					if(genPropVal.propType == IOT_TYPE_STRING)
					{
						genPropVal.lValue = (TLong)genPropVal.sValue;
					}
					result = callback(p_propResult->prop, genPropVal.lValue);
				}
				
				if (result != IOT_EXECUTE_NEED_WAIT)
				{

					p_propResult->code = (result==IOT_EXECUTE_OK)?IOT_EXECUTE_OK:IOT_EXECUTE_DEV_FAIL;					
					p_propResult->ready = true;
					
					if (p_propResult->code == IOT_EXECUTE_OK)
					{
						finish_iot_setprop_execute(p_propResult->prop, result);

						//resolve issue that dev layer can't handle multi cmd include power_on
						// if (p_propResult->prop == IOT_PROP_G_ON)
						// {
						// 	APP_LOG_IOT("processing power_on, other cmd send at next cycle\r");
						// 	break;
						// }
					}

				}
				else
				{
					break;
				}

			}
			else
			{
				finish_iot_setprop_execute(p_propResult->prop, IOT_EXECUTE_FAIL);
				p_propResult->code = IOT_EXECUTE_FAIL;
				
				p_propResult->ready = true;
			}			
			
		}
	}


}

TBool handled_action_by_iot(IotActionName_e actionName, TLong* value, TByte length)
{
	if (actionName == IOT_ACTION_PLUGIN_CONNECT)
	{
		receive_plugin_connect();

		return TRUE;
	}
	else if (actionName == IOT_ACTION_PLUGIN_DISCONNECT)
	{
		receive_plugin_disconnect();
		return TRUE;
	}

	return FALSE;
}

void sync_action_cached_by_iot(callbackInvokeAction callback)
{
	if (is_fridge_action_cached())
	{
		IotAction_result_t* p_actionResult = get_fridge_action_cached();


		if (p_actionResult->code == IOT_EXECUTE_OK)
		{
			TLong* params;
			TByte param_len;
			IotExecuteRet_e result = IOT_EXECUTE_OK;



			params= get_action_fridge_params(p_actionResult->action, &param_len);

			if (params != NULL)
			{
				if (!handled_action_by_iot(p_actionResult->action, params, param_len))
				{
					iot_operated = true;

					if (is_remote_control_allowed())
					{
						if (callback != NULL)
						{
							result = (callback(p_actionResult->action, params, param_len) == IOT_EXECUTE_OK)?IOT_EXECUTE_OK:IOT_EXECUTE_DEV_FAIL;
						}
					}
					else
					{
						result = IOT_EXECUTE_CHILD_PROTECTED;
					}
				}

				if (result != IOT_EXECUTE_NEED_WAIT)
				{
					finish_iot_action_execute(p_actionResult->action, result);
					p_actionResult->code = result;
				}
			}
			else
			{
				finish_iot_action_execute(p_actionResult->action, IOT_EXECUTE_FAIL);
				p_actionResult->code = IOT_EXECUTE_FAIL;
			}
		}

	}
}


TBool is_wait_dev_async_execute(void)
{
	TByte response_num = get_fridge_param_response_nums();
	TBool action_cached =  is_fridge_action_cached();

	if (response_num > 0 || action_cached)
	{

		return TRUE;
	}

	return FALSE;
}


TBool response_for_last_execute(char *pResult)
{
	TByte response_num = get_fridge_param_response_nums();


	if (response_num > 0)
	{
		TBool bReady = TRUE;
		TByte id = 0;

		for (id = 0; id < response_num; id++)
		{
			if (!is_fridge_param_ready(id))
			{
				bReady = FALSE;
				break;
			}
		}


		if (bReady)
		{
			IotPropOper_result_t* p_propResult;

			str_n_cat(pResult, 1, "result ");


			for (id = 0; id < response_num; id++)
			{
				p_propResult = get_fridge_param_response(id);

				iot_property_operation_encode(p_propResult, pResult, CMD_RESULT_BUF_SIZE, TRUE);
			}

			iot_operation_encode_end(pResult, CMD_RESULT_BUF_SIZE);

			if(miio_uart_send_str(pResult) <= 0) {
				APP_LOG( "set property send failed");
			}


			reset_iot_setprop_excute_state();

		}


		return TRUE;
	}

	if (is_fridge_action_cached())
	{

		if (is_fridge_action_ready())
		{
			IotAction_result_t* p_actionResult = get_fridge_action_cached();

			if (iot_action_operation_encode(p_actionResult, pResult, CMD_RESULT_BUF_SIZE) == MIIO_OK)
			{
				TByte paramNum;
			    char piid_buf[ID_MAX_LEN+1] = {0};			
				IotGeneralPropVal_t genPropVal;			

				paramNum = get_action_fridge_out_param_num(p_actionResult->action);
			
				for (int i = 0; i < paramNum; i++)
				{	
					get_action_fridge_out_param_value(p_actionResult->action, i, &genPropVal);
				
				
					sprintf(piid_buf, "%d", get_spec_piid_by_action_out_index(p_actionResult->action, i));
					str_n_cat(pResult, 2, piid_buf, " ");		
				
					iot_operation_value_encode(&genPropVal, pResult, CMD_RESULT_BUF_SIZE);				
				}
				
			}

			iot_operation_encode_end(pResult, CMD_RESULT_BUF_SIZE);		

			if(miio_uart_send_str(pResult) <= 0) {
				APP_LOG( "invoke action send failed");
			}

			reset_fridge_action_cached();
		}

		return TRUE;
	}


	return FALSE;
}

IotExecuteRet_e custom_get_factorydata_value(IotPropName_e prop, IotGeneralPropVal_t* pValue)
{
	IotExecuteRet_e ret = IOT_EXECUTE_FAIL;

	switch (prop)
	{
//		case IOT_FCT_PROP_STATUS:
//			//get_fridge_param_from_dev(IOT_PROP_STATUS, pValue);
//			ret = IOT_EXECUTE_OK;
//			break;
//		case IOT_FCT_PROP_LEFT_TIME:
//			//get_fridge_param_from_dev(IOT_PROP_LEFT_TIME, pValue);			
//			ret = IOT_EXECUTE_OK;			
//			break;
//		case IOT_FCT_PROP_RUN_STATUS:
//			//get_fridge_param_from_dev(IOT_PROP_RUN_STATUS, pValue);			
//			ret = IOT_EXECUTE_OK;			
//			break;
//		case IOT_FCT_PROP_TEMPERATURE:
//			break;
//		case IOT_FCT_PROP_UNBALANCED_TRY_NUMS:
//			break;
//		case IOT_FCT_PROP_WEIGHING_VALUE:
//			break;
//		case IOT_FCT_PROP_WATER_LEVEL_FREQ:
//			break;
//		case IOT_FCT_PROP_PRESPIN_OOB:
//			break;
//		case IOT_FCT_PROP_MAINSPIN_OOB:
//			break;
//		case IOT_FCT_PROP_TARRGET_SPIN_SPEED:
//			break;
//		case IOT_FCT_PROP_SPIN_SPEED:
//			break;
//		case IOT_FCT_PROP_DETERGENT_DELI_TIME:
//			break;
//		case IOT_FCT_PROP_SOFTENER_DELI_TIME:
//			break;
//		case IOT_FCT_PROP_COMPONENTS_STATUS:
//			break;
		default:
			break;
	}

	return ret;

}


IotExecuteRet_e custom_get_datalogging_value(IotPropName_e prop, IotGeneralPropVal_t* pValue)
{
	switch (prop)
	{
//		case IOT_DL_PROP_MODE:
//			get_fridge_param_from_dev(IOT_PROP_MODE, pValue);
//			break;
//		case IOT_DL_PROP_TEMPERATURE:
//			//get_fridge_param_from_dev(IOT_PROP_TEMPERATURE, pValue);			
//			break;
//		case IOT_DL_PROP_RINSH_TIMES:
//			//get_fridge_param_from_dev(IOT_PROP_RINSH_TIMES, pValue);				
//			break;
//		case IOT_DL_PROP_SPIN_SPEED:
//			//get_fridge_param_from_dev(IOT_PROP_SPIN_SPEED, pValue);			
//			break;
//		case IOT_DL_PROP_SOAK_TIME:
//			//get_fridge_param_from_dev(IOT_PROP_SOAK_TIME, pValue);			
//			break;
//		case IOT_DL_PROP_DOOR_STATE:
//			get_fridge_param_from_dev(IOT_PROP_DOOR_STATE, pValue);				
//			break;
//		case IOT_DL_PROP_RESERV_LEFTTIME:
//			//get_fridge_param_from_dev(IOT_PROP_RESERV_LEFTTIME, pValue);				
//			break;

//		case IOT_DL_PROP_SHAKE_TIME:
//			//get_fridge_param_from_dev(IOT_PROP_SHAKE_TIME, pValue);			
//			break;
//		case IOT_DL_PROP_OPTION:
//			{
//				// TLong lOption = 0;			
//				
//				// get_fridge_param_from_dev(IOT_PROP_G_DETERGENT_DELIVERY, pValue);
//				
//				// lOption |= pValue->lValue&0x1;
//				
//				// get_fridge_param_from_dev(IOT_PROP_G_FABRIC_DELIVERY, pValue);
//				
//				// lOption |= (pValue->lValue&0x1) <<1;
//				
//				
//				// get_fridge_param_from_dev(IOT_PROP_SLEEP_MODE, pValue);
//				
//				// lOption |= (pValue->lValue&0x1) <<4;
//				
//				// get_fridge_param_from_dev(IOT_PROP_STEAM, pValue);
//				
//				// lOption |= (pValue->lValue&0x1) <<5;
//				
//				// get_fridge_param_from_dev(IOT_PROP_SHAKE_TIME, pValue);
//				
//				// if (pValue->lValue > 0)
//				// {
//				// 	lOption |= 0x1 <<6;
//				// }

//				// get_fridge_param_from_dev(IOT_PROP_G_CHILD_PROTECTED_ENABLED, pValue);

//				// lOption |= (pValue->lValue&0x1) <<9;

//				// get_fridge_param_from_dev(IOT_PROP_G_CHILD_PROTECTED_STATUS, pValue);

//				// lOption |= (pValue->lValue&0x1) <<10;

//				// pValue->lValue = lOption;
//				// pValue->propType = IOT_TYPE_LONG;
//			}
//			break;
//		case IOT_DL_PROP_DURATION:
//			pValue->lValue = (TLong)(elapse_ms_after_washing/60000);
//			pValue->propType = IOT_TYPE_LONG;				
//			break;
		default:
			return IOT_EXECUTE_FAIL;
	}

	return IOT_EXECUTE_OK;
}
	
void response_properties_changed(int ret)
{
	static TByte retry = 0;

	if(ret != 0 && retry < 3) 
	{
		retry++;
		for (IotPropName_e prop = IOT_PROP_MIN; prop < IOT_PROP_MAX; prop++)
		{
			if(is_fridge_param_uploading(prop))
			{
				set_fridge_param_updated_by_event(prop);
				clear_fridge_param_uploading(prop);
			}
		}
	}
	else
	{
		retry = 0;
		for (IotPropName_e prop = IOT_PROP_MIN; prop < IOT_PROP_MAX; prop++)
		{
			if(is_fridge_param_uploading(prop))
			{
				clear_fridge_param_uploading(prop);
			}
		}
	}

}

TBool report_properties_changed_by_event(TWord event,  char* pResult)
{
	TBool sendHead = FALSE;
	IotGeneralPropVal_t genPropVal;
	IotPropOper_result_t prop_Oper;	 
	TByte report_num = 0;

	prop_Oper.pValue = &genPropVal;

	for (IotPropName_e prop = IOT_PROP_MIN; prop < IOT_PROP_MAX; prop++)
	{
		TBool needReport = FALSE;
		TBool eventTrig = FALSE;

		if (is_fridge_param_updated(prop))
		{
			needReport = TRUE;
		}

		if (!needReport)
		{
			switch(event)
			{
//				case EVENT_START_WASH:
//					if (is_fridge_param_policy_matched(prop, PROP_POLICY_REPORT_ON_WASH_START))
//						needReport = TRUE;
//					break;
//				case EVENT_STOP_WASH:
//					if (is_fridge_param_policy_matched(prop, PROP_POLICY_REPORT_ON_WASH_END))
//						needReport = TRUE;
//					break;
//				case EVENT_TRIG_NEW_FAULT:
//					if (is_fridge_param_policy_matched(prop, PROP_POLICY_REPORT_ON_FAULT))
//						needReport = TRUE;
					break;
				case EVENT_FIRST_CONNECT_NETWORK:
				case EVENT_CONNECT_NETWORK:
					if (is_fridge_param_policy_matched(prop, PROP_POLICY_BASIC_INFO))
						needReport = TRUE;
					break;
				default:
					break;
			}

			if (needReport)
				eventTrig = TRUE;
		}

		if (needReport)
		{
			report_num++;

			if (report_num <= REPORT_PROP_CHANGED_MAX_NUM)
			{
				if (!sendHead)
				{
					memset(pResult, 0, CMD_RESULT_BUF_SIZE);
					str_n_cat(pResult, 2, "properties_changed", " ");

					sendHead = TRUE;
				}

				prop_Oper.siid = get_spec_siid_by_propname(prop);
				prop_Oper.piid = get_spec_piid_by_propname(prop);

				get_fridge_param_from_dev(prop, prop_Oper.pValue);

				// if (is_fridge_param_policy_matched(prop, PROP_POLICY_LIST_VALUE))
				// {
				// 	TLong lValue = prop_Oper.pValue->lValue;

				// 	map_list_idx_and_val(prop, lValue, &(prop_Oper.pValue->lValue), false);
				// }

				iot_changed_operation_encode(&prop_Oper, pResult, CMD_RESULT_BUF_SIZE);

				set_fridge_param_uploading_by_event(prop);
				clear_fridge_param_updated(prop);
			}
			else
			{
				if (eventTrig)
				{
					set_fridge_param_updated_by_event(prop);
				}
				
			}
		}
	}


	if (sendHead)
	{		
		iot_operation_encode_end(pResult, CMD_RESULT_BUF_SIZE);

#if 0
		if(miio_uart_send_str(pResult) <= 0) {
			APP_LOG( "property changed send failed");
		}
#endif		

		return TRUE;
	}

	return FALSE;

}


TBool report_properties_changed_to_iot(char *pResult)
{
	//TODO:events need consider timeliness, if network not ready when event occured, just drop it
	if (is_dev_connected_iot_svr())
	{

		//report on device init
		if (is_fridge_event_exist(EVENT_FIRST_CONNECT_NETWORK))
		{
			clear_fridge_event(EVENT_FIRST_CONNECT_NETWORK);

			return report_properties_changed_by_event(EVENT_FIRST_CONNECT_NETWORK, pResult);
		}


		//report on network_connected
		if (is_fridge_event_exist(EVENT_CONNECT_NETWORK))
		{
			clear_fridge_event(EVENT_CONNECT_NETWORK);

			return report_properties_changed_by_event(EVENT_CONNECT_NETWORK, pResult);
		}


		//report on wash start
//		if (is_fridge_event_exist(EVENT_START_WASH))
//		{
//			clear_fridge_event(EVENT_START_WASH);
//			trig_fridge_event(EVENT_START_WASH_DATA_LOGGING);

//			return report_properties_changed_by_event(EVENT_START_WASH, pResult);
//		}


		//report on fault occured
//		if (is_fridge_event_exist(EVENT_TRIG_NEW_FAULT))
//		{
//			clear_fridge_event(EVENT_TRIG_NEW_FAULT);

//			return report_properties_changed_by_event(EVENT_TRIG_NEW_FAULT, pResult);

//		}


		//report on wash end
//		if (is_fridge_event_exist(EVENT_STOP_WASH))
//		{
//			clear_fridge_event(EVENT_STOP_WASH);
//			trig_fridge_event(EVENT_END_WASH_POST_EVENT);

//			return report_properties_changed_by_event(EVENT_STOP_WASH, pResult);
//		}

		return report_properties_changed_by_event(EVENT_HAS_NONE, pResult);

	}
//	else
//	{
//		if (is_fridge_event_exist(EVENT_START_WASH))
//			clear_fridge_event(EVENT_START_WASH);

//		if (is_fridge_event_exist(EVENT_STOP_WASH))
//			clear_fridge_event(EVENT_STOP_WASH);

//		if (is_fridge_event_exist(EVENT_TRIG_NEW_FAULT))
//			clear_fridge_event(EVENT_TRIG_NEW_FAULT);
//	}
	
	return FALSE;

}

TBool report_event_to_iot(char *pResult)
{
	if (is_dev_connected_iot_svr())
	{
		IotEventName_e event = IOT_EVENT_MAX;
			
		//report on wash end event
//		if (is_fridge_event_exist(EVENT_END_WASH_POST_EVENT))
//		{
//			clear_fridge_event(EVENT_END_WASH_POST_EVENT);
//			trig_fridge_event(EVENT_STOP_WASH_DATA_LOGGING);
//		
//			//report wash end event
//			event = IOT_EVENT_WASH_END;
//		}
//		//report on cavity clean end event
//		else if (is_fridge_event_exist(EVENT_CLEAN_COMPLETED))
//		{
//			clear_fridge_event(EVENT_CLEAN_COMPLETED);

//			event = IOT_EVENT_CLEAN_COMPLETED;
//		}
//		//report on wash start event
//		else if (is_fridge_event_exist(EVENT_START_WASH_DATA_LOGGING))
//		{
//			clear_fridge_event(EVENT_START_WASH_DATA_LOGGING);
//		

//			//report datalogging
//			event = IOT_EVENT_START_DATALOGGING;
//		}
//		//report on wash start event
//		else if (is_fridge_event_exist(EVENT_STOP_WASH_DATA_LOGGING))
//		{
//			clear_fridge_event(EVENT_STOP_WASH_DATA_LOGGING);
//		

//			//report datalogging
//			event = IOT_EVENT_END_DATALOGGING;

//		}	
//		//report on factory report event
//		else if (is_fridge_event_exist(EVENT_TRIG_FACTORY_DATA))
//		{
//			//IotGeneralPropVal_t genPropVal;
//			
//			// if (get_fridge_param_from_dev(IOT_PROP_G_FACTORY_DEADLINE, &genPropVal) == IOT_EXECUTE_OK)
//			// {
//			// 	if (current_date>0 && genPropVal.lValue > current_date)
//			// 	{
//			// 		clear_fridge_event(EVENT_TRIG_FACTORY_DATA);
//			// 		event = IOT_EVENT_DATA_REPORT;
//			// 		APP_LOG( "start factory data report!\r");
//			// 	}
//			// }
//			
//		}

        if (is_fridge_event_exist(EVENT_RESET))
		{
			clear_fridge_event(EVENT_RESET);
		
			event = IOT_EVENT_RESET;
		}         
        else if (is_fridge_event_exist(EVENT_ICE_CLEAN_FINISH))
		{
			clear_fridge_event(EVENT_ICE_CLEAN_FINISH);
		
			event = IOT_EVENT_ICE_CLEAN_FINISH;
		}         
        else if (is_fridge_event_exist(EVENT_FOOD_REMIND_NOTICE))
		{
			clear_fridge_event(EVENT_FOOD_REMIND_NOTICE);
		
			event = IOT_EVENT_FOOD_REMIND_NOTICE;
		}
        else if (is_fridge_event_exist(EVENT_DATA_LOGGING))
		{
			clear_fridge_event(EVENT_DATA_LOGGING);
		
			event = IOT_EVENT_DATALOGGING;
		} 

		if (event != IOT_EVENT_MAX)
		{
			TByte siid;
			TByte eiid;
			TByte paramNum;
			IotExecuteRet_e ret = IOT_EXECUTE_OK;
		    char piid_buf[ID_MAX_LEN+1] = {0};			
			IotGeneralPropVal_t genPropVal;
			
			siid = get_spec_siid_by_eventname(event);
			eiid = get_spec_eiid_by_eventname(event);
			
			paramNum = get_event_report_param_num(event);
			
			if (iot_event_operation_encode(siid, eiid, pResult, CMD_RESULT_BUF_SIZE) == MIIO_OK)
			{
				for (int i = 0; i < paramNum; i++)
				{	
					if (event == IOT_EVENT_DATALOGGING)
					{
						ret = get_event_report_param_value(event, i, custom_get_datalogging_value, &genPropVal);
					}
					else
					{
						ret = get_event_report_param_value(event, i, NULL, &genPropVal);
					}
				
					sprintf(piid_buf, "%d", get_spec_piid_by_event_index(event, i));
					str_n_cat(pResult, 2, piid_buf, " ");		

					if (ret == IOT_EXECUTE_OK)
						iot_operation_value_encode(&genPropVal, pResult, CMD_RESULT_BUF_SIZE);				
					else
						str_n_cat(pResult, 1, " ");								
				}
			}
			
			iot_operation_encode_end(pResult, CMD_RESULT_BUF_SIZE);

			return TRUE;

		}
	}

	return FALSE;	
}

static void on_property_set_to_buffer(IotPropOper_result_t* pIotResult)
{
	prepare_iot_setprop_execute(pIotResult->siid, pIotResult->piid, pIotResult->pValue);

	iot_operated = true;
}

static void on_property_get_from_buffer(IotPropOper_result_t* pIotResult)
{
	IotExecuteRet_e ret;
	IotPropName_e propName;

	propName = get_propname_by_spec_id((TByte)pIotResult->siid, (TByte)pIotResult->piid);

	ret = get_fridge_param_from_dev(propName, pIotResult->pValue);

	if (ret == IOT_EXECUTE_OK)
	{
		// if (is_fridge_param_policy_matched(propName, PROP_POLICY_LIST_VALUE))
		// {
		// 	TLong lValue = pIotResult->pValue->lValue;
		
		// 	map_list_idx_and_val(propName, lValue, &(pIotResult->pValue->lValue), false);
		// }

		pIotResult->code = IOT_EXECUTE_OK;

		if (is_plugin_connected() && (!is_prop_updated_by_plugin(propName)))
		{
			set_prop_updated_by_plugin(propName);
		}
	}
	else
	{
		pIotResult->code = ret;	
	}


}

static void on_action_cached(const char *pbuf, size_t buf_sz)
{
    const char *temp = pbuf;
    int pos = 0;

    if( (NULL == pbuf) || (buf_sz == 0) )
        return;

    do
    {
		uint16_t siid;
		uint16_t aiid;	
	    uint16_t piid;		
		
        // get siid
        siid = miio_atoi(temp);

        // get aiid
        pos = miio_strtok(temp, " ");
        temp = temp+pos;
        aiid = miio_atoi(temp);

		if (prepare_iot_action_execute(siid, aiid))
		{
	        // get piid & value
	        int index = 0;
			IotGeneralPropVal_t genPropVal;			
			
	        while((pos = miio_strtok(temp, " ")) != 0) {
	            temp = temp+pos;
	            piid = miio_atoi(temp);
	            pos = miio_strtok(temp, " ");
	            temp = temp+pos;

	            if(0 == pos) {
					index = 0xFF;
	            }
				else
				{
					iot_operation_value_decode(temp, &genPropVal);
				}

				if (!prepare_iot_action_param(siid, piid, index, &genPropVal))
				{
					break;
				}
				
				index++;				

	        }

		}


    } while (0);	

}

int execute_property_operation_async(const char *pbuf, int buf_sz, bool set_value, char *presult)
{
	static char str_buf[CMD_STR_MAX_LEN + 1];

    int params_pairs = 1;
    const char *temp = pbuf;
    int pos=0;
	int step = set_value?3:2;
    IotPropName_e prop;
    IotExecuteRet_e ret;

    if( (NULL == pbuf) || (buf_sz == 0) )
        return -1;	

    while(1) {
        pos = miio_strtok(temp, " ");
        if (0 != pos) { params_pairs++;  temp += pos;}
        else { break; }
    }

    if(params_pairs % step != 0) {
        APP_LOG( "params error");
        return -1;
    }
    else {
        params_pairs /= step;
    }	

    do {
        uint32_t i;
		IotGeneralPropVal_t genPropVal;	
		IotPropOper_result_t iotPropResult;

		iotPropResult.pValue = &genPropVal;
		temp = pbuf;
		pos = 0;
	

		if (set_value) {
			reset_iot_setprop_excute_state();
		}
		else
		{
			str_n_cat(presult, 1, "result ");
		}

        for (i = 0; i < params_pairs; ++i) {

	        iotPropResult.siid = miio_atoi(temp);

	        // get piid string
	        pos = miio_strtok(temp, " ");
	        temp = temp+pos;
	        iotPropResult.piid = miio_atoi(temp);

            prop = get_propname_by_spec_id(iotPropResult.siid , iotPropResult.piid);

	        if (set_value) {
		        // get value string
		        pos = miio_strtok(temp, " ");
		        temp = temp+pos;
                
                if(prop >= IOT_PROP_MAINTENANCE_SECTION1 && prop <= IOT_PROP_MAINTENANCE_SECTION8)
                {
		    memset(str_buf, 0, CMD_STR_MAX_LEN);
		    strncpy(str_buf, temp, MIN(miio_strlen(temp), CMD_STR_MAX_LEN));
                    ret = set_maintenance_data(prop - IOT_PROP_MAINTENANCE_SECTION1, str_buf);
                    assemble_fridge_param_command(iotPropResult.siid, iotPropResult.piid,
                                                  prop, ret);
                }
		else if(IOT_PROP_LINYUN_POWER_PARAM == prop)
		{
		    memset(str_buf, 0, CMD_STR_MAX_LEN);
		    strncpy(str_buf, temp, MIN(miio_strlen(temp), CMD_STR_MAX_LEN));
		    ret = set_lypower_data(str_buf);
                    assemble_fridge_param_command(iotPropResult.siid, iotPropResult.piid,
                                                  prop, ret);
		}
                else
                {
                    iot_operation_value_decode(temp, iotPropResult.pValue);
                    on_property_set_to_buffer(&iotPropResult);
                }
        }
			else {
                if(prop == IOT_PROP_FACTORY_DATA)
                {
                    get_factory_data(presult, CMD_RESULT_BUF_SIZE);
                }
				else if(prop >= IOT_PROP_MAINTENANCE_SECTION1 && prop <= IOT_PROP_MAINTENANCE_SECTION8)
				{
					get_maintence_data(prop - IOT_PROP_MAINTENANCE_SECTION1, presult, CMD_RESULT_BUF_SIZE);
				}
				else if(IOT_PROP_LINYUN_POWER_PARAM == prop)
				{
					get_lypower_data(presult, CMD_RESULT_BUF_SIZE);
				}
                else
                {
                    on_property_get_from_buffer(&iotPropResult);
                    iot_property_operation_encode(&iotPropResult, presult, CMD_RESULT_BUF_SIZE, set_value);
                }
            }

            if( i >= REPORT_PROP_CHANGED_MAX_NUM-1 ) //21*3=63
            {
            	APP_LOG_IOT( "prop operate param exceed 20:%d\r", params_pairs);
                break;
            }

	        pos = miio_strtok(temp, " ");
	        temp = temp+pos;			
        }

		if(!set_value)
	        iot_operation_encode_end(presult, CMD_RESULT_BUF_SIZE);
    } while (false);


	return 0;
}


int execute_action_invocation_async(const char *pbuf, int buf_sz, char *presult)
{
    int ret = 0;
    int params_pairs = 1;
    const char *temp = pbuf;
    int pos=0;


    while(1) {
        pos = miio_strtok(temp, " ");
        if (0 != pos) { params_pairs++; temp += pos;}
        else { break; }
    }

    if(params_pairs >= 4 && params_pairs % 2 != 0) {
        APP_LOG( "params error");
        ret = -1;
        miio_encode_error_response(ERROR_MESSAGE_UNPARAMS, ERROR_CODE_UNPARAMS, presult);
        return ret;
    } 


    on_action_cached(pbuf, buf_sz);

	return ret;
}



