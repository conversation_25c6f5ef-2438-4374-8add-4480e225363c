/*!
 * @file
 * @brief This is the header file for the initialization module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "Core_Init.h"
#include "CoreUser_Init_Table.h"
#include "Core_Types.h"

#define NUM_INIT_FUNCTIONS \
    (sizeof(CoreUser_Init_aInitTable) / sizeof(T_Core_Init_Function))

void Core_Init_Execute(void);

void Core_Init_Execute(void)
{
    uint8_t u8_Index = 0;

    for(u8_Index = 0; u8_Index < NUM_INIT_FUNCTIONS; u8_Index++)
    {
        if((T_Core_Init_Function)(NULL) != CoreUser_Init_aInitTable[u8_Index].pfInitFunction)
        {
            CoreUser_Init_aInitTable[u8_Index].pfInitFunction();
        }
    }
}
