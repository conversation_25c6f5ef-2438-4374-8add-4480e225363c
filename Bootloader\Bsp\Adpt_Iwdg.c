/*!
 * @file
 * @brief Iwdg adapter.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stdio.h>
#include "wdt.h"
#include "lpm.h"
#include "reset.h"
#include "wwdt.h"
#include "Adpt_Iwdg.h"
#include "syslog.h"


void Wdt_IRQHandler(void)
{
    if(Wdt_GetIrqStatus())
    {
        Wdt_IrqClr();
        err("boot watchdog reset...\n");
    }
    EnableNvic(WDT_IRQn, IrqLevel0, FALSE);
    Sysctrl_SetPeripheralGate(SysctrlPeripheralWdt, FALSE);
    NVIC_SystemReset();
    while(1);
}

static void App_WdtInit(void)
{
    /* 开启WDT外设时钟 */
    Sysctrl_SetPeripheralGate(SysctrlPeripheralWdt, TRUE);
    /* WDT 初始化 */
    Wdt_Init(WdtIntEn, WdtT26s2);
    Wdt_IrqClr();
    EnableNvic(WDT_IRQn, IrqLevel0, TRUE);
}

void Board_InitIwdg(void)
{
    App_WdtInit();
    /* 启动 WDT */
    Wdt_Start();

    /* IWDT 复位现象观测 */
    if(Reset_GetFlag(ResetFlagMskWdt))
    {
        Reset_ClearFlag(ResetFlagMskWdt);
    }
}

void IWDG_Refesh(void)
{
    Wdt_Feed();
}

void Board_DeinitIwdg(void)
{
    EnableNvic(WDT_IRQn, IrqLevel0, FALSE);
    Sysctrl_SetPeripheralGate(SysctrlPeripheralWdt, FALSE);
}
