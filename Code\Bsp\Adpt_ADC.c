/*!
 * @file
 * @brief Clock adapter.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "Adpt_ADC.h"
#include "gpio.h"
#include "bgr.h"

// PA00
#define REF_SNR_GPIO_CH AdcExInputCH0
// PA01
#define VV_SNR_GPIO_CH AdcExInputCH1
// PA02
#define VAR_SNR_GPIO_CH AdcExInputCH2
// PA05
#define AC_VOLT_GPIO_CH AdcExInputCH5
// PA06
#define DC_VOLT_GPIO_CH AdcExInputCH6
// PC00
#define HUM_SNR_GPIO_CH AdcExInputCH10
// PC01
#define ROOM_SNR_GPIO_CH AdcExInputCH11
// PC02
#define FRZ_SNR_GPIO_CH AdcExInputCH12
// PC03
#define FRZ_DEF_SNRGPIO__CH AdcExInputCH13

#define U8_12BIT_TO_10BIT_SHIFT_BITS ((uint8_t)2)

volatile uint32_t u32AdcRestult[(uint8_t)eSqrChannel_Max];

/**
 * @brief  ADC 初始化
 * @retval None.
 */
void App_AdcInit(void)
{
    stc_adc_cfg_t stcAdcCfg;

    DDL_ZERO_STRUCT(stcAdcCfg);

    /* 开启ADC/BGR外设时钟 */
    Sysctrl_SetPeripheralGate(SysctrlPeripheralAdcBgr, TRUE);

    Bgr_BgrEnable(); /* 开启BGR */

    /* ADC 初始化配置 */
    stcAdcCfg.enAdcMode = AdcScanMode; /* 采样模式-扫描 */
    stcAdcCfg.enAdcClkDiv = AdcMskClkDiv8; /* 采样分频-8 */
    stcAdcCfg.enAdcSampCycleSel = AdcMskSampCycle8Clk; /* 采样周期数-8 */
    stcAdcCfg.enAdcRefVolSel = AdcMskRefVolSelAVDD; /* 参考电压选择AVDD */
    stcAdcCfg.enAdcOpBuf = AdcMskBufEnable; /* OP BUF配置-开 */
    stcAdcCfg.enInRef = AdcMskInRefDisable; /* 内部参考电压使能-关 */
    stcAdcCfg.enAdcAlign = AdcAlignRight; /* 转换结果对齐方式-右 */
    Adc_Init(&stcAdcCfg);
}

/**
 * @brief  ADC SQR扫描功能配置
 * @retval None.
 */
void App_AdcSQRCfg(void)
{
    stc_adc_sqr_cfg_t stcAdcSqrCfg;

    DDL_ZERO_STRUCT(stcAdcSqrCfg);

    /* SQR扫描模式功能及通道配置 */
    /* 注意：扫描模式下，当配置转换次数为n时，转换通道的配置范围必须为[SQRCH(0)MUX,SQRCH(n-1)MUX] */
    stcAdcSqrCfg.bSqrDmaTrig = FALSE;
    stcAdcSqrCfg.enResultAcc = AdcResultAccDisable;
    stcAdcSqrCfg.u8SqrCnt = (uint8_t)eSqrChannel_Max;
    Adc_SqrModeCfg(&stcAdcSqrCfg);

    Adc_CfgSqrChannel(AdcSQRCH0MUX, REF_SNR_GPIO_CH);
    Adc_CfgSqrChannel(AdcSQRCH1MUX, VV_SNR_GPIO_CH);
    Adc_CfgSqrChannel(AdcSQRCH2MUX, VAR_SNR_GPIO_CH);
    Adc_CfgSqrChannel(AdcSQRCH3MUX, AC_VOLT_GPIO_CH);
    Adc_CfgSqrChannel(AdcSQRCH4MUX, DC_VOLT_GPIO_CH);
    Adc_CfgSqrChannel(AdcSQRCH5MUX, HUM_SNR_GPIO_CH);
    Adc_CfgSqrChannel(AdcSQRCH6MUX, ROOM_SNR_GPIO_CH);
    Adc_CfgSqrChannel(AdcSQRCH7MUX, FRZ_SNR_GPIO_CH);
    Adc_CfgSqrChannel(AdcSQRCH8MUX, FRZ_DEF_SNRGPIO__CH);

    /* ADC 中断使能 */
    Adc_EnableIrq();

    EnableNvic(ADC_DAC_IRQn, IrqLevel3, TRUE);
    ///< 启动顺序扫描采样
    Adc_SQR_Start();
}

/**
 * @brief  ADC中断服务函数
 * @retval None.
 */
void Adc_IRQHandler(void)
{
    if(TRUE == Adc_GetIrqStatus(AdcMskIrqSqr))
    {
        Adc_ClrIrqStatus(AdcMskIrqSqr);

        u32AdcRestult[0] = Adc_GetSqrResult(AdcSQRCH0MUX); // 获取顺序扫描通道0
        u32AdcRestult[1] = Adc_GetSqrResult(AdcSQRCH1MUX); // 获取顺序扫描通道1
        u32AdcRestult[2] = Adc_GetSqrResult(AdcSQRCH2MUX); // 获取顺序扫描通道2
        u32AdcRestult[3] = Adc_GetSqrResult(AdcSQRCH3MUX); // 获取顺序扫描通道3
        u32AdcRestult[4] = Adc_GetSqrResult(AdcSQRCH4MUX); // 获取顺序扫描通道4
        u32AdcRestult[5] = Adc_GetSqrResult(AdcSQRCH5MUX); // 获取顺序扫描通道5
        u32AdcRestult[6] = Adc_GetSqrResult(AdcSQRCH6MUX); // 获取顺序扫描通道6
        u32AdcRestult[7] = Adc_GetSqrResult(AdcSQRCH7MUX); // 获取顺序扫描通道7
        u32AdcRestult[8] = Adc_GetSqrResult(AdcSQRCH8MUX); // 获取顺序扫描通道8

        Adc_SQR_Stop();
    }
}

void Board_InitAdc(void)
{
    /* ADC 模块初始化 */
    App_AdcInit();

    /* ADC SQR扫描 功能配置 */
    App_AdcSQRCfg();
}

void ADC_Restart(void)
{
    ///< 启动顺序扫描采样
    Adc_SQR_Start();
}

uint16_t ADC_GetResult(uint8_t sqr_Channel)
{
    uint16_t u16_value;

    u16_value = (uint16_t)(u32AdcRestult[sqr_Channel] >> U8_12BIT_TO_10BIT_SHIFT_BITS);

    return u16_value;
}
