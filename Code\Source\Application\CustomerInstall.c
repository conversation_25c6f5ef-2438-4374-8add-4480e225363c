/*!
 * @file
 * @brief Manages all the state variables of the showroom mode.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "CustomerInstall.h"
#include "ResolverDevice.h"
#include "Driver_SingleDamper.h"
#include "Driver_CompFrequency.h"
#include "SystemManager.h"
#include "Driver_Fan.h"
#include "Drive_Valve.h"
#include "Core_CallBackTimer.h"

#define U16_SELF_CHECK_CYCLE_SECOND (uint16_t)1
#define U16_CUSTOMER_INSTALL_SECOND (uint16_t)(3*60*60)

static st_CoreCallbackTimer st_CustomerInstallModeTimer;
static uint16_t u16_ControlCount;

static void Process_CustomerInstall(void);

static void Start_PollTimer(uint16_t tickSec)
{
    Core_CallbackTimer_TimerStart(
        &st_CustomerInstallModeTimer,
        Process_CustomerInstall,
        tickSec,
        0,
        eCallbackTimer_Type_Periodic,
        eCallbackTimer_Priority_Normal);
}

static void Stop_PollTimer(void)
{
    Core_CallbackTimer_TimerStop(&st_CustomerInstallModeTimer);
}

static void Process_CustomerInstall(void)
{
   u16_ControlCount++; 
   if(u16_ControlCount > U16_CUSTOMER_INSTALL_SECOND)
   {
      FridgeState_Update((FridgeState_t)eFridge_Running);  
   }
}

void CustomerInstallMode_Init(void)
{
    u16_ControlCount = 0;
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, 0);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, 0);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 0);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, 0);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DAMPER_AllClose);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DAMPER_AllClose);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_IonGenerator, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamperHeater, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzIonGenerator, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDefHeater, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, Valve_AllOpen);
    Start_PollTimer(U16_SELF_CHECK_CYCLE_SECOND);
}

void CustomerInstallMode_Exit(void)
{
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamper, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_IonGenerator, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VarDamperHeater, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzIonGenerator, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDefHeater, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, DS_DontCare);
    Stop_PollTimer();
}
