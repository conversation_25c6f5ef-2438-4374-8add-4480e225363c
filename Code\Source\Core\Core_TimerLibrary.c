/*!
 * @file
 * @brief This file defines public constants, types and functions for the Core Timer Library.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "Core_Types.h"
#include "Core_TimeBase.h"
#include "Core_TimerLibrary.h"

void Core_TimerLib_TimerInit(st_CoreTimerLibTimer *const pst_Timer);

void Core_TimerLib_TimerStart(st_CoreTimerLibTimer *const pst_Timer,
    const uint16_t u16_DurationSeconds,
    const uint16_t u16_DurationMilliSeconds);

void Core_TimerLib_TimerStartMeasurement(
    st_CoreTimerLibTimer *const pst_Timer);

void Core_TimerLib_TimerStop(st_CoreTimerLibTimer *const pst_Timer);

bool Core_TimerLib_IsTimerRunning(
    const st_CoreTimerLibTimer *const pst_Timer);

bool Core_TimerLib_IsTimerStopped(
    const st_CoreTimerLibTimer *const pst_Timer);

bool Core_TimerLib_IsTimerExpired(
    st_CoreTimerLibTimer *const pst_Timer);

void Core_TimerLib_GetElapsedTime(st_CoreTimerLibTimer *const pst_Timer,
    ST_Core_TimerLib_TimeUnit *const pst_DestinationTimeUnit);

uint16_t Core_TimerLib_GetRemainingTime_Seconds(
    st_CoreTimerLibTimer *const pst_Timer);

void Core_TimerLib_TimerInit(st_CoreTimerLibTimer *const pst_Timer)
{
    pst_Timer->st_Duration.u16_Seconds = CORE_TIMERLIB_TIMER_STOPPED;
    pst_Timer->st_Duration.u16_Ticks = CORE_TIMERLIB_TIMER_STOPPED;
}

void Core_TimerLib_TimerStart(st_CoreTimerLibTimer *const pst_Timer,
    const uint16_t u16_DurationSeconds,
    const uint16_t u16_DurationMilliSeconds)
{
    uint16_t u16_Temp = 0;
    pst_Timer->u16_StartTime = Core_TimeBase_GetSystemTickCounter();
    u16_Temp = u16_DurationMilliSeconds /
        CORE_TIMEBASE_NUM_MILLISECONDS_PER_SECOND;

    if((MAX_U16 - u16_DurationSeconds) >= u16_Temp)
    {
        pst_Timer->st_Duration.u16_Seconds = u16_DurationSeconds + u16_Temp;
    }
    else
    {
        pst_Timer->st_Duration.u16_Seconds = MAX_U16;
    }

    pst_Timer->st_Duration.u16_Ticks = (u16_DurationMilliSeconds %
                                           CORE_TIMEBASE_NUM_MILLISECONDS_PER_SECOND) *
        COREUSER_TIMEBASE_NUM_TICKS_PER_MILLISECOND;
}

void Core_TimerLib_TimerStartMeasurement(
    st_CoreTimerLibTimer *const pst_Timer)
{
    Core_TimerLib_TimerStart(pst_Timer, CORE_TIMERLIBRARY_MAX_DURATION_SECONDS, CORE_TIMERLIBRARY_MAX_DURATION_MILLISECONDS);
}

void Core_TimerLib_TimerStop(st_CoreTimerLibTimer *const pst_Timer)
{
    pst_Timer->st_Duration.u16_Seconds = CORE_TIMERLIB_TIMER_STOPPED;
    pst_Timer->st_Duration.u16_Ticks = CORE_TIMERLIB_TIMER_STOPPED;
}

bool Core_TimerLib_IsTimerRunning(
    const st_CoreTimerLibTimer *const pst_Timer)
{
    bool b_IsTimerRunning = false;

    if((CORE_TIMERLIB_TIMER_STOPPED != pst_Timer->st_Duration.u16_Ticks))
    {
        b_IsTimerRunning = true;
    }

    return (b_IsTimerRunning);
}

bool Core_TimerLib_IsTimerStopped(
    const st_CoreTimerLibTimer *const pst_Timer)
{
    bool b_IsTimerStopped = false;

    if((CORE_TIMERLIB_TIMER_STOPPED == pst_Timer->st_Duration.u16_Ticks))
    {
        b_IsTimerStopped = true;
    }

    return (b_IsTimerStopped);
}

bool Core_TimerLib_IsTimerExpired(st_CoreTimerLibTimer *const pst_Timer)
{
    bool b_IsTimerExpired = false;
    uint16_t u16_CurrentTickCounter = 0;
    uint16_t u16_TimeElapsedTicks = 0;

    if(true == Core_TimerLib_IsTimerStopped(pst_Timer))
    {
        b_IsTimerExpired = true;
    }
    else
    {
        u16_CurrentTickCounter = Core_TimeBase_GetSystemTickCounter();

        if(u16_CurrentTickCounter >= pst_Timer->u16_StartTime)
        {
            u16_TimeElapsedTicks = u16_CurrentTickCounter -
                pst_Timer->u16_StartTime;
        }
        else
        {
            u16_TimeElapsedTicks = MAX_U16 - pst_Timer->u16_StartTime +
                u16_CurrentTickCounter + 1;
        }

        pst_Timer->u16_StartTime = u16_CurrentTickCounter;

        if(u16_TimeElapsedTicks < pst_Timer->st_Duration.u16_Ticks)
        {
            pst_Timer->st_Duration.u16_Ticks =
                pst_Timer->st_Duration.u16_Ticks - u16_TimeElapsedTicks;
        }
        else
        {
            u16_TimeElapsedTicks = u16_TimeElapsedTicks -
                pst_Timer->st_Duration.u16_Ticks;
            pst_Timer->st_Duration.u16_Ticks = 0;

            while((pst_Timer->st_Duration.u16_Seconds > 0) &&
                (u16_TimeElapsedTicks > CORE_TIMEBASE_NUM_TICKS_PER_SECOND))
            {
                pst_Timer->st_Duration.u16_Seconds--;
                u16_TimeElapsedTicks = u16_TimeElapsedTicks -
                    CORE_TIMEBASE_NUM_TICKS_PER_SECOND;
            }

            if(0 == pst_Timer->st_Duration.u16_Seconds)
            {
                b_IsTimerExpired = true;
            }
            else
            {
                pst_Timer->st_Duration.u16_Seconds--;
                pst_Timer->st_Duration.u16_Ticks =
                    CORE_TIMEBASE_NUM_TICKS_PER_SECOND;
                pst_Timer->st_Duration.u16_Ticks =
                    pst_Timer->st_Duration.u16_Ticks - u16_TimeElapsedTicks;
            }
        }
    }

    return (b_IsTimerExpired);
}

void Core_TimerLib_GetElapsedTime(st_CoreTimerLibTimer *const pst_Timer,
    ST_Core_TimerLib_TimeUnit *const pst_DestinationTimeUnit)
{
    (void)Core_TimerLib_IsTimerExpired(pst_Timer);

    pst_DestinationTimeUnit->u16_Ticks =
        (CORE_TIMERLIBRARY_MAX_DURATION_MILLISECONDS *
            COREUSER_TIMEBASE_NUM_TICKS_PER_MILLISECOND) -
        pst_Timer->st_Duration.u16_Ticks;
    pst_DestinationTimeUnit->u16_Seconds = CORE_TIMERLIBRARY_MAX_DURATION_SECONDS -
        pst_Timer->st_Duration.u16_Seconds;
}

uint16_t Core_TimerLib_GetRemainingTime_Seconds(
    st_CoreTimerLibTimer *const pst_Timer)
{
    uint16_t u16_RemainingTime_Seconds = 0;

    if((st_CoreTimerLibTimer *)(NULL) == pst_Timer)
    {
        u16_RemainingTime_Seconds = 0;
    }
    else
    {
        (void)Core_TimerLib_IsTimerExpired(pst_Timer);

        u16_RemainingTime_Seconds = pst_Timer->st_Duration.u16_Seconds;
    }

    return (u16_RemainingTime_Seconds);
}
