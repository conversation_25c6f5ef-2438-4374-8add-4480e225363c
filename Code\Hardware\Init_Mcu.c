/*!
 * @file
 * @brief Initialize MCU.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "Adpt_Clock.h"
#include "Adpt_GPIO.h"
#include "Adpt_Timebase.h"
#include "Adpt_Iwdg.h"
#include "Adpt_PWM.h"
#include "Adpt_ADC.h"
#include "Adpt_Usart.h"
#include "Adpt_Reset.h"
#include "Adpt_Flash.h"

void Init_Mcu(void)
{
    Board_InitReset();
    Board_InitClock();
    Board_InitPins();
    Board_InitSysTick();
    Board_InitTim();
    Board_InitAdc();
    Board_InitUsart();
    Board_InitIwdg();
    Board_InitFlash();
}
