/*!
 * @file
 * @brief Simple finite state machine.
 * @warning A transition must be the last action taken when handling a signal.  The result of an
 * action taken after a transition is implementation-defined. *
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef SIMPLE_FSM_H
#define SIMPLE_FSM_H

#include <stdint.h>

enum
{
    SimpleFsmSignal_Entry,
    SimpleFsmSignal_Exit,
    SimpleFsmSignal_UserStart
};
typedef uint8_t SimpleFsmSignal_t;

struct SimpleFsm_t;

typedef void (*SimpleFsmState_t)(struct SimpleFsm_t *fsm, const SimpleFsmSignal_t signal, const void *data);

typedef struct SimpleFsm_t
{
    SimpleFsmState_t currentState;
    void *context;
} SimpleFsm_t;

/*!
 * Initialize an FSM. The initial state will receive the entry signal with no data.
 * @param instance
 * @param initialState
 */
void SimpleFsm_Init(SimpleFsm_t *instance, SimpleFsmState_t initialState, void *_context);

/*!
 * Send a signal to the current state.
 * @param instance
 * @param signal
 * @param data
 */
void SimpleFsm_SendSignal(SimpleFsm_t *instance, SimpleFsmSignal_t signal, const void *data);

/*!
 * Transition the FSM from the current state to the target state. The current state will receive
 * the exit signal with no data and the target state will receive the entry signal with no data.
 * The target state will become the new current state.
 * @param instance
 * @param targetState
 */
void SimpleFsm_Transition(SimpleFsm_t *instance, SimpleFsmState_t targetState);

#endif
