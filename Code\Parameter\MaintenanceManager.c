#include "MaintenanceManager.h"
#include "ParameterManager.h"
#include "Crc16_CCITT_FALSE.h"
#include "Driver_Flash.h"
#include "miio_api.h"
#include "syslog.h"

static uint16_t maintenance_sections_buffer[MAINTENANCE_SECTION_NUM][MAINTENANCE_SECTION_PARAMETERS];
static bool b_cmd_wifi;

maintenance_manager_st maintenance_manager;

static void MaintenanceSectionUploadResult(const char* pValue, char result)
{
    if(result > 0 && b_cmd_wifi == true)
    {
        maintenance_manager.section++;
        if(maintenance_manager.section == MAINTENANCE_SECTION_NUM)
        {
            maintenance_manager.uploaded = true;
            maintenance_manager.state = MAINTENANCE_MANAGER_STATE_WAIT;
            maintenance_manager.section = 0;
        }
        maintenance_manager.timercount = 0;
    }

    b_cmd_wifi = false;
}

static void MaintenanceManagerUpload(void)
{
    maintenance_manager.timercount++;
    if(b_cmd_wifi == false)
    {
        if(execute_wifi_cmd_async(WIFI_CMD_MAINTENANCE_UPLOAD, MaintenanceSectionUploadResult) == 0)
        {
            b_cmd_wifi = true;
        }
    }

    if(maintenance_manager.timercount > UPLOAD_TIMEOUT_100MS)
    {
        b_cmd_wifi = false;
        maintenance_manager.section = 0;
        maintenance_manager.timercount = 0;
        maintenance_manager.uploaded = false;
        maintenance_manager.state = MAINTENANCE_MANAGER_STATE_WAIT;
    }
}

static bool CheckCrcMaintenanceParam(void)
{
    uint8_t index;
    uint16_t data;
    uint8_t section;
    uint8_t paramcount;
    uint16_t crc = U16_CRC_INITIAL_VALUE;

    for(index = 0; index < MAINTENANCE_SECTION_NUM; index++)
    {
       paramcount = 0;
       if(index == 0)
       {
           paramcount = 1;
       }
    
       for( ; paramcount < MAINTENANCE_SECTION_PARAMETERS; paramcount++)
       {
           data = maintenance_sections_buffer[index][paramcount];
           crc = Cal_CRC_SingleData(crc, (data >> 8) & 0xFF);
           crc = Cal_CRC_SingleData(crc, (data & 0xFF));
       }
    }
    
    if(maintenance_sections_buffer[0][0] != crc)
    {
        return false;
    }

    crc = U16_CRC_INITIAL_VALUE;
    for(index = 0; index < MAINTENANCE_SECTION_NUM; index++)
    {
       for(paramcount = 0; paramcount < MAINTENANCE_SECTION_PARAMETERS; paramcount++)
       {
           data = maintenance_sections_buffer[index][paramcount];
           crc = Cal_CRC_SingleData(crc, (data >> 8) & 0xFF);
           crc = Cal_CRC_SingleData(crc, (data & 0xFF));
       }
    }
    if(crc != maintenance_manager.total_crc)
    {
        return false;
    }
    return true;
}


static void MaintenanceManagerDownload(void)
{
    uint8_t index;
    maintenance_manager.timercount++;

    if(maintenance_manager.mask == ((1 << MAINTENANCE_SECTION_NUM)- 1))
    {
        if(CheckCrcMaintenanceParam())
        {
            for(index = 0; index < MAINTENANCE_SECTION_NUM;index++)
            {
                SetMaintenanceParam(index + PARAMTYPE_MAINTENANCE_SECTION1, maintenance_sections_buffer[index]);
            }
        }
        b_cmd_wifi = false;
        maintenance_manager.section = 0;
        maintenance_manager.state = MAINTENANCE_MANAGER_STATE_UPLOAD;
        maintenance_manager.mask = 0;
        maintenance_manager.total_version = 0;
        maintenance_manager.total_crc = 0;
        maintenance_manager.uploaded = false;
    }

    if(maintenance_manager.timercount > DOWNLOAD_TIMEOUT_100MS)
    {
        maintenance_manager.timercount = 0;
        maintenance_manager.state = MAINTENANCE_MANAGER_STATE_WAIT;
        maintenance_manager.total_version = 0;
        maintenance_manager.total_crc = 0;
        maintenance_manager.mask = 0;
    }
}


static void MaintenanceManagerWait(void)
{
    maintenance_manager.timercount++;
    if(maintenance_manager.uploaded == false &&
       maintenance_manager.timercount > UPLOAD_RETRY_TIMEOUT_100MS)
    {
        maintenance_manager.timercount = 0;
        maintenance_manager.state = MAINTENANCE_MANAGER_STATE_UPLOAD;
    }
    return;
}


void MaintenanceManagerInit(void)
{
    memset(&maintenance_manager, 0, sizeof(maintenance_manager_st));
    maintenance_manager.statefuncs[MAINTENANCE_MANAGER_STATE_UPLOAD] = MaintenanceManagerUpload;
    maintenance_manager.statefuncs[MAINTENANCE_MANAGER_STATE_DOWNLOAD] = MaintenanceManagerDownload;
    maintenance_manager.statefuncs[MAINTENANCE_MANAGER_STATE_WAIT] = MaintenanceManagerWait;
}

void MaintenanceManagerRun(void)
{
    if(IsParameterManagerUploadReady() == false)
    {
        return;
    }

    if(maintenance_manager.state >= MAINTENANCE_MANAGER_STATE_UPLOAD
       && maintenance_manager.state < MAINTENANCE_MANAGER_STATE_MAX)
    {
        if(maintenance_manager.statefuncs[maintenance_manager.state] != NULL)
        {
            maintenance_manager.statefuncs[maintenance_manager.state]();
        }
    }
}

int GetMaintenanceParamVal(maintenance_parameter_e type, uint16_t *val)
{
    uint8_t offset;
    uint8_t section;

    section = type / MAINTENANCE_SECTION_PARAMETERS + PARAMTYPE_MAINTENANCE_SECTION1;
    offset = type % MAINTENANCE_SECTION_PARAMETERS;

    return GetMaintenanceSectionParam(section, offset, val);
}

int DownloadMaintenanceParam(uint8_t index, const char *paramstr)
{
    uint8_t count;
    uint8_t pos;
    uint16_t crc;
    uint16_t data;
    const char *buf;
    uint16_t pcrc = U16_CRC_INITIAL_VALUE;
    uint16_t params = 6 + MAINTENANCE_SECTION_PARAMETERS;

    if(IsParameterManagerUploadReady() == false)
    {
        return -1;
    }
    //“参数版本,总包参数校验,单包参数校验,总包序号,单包序号,参数长度,参数1...参数n”
    if(paramstr[0] != '\"')
    {
        return -1;
    }
    buf = paramstr + 1;
    while(count < params)
    {
        if(count == 0)
        {
            maintenance_manager.total_version = miio_atoi(buf);
        }

        if(count == 1)
        {
            maintenance_manager.total_crc = miio_atoi(buf);
        }

        if(count == 2)
        {
            crc = miio_atoi(buf);
        }

        if(count == 3)
        {
            if(miio_atoi(buf) != MAINTENANCE_SECTION_NUM)
            {
                return -1;
            }
        }

        if(count == 4)
        {
            if(miio_atoi(buf) != index + 1)
            {
                return -1;
            }
        }

        if(count == 5)
        {
            if(miio_atoi(buf) != MAINTENANCE_SECTION_PARAMETERS)
            {
                return -1;
            }
        }

        if(count > 5)
        {
            maintenance_sections_buffer[index][count - 6] = miio_atoi(buf) ;
        }

        count++;
        pos = miio_strtok(buf, ",");
        if(pos == 0 && count < params)
        {
            return -1;
        }
        buf += pos;
    }

    for(count = 0; count < MAINTENANCE_SECTION_PARAMETERS; count++)
    {
        data = maintenance_sections_buffer[index][count];
        pcrc = Cal_CRC_SingleData(pcrc, (data >> 8) & 0xFF);
        pcrc = Cal_CRC_SingleData(pcrc, (data & 0xFF));
    }
    if(crc != pcrc)
    {
        return -1;
    }
    maintenance_manager.state = MAINTENANCE_MANAGER_STATE_DOWNLOAD;
    maintenance_manager.timercount = 0;
    maintenance_manager.mask |= 1 << index;
    return 0;
}

int UploadMaintenanceParam(char *pResult)
{
    uint16_t data;
    uint8_t index;
    uint8_t section;
    uint16_t len = 0;
    uint16_t crc = U16_CRC_INITIAL_VALUE;

    //properties_changed 14 1 "0,0,单包参数校验,总包序号,单包序号,参数长度,参数1...参数n"
    section = maintenance_manager.section + PARAMTYPE_MAINTENANCE_SECTION1;
    GetMaintenanceParam(section, maintenance_sections_buffer[maintenance_manager.section]);
    for(index = 0; index < MAINTENANCE_SECTION_PARAMETERS; index++)
    {
        data = maintenance_sections_buffer[maintenance_manager.section][index];
        crc = Cal_CRC_SingleData(crc, (data >> 8) & 0xFF);
        crc = Cal_CRC_SingleData(crc, (data & 0xFF));
    }

    len += snprintf(pResult, CMD_RESULT_BUF_SIZE, "properties_changed 14 %d \"0,0,%u,%u,%u,%u", section, crc, MAINTENANCE_SECTION_NUM, section, MAINTENANCE_SECTION_PARAMETERS);
    for(index = 0; index < MAINTENANCE_SECTION_PARAMETERS; index++)
    {
        data = maintenance_sections_buffer[maintenance_manager.section][index];
        len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, ",%u", data);
    }
    snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "\"\r");
    return 0;
}

