#ifndef __FIREWARE_COMM_H__
#define __FIREWARE_COMM_H__

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include <stdio.h>

#define FIREWARE_SUCCESS (0)
#define FIREWARE_ERROR (-1)

#define FIREWARE_FRAME_HEAD ((uint8_t)0xA5)
#define FIREWARE_FRAME_END ((uint8_t)0x5A)
#define FIREWARE_FRAME_DATA_MAX ((uint8_t)128)
#define FIREWARE_FRAME_LEN_MAX ((uint8_t)138)

#define FIREWARE_FRAME_SUCCESS 0
#define FIREWARE_FRAME_TIMEOUT -1
#define FIREWARE_FRAME_SENDBUSY -2
#define FIREWARE_FRAME_SENDERROR -3
#define FIREWARE_FRAME_RESPONSE_ACK 1
#define FIREWARE_FRAME_RESPONSE_NACK 2

#define FIREWARE_FRAME_RETRY 5
#define FIREWARE_MAX_NUMS 8

#define CONTAINER_OF(container_type, field_name, pointer_to_field) \
    (container_type *)((void *)((char *)pointer_to_field - offsetof(container_type, field_name)))

#define GET_U16_HIGHBYTE(U16_TYPE) ((uint8_t)((U16_TYPE) >> 8))
#define GET_U16_LOWBYTE(U16_TYPE) ((uint8_t)((U16_TYPE)&0x00FF))

struct fireware_comm;

// 帧格式
typedef enum
{
    FIREWARE_FRAME_STATE_HEAD = 0, // 报文头
    FIREWARE_FRAME_STATE_SRC, // 报文源地址
    FIREWARE_FRAME_STATE_DEST, // 报文目的地址
    FIREWARE_FRAME_STATE_FUNCBYTE1, // 报文功能号1
    FIREWARE_FRAME_STATE_FUNCBYTE2, // 报文功能号2
    FIREWARE_FRAME_STATE_FUNCBYTE3, // 报文功能号3
    FIREWARE_FRAME_STATE_LENGTH, // 报文长度
    FIREWARE_FRAME_STATE_DATA, // 正常报文数据
    FIREWARE_FRAME_STATE_OVER, // 完成
    INVERTER_FRAME_STATE_MAX,
} fireware_state_em;

typedef struct
{
    uint8_t head;
    uint8_t srcAddr;
    uint8_t destAddr;
    uint8_t fb1;
    uint8_t fb2;
    uint8_t fb3;
    uint8_t len;
    uint8_t data[FIREWARE_FRAME_DATA_MAX];
    uint8_t crch;
    uint8_t crcl;
    uint8_t end;
} fireware_frame_st;

typedef enum
{
    FIREWARE_OTA_FUNC = 0xDD,
    FIREWARE_APP_FUNC = 0xAA,
} fireware_func_e;

typedef enum
{
    FIREWARE_OTA_QUERY_VER = 0x1,
    FIREWARE_OTA_RESET = 0x2,
    FIREWARE_OTA_JUMP_BOOT = 0x3,
    FIREWARE_OTA_LOAD_START = 0x4,
    FIREWARE_OTA_LOAD_END = 0x5,
    FIREWARE_OTA_CHECKSUM = 0x6,
    FIREWARE_OTA_JUMP_APP = 0x7,
    FIREWARE_OTA_LOAD_DATA = 0x8,
    FIREWARE_OTA_RUN_STATE = 0x9,
} fireware_ota_subfunc_e;

typedef enum
{
    FIREWARE_RUN_STATE_BOOT = 0x0,
    FIREWARE_RUN_STATE_APP = 0x1,
} fireware_run_state_e;

typedef enum
{
    FIREWARE_ADDR_NONE = 0x0,
    FIREWARE_MAIN_ADDR = 0x1,
    FIREWARE_DISPLAY_ADDR = 0x2,
    FIREWARE_PARTIALFRZ_ADDR = 0x3,
    FIREWARE_ICEMAKER_ADDR = 0x4,
    FIREWARE_INVERTER_ADDR = 0x5,
    FIREWARE_NFC_ADDR = 0x6,
} fireware_addr_e;

typedef struct
{
    uint16_t hwVersion;
    uint16_t appVersion;
    uint16_t appCrc;
} fireware_version_st;

typedef enum
{
    FIREWARE_RESPONSE_ACK = 1,
    FIREWARE_RESPONSE_NACK = 2,
    FIREWARE_RESPONSE_TIMEOUT = 3,
    FIREWARE_RESPONSE_ERROR = 4,
} fireware_response_e;

typedef struct
{
    int32_t (*transmit)(struct fireware_comm *fireware);
    int32_t (*receive)(struct fireware_comm *fireware);
} fireware_ops_s;

typedef struct fireware_comm
{
    fireware_frame_st sendPacket;
    fireware_frame_st recvPacket;
    fireware_ops_s ops;
    fireware_addr_e fwAddr;
    uint16_t sendCrcValue;
    uint8_t packno;
    uint8_t crcretry;
    uint8_t retry;
    bool enable;
    void *private;
} fireware_comm_st;

typedef struct
{
    fireware_comm_st *pfw[FIREWARE_MAX_NUMS];
    uint16_t fw_cnts;
} fireware_mananger_st;

static inline void FirewareEnable(fireware_comm_st *fw)
{
    fw->enable = true;
}

static inline void FirewareDisable(fireware_comm_st *fw)
{
    fw->enable = false;
}

void InitFirewareComm(void);
void FirewareCommRegister(fireware_comm_st *fw);
fireware_comm_st *FirewareGetByAddr(fireware_addr_e id);
void FirewareAllDisable(void);
int32_t FirewareAllToApp(void);
int32_t FirewareAllTryToApp(void);
int32_t FirewareAllInApp(void);
int32_t FirewareAllToBoot(void);
int32_t FirewareAllInBoot(void);
int32_t FirewareJumpToApp(fireware_comm_st *fw);
int32_t FirewareJumpToBoot(fireware_comm_st *fw);
int32_t FirewareCheckAppCrc(fireware_comm_st *fw);
int32_t FirewareStopOtaDownload(fireware_comm_st *fw);
int32_t FirewareStartOtaDownload(fireware_comm_st *fw);
int32_t FirewareRunState(fireware_comm_st *fw, fireware_run_state_e *state);
int32_t FirewareOtaDownload(fireware_comm_st *fw, uint8_t *buf, uint8_t len);
int32_t FirewareQueryAppVersion(fireware_comm_st *fw, fireware_version_st *version);

#endif
