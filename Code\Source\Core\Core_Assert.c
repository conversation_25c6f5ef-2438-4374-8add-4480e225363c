/*!
 * @file
 * @brief Assert implementation.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "Core_Assert.h"

/*!
 * @brief Nothing to do.
 */
void Assert_Init(void)
{
}

/*!
 * @brief Reset system.
 */
#if(ASSERT_TYPE == ASSERT_DISABLE)
#elif(ASSERT_TYPE == ASSERT_LOG_VC)
void OnAssert__(char const *file, unsigned line)
{
}
#else
void OnAssert__(void)
{
}
#endif
