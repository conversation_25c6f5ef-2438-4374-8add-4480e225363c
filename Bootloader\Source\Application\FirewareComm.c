/*!
 * @file
 * @brief This module covers the complete fan functionality.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include <string.h>
#include "FirewareComm.h"
#include "InverterUsart.h"
#include "Crc16_CCITT_FALSE.h"
#include "syslog.h"

static fireware_mananger_st fw_manager;

static void Edit_OtaControlFrame(fireware_comm_st *fw, fireware_ota_subfunc_e func)
{
    uint8_t *data = (uint8_t *)&fw->sendPacket.head;
    uint8_t index;
    uint8_t len;

    fw->sendPacket.head = FIREWARE_FRAME_HEAD;
    fw->sendPacket.srcAddr = FIREWARE_MAIN_ADDR;
    fw->sendPacket.destAddr = fw->fwAddr;
    fw->sendPacket.fb1 = FIREWARE_OTA_FUNC;
    fw->sendPacket.fb2 = func;
    fw->sendPacket.fb3 = 0x00;
    fw->sendPacket.len = 0x00;
    fw->sendPacket.end = FIREWARE_FRAME_END;
    fw->sendCrcValue = U16_CRC_INITIAL_VALUE;
    len = offsetof(fireware_frame_st, data) + fw->sendPacket.len;
    for(index = 0; index < len; index++)
    {
        fw->sendCrcValue =
            Cal_CRC_SingleData(fw->sendCrcValue, *data++);
    }

    fw->sendPacket.crch = GET_U16_HIGHBYTE(fw->sendCrcValue);
    fw->sendPacket.crcl = GET_U16_LOWBYTE(fw->sendCrcValue);
    return;
}

static void Edit_OtaDataFrame(fireware_comm_st *fw, uint8_t *buf, uint8_t size)
{
    uint8_t *data = &fw->sendPacket.head;
    uint8_t index;
    uint8_t len;

    fw->sendPacket.head = FIREWARE_FRAME_HEAD;
    fw->sendPacket.srcAddr = FIREWARE_MAIN_ADDR;
    fw->sendPacket.destAddr = fw->fwAddr;
    fw->sendPacket.fb1 = FIREWARE_OTA_FUNC;
    fw->sendPacket.fb2 = FIREWARE_OTA_LOAD_DATA;
    fw->sendPacket.fb3 = fw->packno;
    fw->sendPacket.len = size;
    fw->sendPacket.end = FIREWARE_FRAME_END;
    fw->sendCrcValue = U16_CRC_INITIAL_VALUE;
    memcpy(fw->sendPacket.data, buf, size);
    len = offsetof(fireware_frame_st, data) + fw->sendPacket.len;
    for(index = 0; index < len; index++)
    {
        fw->sendCrcValue =
            Cal_CRC_SingleData(fw->sendCrcValue, *data++);
    }

    fw->sendPacket.crch = GET_U16_HIGHBYTE(fw->sendCrcValue);
    fw->sendPacket.crcl = GET_U16_LOWBYTE(fw->sendCrcValue);
    return;
}

static int32_t Handle_FirewareRecvFrame(fireware_comm_st *fw, void *data)
{
    int32_t response;
    fireware_frame_st *rpacket;
    fireware_frame_st *spacket;
    fireware_run_state_e *state;
    fireware_version_st *version;

    rpacket = &fw->recvPacket;
    spacket = &fw->sendPacket;

    debug("fb1:%x-%x fb2:%x-%x fb3:%x-%x addr:%x-%x\n", spacket->fb1, rpacket->fb1, spacket->fb2, rpacket->fb2, spacket->fb3, rpacket->fb3, spacket->destAddr, spacket->srcAddr);

    if((spacket->fb1 == rpacket->fb1) &&
        (spacket->fb2 == rpacket->fb2) &&
        (spacket->destAddr == rpacket->srcAddr))
    {
        response = FIREWARE_RESPONSE_ACK;
        if(rpacket->fb1 == FIREWARE_OTA_FUNC)
        {
            if(rpacket->fb2 == FIREWARE_OTA_QUERY_VER && rpacket->len == 6 && data)
            {
                version = (fireware_version_st *)data;
                version->hwVersion = rpacket->data[0] << 8 | rpacket->data[1];
                version->appVersion = rpacket->data[2] << 8 | rpacket->data[3];
                version->appCrc = rpacket->data[4] << 8 | rpacket->data[5];
            }
            else if(rpacket->fb2 == FIREWARE_OTA_RUN_STATE && rpacket->len == 1 && data)
            {
                state = (fireware_run_state_e *)data;
                *state = rpacket->data[0];
                if(*state != FIREWARE_RUN_STATE_BOOT &&
                    *state != FIREWARE_RUN_STATE_APP)
                {
                    response = FIREWARE_RESPONSE_ERROR;
                }
            }
            else if(rpacket->len == 1)
            {
                response = rpacket->data[0];
            }
            else
            {
                response = FIREWARE_RESPONSE_ERROR;
            }
        }
    }
    else
    {
        response = FIREWARE_RESPONSE_ERROR;
    }
    return response;
}

static int32_t FirewareTransferSync(fireware_comm_st *fw, void *data)
{
    uint8_t cnt = FIREWARE_FRAME_RETRY;

    if(fw->retry != 0)
    {
        cnt = fw->retry;
    }

    while(cnt--)
    {
        if(fw->ops.transmit(fw) < 0)
        {
            err("FirewareTransferSync transmit failed\n");
            continue;
        }
        if(fw->ops.receive(fw) < 0)
        {
            err("FirewareTransferSync receive failed\n");
            continue;
        }
        if(Handle_FirewareRecvFrame(fw, data) == FIREWARE_RESPONSE_ACK)
        {
            fw->retry = 0;
            return FIREWARE_SUCCESS;
        }
        else if(fw->sendPacket.fb2 == FIREWARE_OTA_CHECKSUM)
        {
            fw->retry = 0;
            return FIREWARE_ERROR;
        }
    }
    fw->retry = 0;
    return FIREWARE_ERROR;
}

fireware_comm_st *FirewareGetByAddr(fireware_addr_e id)
{
    int32_t index;

    for(index = 0; index < fw_manager.fw_cnts; index++)
    {
        if(fw_manager.pfw[index]->fwAddr == id)
        {
            return fw_manager.pfw[index];
        }
    }
    return NULL;
}

int32_t FirewareJumpToBoot(fireware_comm_st *fw)
{
    if(NULL == fw)
    {
        return FIREWARE_ERROR;
    }

    Edit_OtaControlFrame(fw, FIREWARE_OTA_JUMP_BOOT);
    fw->retry = 1;
    return FirewareTransferSync(fw, NULL);
}

int32_t FirewareAllToBoot(void)
{
    int32_t index;
    int32_t ret = 0;

    for(index = 0; index < fw_manager.fw_cnts; index++)
    {
        ret |= FirewareJumpToBoot(fw_manager.pfw[index]);
    }
    return ret;
}

int32_t FirewareQueryAppVersion(fireware_comm_st *fw, fireware_version_st *version)
{
    if(NULL == fw)
    {
        return FIREWARE_ERROR;
    }

    Edit_OtaControlFrame(fw, FIREWARE_OTA_QUERY_VER);
    return FirewareTransferSync(fw, version);
}

int32_t FirewareStartOtaDownload(fireware_comm_st *fw)
{
    if(NULL == fw)
    {
        return FIREWARE_ERROR;
    }

    fw->packno = 0;
    Edit_OtaControlFrame(fw, FIREWARE_OTA_LOAD_START);
    return FirewareTransferSync(fw, NULL);
}

int32_t FirewareStopOtaDownload(fireware_comm_st *fw)
{
    if(NULL == fw)
    {
        return FIREWARE_ERROR;
    }

    Edit_OtaControlFrame(fw, FIREWARE_OTA_LOAD_END);
    return FirewareTransferSync(fw, NULL);
}

int32_t FirewareCheckAppCrc(fireware_comm_st *fw)
{
    if(NULL == fw)
    {
        return FIREWARE_ERROR;
    }

    Edit_OtaControlFrame(fw, FIREWARE_OTA_CHECKSUM);
    fw->retry = fw->crcretry;
    return FirewareTransferSync(fw, NULL);
}

int32_t FirewareJumpToApp(fireware_comm_st *fw)
{
    if(NULL == fw)
    {
        return FIREWARE_ERROR;
    }

    Edit_OtaControlFrame(fw, FIREWARE_OTA_JUMP_APP);
    return FirewareTransferSync(fw, NULL);
}

int32_t FirewareAllToApp(void)
{
    int32_t index;
    int32_t ret = 0;

    for(index = 0; index < fw_manager.fw_cnts; index++)
    {
        if(!fw_manager.pfw[index]->enable)
        {
            continue;
        }

        ret |= FirewareJumpToApp(fw_manager.pfw[index]);
    }
    return ret;
}

int32_t FirewareAllTryToApp(void)
{
    int32_t index;
    int32_t ret = 0;

    for(index = 0; index < fw_manager.fw_cnts; index++)
    {
        ret |= FirewareJumpToApp(fw_manager.pfw[index]);
    }
    return ret;
}


int32_t FirewareOtaDownload(fireware_comm_st *fw, uint8_t *buf, uint8_t len)
{
    int32_t ret;

    if(NULL == fw)
    {
        return FIREWARE_ERROR;
    }

    Edit_OtaDataFrame(fw, buf, len);
    ret = FirewareTransferSync(fw, NULL);
    if(ret == FIREWARE_SUCCESS)
    {
        fw->packno++;
    }
    return ret;
}

int32_t FirewareRunState(fireware_comm_st *fw, fireware_run_state_e *state)
{
    if(NULL == fw)
    {
        return FIREWARE_ERROR;
    }

    Edit_OtaControlFrame(fw, FIREWARE_OTA_RUN_STATE);
    return FirewareTransferSync(fw, state);
}

int32_t FirewareAllInApp(void)
{
    fireware_run_state_e state;
    int32_t index;
    int32_t ret;
    int32_t result = FIREWARE_SUCCESS;

    for(index = 0; index < fw_manager.fw_cnts; index++)
    {
        if(!fw_manager.pfw[index]->enable)
        {
            continue;
        }

        ret = FirewareRunState(fw_manager.pfw[index], &state);
        if(ret < 0 || state != FIREWARE_RUN_STATE_APP)
        {
            result = FIREWARE_ERROR;
        }
        else
        {
            FirewareDisable(fw_manager.pfw[index]);
        }
    }

    return result;
}

int32_t FirewareAllInBoot(void)
{
    fireware_run_state_e state;
    int32_t index;
    int32_t ret;

    for(index = 0; index < fw_manager.fw_cnts; index++)
    {
        ret = FirewareRunState(fw_manager.pfw[index], &state);
        info("fireware(%d) state is %d\n", fw_manager.pfw[index]->fwAddr, state);
        if(ret < 0 || state != FIREWARE_RUN_STATE_BOOT)
        {
            return FIREWARE_ERROR;
        }
    }

    return FIREWARE_SUCCESS;
}

void FirewareAllDisable(void)
{
    int32_t index;

    for(index = 0; index < fw_manager.fw_cnts; index++)
    {
        FirewareDisable(fw_manager.pfw[index]);
    }
}

void FirewareCommRegister(fireware_comm_st *fw)
{
    if(NULL == fw)
    {
        err("FirewareCommRegister input is NULL\n");
        return;
    }

    if(fw_manager.fw_cnts >= FIREWARE_MAX_NUMS)
    {
        err("fireware count (%d) is out of range\n", fw_manager.fw_cnts);
        return;
    }

    if(FirewareGetByAddr(fw->fwAddr))
    {
        err("fireware id(%d) is already registered\n", fw->fwAddr);
        return;
    }

    fw_manager.pfw[fw_manager.fw_cnts++] = fw;
    return;
}
