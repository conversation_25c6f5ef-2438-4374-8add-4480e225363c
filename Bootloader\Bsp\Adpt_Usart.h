/*!
 * @file
 * @brief This file defines public constants, types and functions for the usart.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef _Adpt_USART_H_
#define _Adpt_USART_H_

#include <stdint.h>

#define UART_QUEUE_SIZE 256

enum 
{
    UART_CH_WIFI = 0,
    UART_CH_TESTUART,
    UART_CH_MAX
};

struct uart_channel;

typedef struct
{
   int (*send)(struct uart_channel *ch);
   int (*enablerxirq)(struct uart_channel *ch, uint8_t en);
   int (*enabletxirq)(struct uart_channel *ch, uint8_t en);
}uart_channel_ops;

typedef struct uart_channel
{
    uint8_t  tx_queue[UART_QUEUE_SIZE];
    uint16_t tx_front;
    uint16_t tx_rear;
    uint8_t  rx_queue[UART_QUEUE_SIZE];
    uint16_t rx_front;
    uint16_t rx_rear;
    uint8_t tx_done;
    uart_channel_ops uops;
}uart_channel_st;


static inline uint8_t uart_rx_queue_is_empty(uart_channel_st *uch)
{
    return uch->rx_front == uch->rx_rear;
}

static inline uint8_t uart_rx_queue_is_full(uart_channel_st *uch)
{
    return ( (uch->rx_rear + 1 ) % UART_QUEUE_SIZE == uch->rx_front);
}

static inline  uint8_t uart_tx_queue_is_empty(uart_channel_st *uch)
{
    return uch->tx_front == uch->tx_rear;
}

 static inline uint8_t uart_tx_queue_is_full(uart_channel_st *uch)
 {
     return ( (uch->tx_rear + 1 ) % UART_QUEUE_SIZE == uch->tx_front);
 }


void Board_InitUsart(void);
void Board_DeinitUsart(void);
void Board_InitTestUsart(void);
void LpUart0_SendOneData(uint8_t u8_data);
void LpUart0_EnalbeTxInterrupts(void);
void LpUart0_DisalbeTxInterrupts(void);
void LpUart0_EnalbeRxInterrupts(void);
void LpUart0_DisalbeRxInterrupts(void);
void LpUart1_SendOneData(uint8_t u8_data);
void LpUart1_EnalbeTxInterrupts(void);
void LpUart1_DisalbeTxInterrupts(void);
void Uart3_SendOneData(uint8_t u8_data);
void Uart3_EnalbeTxInterrupts(void);
void Uart3_DisalbeTxInterrupts(void);
void Uart3_EnalbeRxInterrupts(void);
void Uart3_DisalbeRxInterrupts(void);
void InverterUart_SendOneData(uint8_t u8_data);
void InverterUart_EnalbeTxInterrupts(void);
void InverterUart_DisalbeTxInterrupts(void);
void InverterUart_EnalbeRxInterrupts(void);
void InverterUart_DisalbeRxInterrupts(void);
uint8_t uart_send_data(uint8_t ch, uint8_t *send_buffer, uint8_t length);
uint8_t uart_receive_data(uint8_t ch, uint8_t *recv_buf, uint8_t recv_len);
uint8_t uart_recv_buffer_is_empty(uint8_t ch);
int uart_send_byte(uint8_t ch, unsigned char u8data);
int uart_recv_byte(uint8_t ch, unsigned char *p_u8data);
#endif
