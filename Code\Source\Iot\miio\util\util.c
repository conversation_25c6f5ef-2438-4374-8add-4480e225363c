#include "miio_define.h"
#include "util.h"
#include "String.h"
////void str_n_cat(char __far *pDst, int n_str, ...)
//{
//    const char __far *p = NULL;
//    va_list list;
//    va_start(list, n_str);


//    while (n_str--) {
//        p = va_arg(list, const __far char *);
//        _COM_strncat_ff(pDst, p, strlen(p));
//    }

//    va_end(list);
//}

void str_n_cat(char *pDst, int n_str, ...)
{
    const char *p = NULL;
    va_list list;
    va_start(list, n_str);


    while (n_str--) {
        p = va_arg(list, const char *);
        strncat(pDst, p, strlen(p));
    }

    va_end(list);
}
