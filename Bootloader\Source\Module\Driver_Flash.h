/*!
 * @file
 * @brief This file defines public constants, types and functions for the fan drive module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __DRIVER_FLASH_H
#define __DRIVER_FLASH_H

#include <stdint.h>
#include <stdbool.h>
#include <stdlib.h>
#include "syslog.h"

#define APP_HEADER_SIZE             16
#define APP_MAGIC_NUM               0x89765432
#define OTA_MAGIC                   ((uint32_t)0x89765432)
#define OTA_START_FLAG              ((uint32_t)0x55555555)
#define OTA_TESTUART_FLAG           ((uint32_t)0x5aa5a55a)
#define OTA_DONE_FLAG               ((uint32_t)0xaaaaaaaa)
#define MCU_FACTORY_VERSION         (1)
#define MCU_MAX_VERSION             (9999)
#define APP_PROMOTE_TIMES           (32)
#define FLASH_ERASE_STATE           (0xFF)
#if FLASH_ERASE_STATE == 0xFF
#define OTA_QUITE_FLAG              ((uint32_t)0xFFFFFFFF)
#define OTA_NORMAL_FLAG             ((uint32_t)0x0)
#else
#define OTA_QUITE_FLAG              ((uint32_t)0x0)
#define OTA_NORMAL_FLAG             ((uint32_t)0xFFFFFFFF)
#endif

/* BootLoader flash相关宏定义 */
#define BOOT_SIZE (48 * FLASH_SECTOR_SIZE) // BootLoader flash尺寸
#define BOOT_PARA_ADDRESS (FLASH_BASE + BOOT_SIZE - 2 * FLASH_SECTOR_SIZE) // BootLoader para存储地址

/* APP flash相关宏定义 */
#define APP_ADDRESS (FLASH_BASE + BOOT_SIZE) // APP程序存储基地址
#define APP_SIZE (391 * FLASH_SECTOR_SIZE) // APP flash尺寸

#define PARAM_ADDRESS (APP_ADDRESS + APP_SIZE) // 配置存储基地址
#define PARAM_SIZE (8 * FLASH_SECTOR_SIZE) // 配置存储空间大小

#define FACTORY_ADDRESS (PARAM_ADDRESS + PARAM_SIZE) // 工厂SN存储基地址
#define FACTORY_SIZE (1 * FLASH_SECTOR_SIZE) // 工厂SN空间大小

#define EEPARAM_ADDRESS (FACTORY_ADDRESS + FACTORY_SIZE)
#define EEPARAM_SIZE (16 * FLASH_SECTOR_SIZE)

#define MAINTENANCE_SECTION_ADDRESS (EEPARAM_ADDRESS + EEPARAM_SIZE)
#define MAINTENANCE_SECTION_SIZE (2 * FLASH_SECTOR_SIZE)
#define MAINTENANCE_SECTION_NUM 8

#define PANIC_LOG_ADDRESS (MAINTENANCE_SECTION_ADDRESS + MAINTENANCE_SECTION_SIZE * MAINTENANCE_SECTION_NUM)
#define PANIC_LOG_SIZE (16 * FLASH_SECTOR_SIZE)

#define OTA_LOG_ADDRESS      (PANIC_LOG_ADDRESS + PANIC_LOG_SIZE)
#define OTA_LOG_SIZE         (16 * FLASH_SECTOR_SIZE)

#define APP_PROMOTE_ADDR      (0x20007FE0)

typedef void (*func_ptr_t)(void);

typedef enum
{
    OTA_IMAGE_APP = 0,
    OTA_IMAGE_MAX
}ota_imageid_e;

typedef struct
{
    uint32_t offset;
    uint32_t bytes;
    uint32_t size;
    uint8_t skip;
} ota_imagedld_st;

#pragma pack(1)
typedef struct
{
    uint32_t magic;
    uint32_t version;
    uint32_t length;
    uint16_t crc16;
    uint8_t pad[2];
}app_imageheader_st;
#pragma pack()

typedef struct
{
    uint32_t ota_version;
    uint32_t ota_flag;
    uint32_t quite_flag;
    uint32_t ota_magic;
} ota_param_st;

typedef struct
{
    uint32_t magic;
    uint32_t count;
    uint16_t crc16;
    uint32_t boot_version;
    uint32_t boot_crc;
    uint32_t app_version;
    uint32_t app_crc;
}app_promote_st;

void Init_Flash(void);
void SetOtaDone(uint32_t version);
void ClearOtaParam(void);
void JumpToApp(void);;
void BoardReset(void);
void ImageDownloadRest(void);
void RevertOtaFlag(void);
bool IsOtaFlag(void);
bool IsTestUartOtaFlag(void);
uint32_t GetMcuVersion(void);
uint16_t convert_from_bigendian16(uint16_t val);
uint32_t convert_from_bigendian32(uint32_t val);
int32_t CheckImageCrc(ota_imageid_e imageid);
uint32_t GetAppPromoteCount(void);
void ClearAppPromoteCount(void);
void IncreaseAppPromoteCount(void);
int32_t ImageDownload(ota_imageid_e imageid, uint8_t *buf, uint32_t len);
int32_t CheckImageVersionCrc(ota_imageid_e imageid, uint32_t ota_version, uint32_t ota_crc);
void SaveOfflineLog(void *buf, uint32_t len);
uint32_t CalcBootCrc(void);
uint32_t GetAppVersion(void);
#endif
