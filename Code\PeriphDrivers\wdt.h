/**
 ******************************************************************************
 * @file   wdt.h
 *
 * @brief This file contains all the functions prototypes of the WDT driver
 *        library.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-03-15       MADS            First version
 @endverbatim
 ******************************************************************************
 * Copyright (C) 2023, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 ******************************************************************************
 */

#ifndef __WDT_H__
#define __WDT_H__

/* C binding of definitions if building with C++ compiler */
#ifdef __cplusplus
extern "C" {
#endif

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "ddl.h"

/**
 * @addtogroup HC32L186_DDL 驱动库
 * @{
 */

/**
 * @addtogroup DDL_WDT WDT模块驱动库
 * @{
 */

/*******************************************************************************
 * Global type definitions ('typedef')
 ******************************************************************************/
/**
 * @defgroup _Global_Types WDT全局类型定义
 * @{
 */

/**
 * @brief  wdt溢出后复位或中断配置
 */
typedef enum
{
    WdtResetEn    = 0,   /*!< 复位使能 */
    WdtIntEn      = 1,   /*!< 中断使能 */
} en_wdt_func_t;

/**
 * @brief  当前运行时间配置数据类型定义
 */
typedef enum
{
    WdtT1ms6   = 0u,       /*!< 1.6ms */
    WdtT3ms2   = 1u,       /*!< 3.2ms */
    WdtT6ms4   = 2u,       /*!< 6.4ms */
    WdtT13ms   = 3u,       /*!< 13ms */
    WdtT26ms   = 4u,       /*!< 26ms */
    WdtT51ms   = 5u,       /*!< 51ms */
    WdtT102ms  = 6u,       /*!< 102ms */
    WdtT205ms  = 7u,       /*!< 205ms */
    WdtT500ms  = 8u,       /*!< 500ms */
    WdtT820ms  = 9u,       /*!< 820ms */
    WdtT1s64   = 10u,      /*!< 1.64s */
    WdtT3s28   = 11u,      /*!< 3.28s */
    WdtT6s55   = 12u,      /*!< 6.55s */
    WdtT13s1   = 13u,      /*!< 13.1s */
    WdtT26s2   = 14u,      /*!< 26.2s */
    WdtT52s4   = 15u,      /*!< 52.4s */
} en_wdt_time_t;

/**
 * @}
 */

/*******************************************************************************
 * Global pre-processor symbols/macros ('#define')
 ******************************************************************************/

/*******************************************************************************
 * Global variable definitions ('extern')
 ******************************************************************************/

/*******************************************************************************
  Global function prototypes (definition in C source)
 ******************************************************************************/
/**
 * @addtogroup WDT_Global_Functions WDT全局函数定义
 * @{
 */

/* wdt初始化 */
en_result_t Wdt_Init(en_wdt_func_t enFunc, en_wdt_time_t enTime);

/* 启动函数 */
void Wdt_Start(void);
/* 喂狗处理 */
void Wdt_Feed(void);
/* 中断标志清除 */
void Wdt_IrqClr(void);
/* wdt溢出时间设置 */
void Wdt_WriteWdtLoad(uint8_t u8LoadValue);
/* 读取当前计数值 */
uint8_t Wdt_ReadWdtValue(void);
/* 中断状态获取 */
boolean_t Wdt_GetIrqStatus(void);
/* 运行状态获取 */
boolean_t Wdt_ReadwdtStatus(void);

/**
 * @}
 */


/**
 * @}
 */

/**
 * @}
 */

#ifdef __cplusplus
}
#endif

#endif /* __WDT_H__ */
/******************************************************************************
 * EOF (not truncated)
 *****************************************************************************/
