/*!
 * @file
 * @brief This file defines public constants, types and functions for the fan drive module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __SBUS_DISPLAY_H
#define __SBUS_DISPLAY_H

#include "Sbus_Core.h"

#define DISPLAY_EVENT_POLL_TIMER_MS 200
#define DISPLAY_ERROR_EVENT_POLL_TIMER_MS 2000
#define DISPLAY_WIFI_REQUEST_TIMEOUT_500MS 6
#define DISPLAY_BOOT_MS 2000
#define DISPLAY_MTYPE_SYNC_MINUTES 30

#define FORCE_SKIP_INSPECTION (1 << 0)
#define FORCE_VALVE_OPEN (1 << 1)
#define FORCE_STATE_VERTICAL_HEATER (1 << 2)
#define FORCE_STATE_WATER_ALARM (1 << 3)
#define FORCE_ION_GENERATOR (1 << 4)
#define FORCE_CUSTOMER_INSTALL (1 << 5)
#define ION_FORCE_ON_MIN (24 * 60)

typedef struct
{
    uint32_t version;
    uint32_t display_error;
    uint8_t mode;
    uint8_t door_alarm;
    uint32_t error;
    uint32_t pfault;
    uint8_t buzzer;
    int8_t ref_temp;
    int8_t ref_set_temp;
    uint8_t ref_disable;
    uint8_t ref_turbocool;
    uint8_t frz_deepfreeze;
    int8_t frz_temp;
    int8_t frz_set_temp;
    int8_t turbo_frz_temp;
    int8_t var_temp;
    int8_t vbottom_temp;
    int8_t vtop_temp;
    int8_t vbottomx_temp;
    uint8_t infant_mode;
    uint8_t deforst_state;
    int8_t  deforst_sensor;
    uint8_t refdamper_state;
    uint8_t frzfan_state;
    uint8_t reffan_state;
    uint16_t comp_state;
    uint8_t coolfan_state;
    uint8_t vardamper_state;
    int8_t room_temp;
    int8_t refdeforst_temp;
    uint8_t valve_state;
    uint8_t humidity;
    uint8_t recovery;
    uint8_t icemaker_mode;
    uint8_t icemaker_state;
    uint8_t icemaker_test;
    uint32_t icemaker_reserve;
    uint8_t icemaker_reserve_sw;
    uint8_t icemaker_volume;
    uint8_t comperror_state;
    uint16_t door_state;
    uint8_t cooling_state;
    uint8_t system_state;
    uint8_t hearter_state;
    uint8_t wifi_state;
    uint8_t wifi_restore;
    uint8_t wifi_factory;
    uint8_t wifi_match;
    uint8_t force_state;
    uint8_t test_state;
    uint8_t inspection;
    uint8_t mtype;
    uint8_t ref_ionstate;
    uint8_t frz_ionstate;
    uint8_t food_alarm;
    uint8_t food_unalarm;
    uint16_t food_alarm_room;
    int16_t food_alarm_days;
    uint8_t sn_state;
    uint8_t init;
} display_param_st;

typedef enum
{
    DISPLAY_PROPERTY_TYPE_MACHINE = 0,
    DISPLAY_PROPERTY_TYPE_FAULT,
    DISPLAY_PROPERTY_TYPE_MODE,
    DISPLAY_PROPERTY_TYPE_REF_TURBOCOOL,
    DISPLAY_PROPERTY_TYPE_FRZ_DEEPFREEZE,
    DISPLAY_PROPERTY_TYPE_DOOR_ALARM,
    DISPLAY_PROPERTY_TYPE_REF_TEMP,
    DISPLAY_PROPERTY_TYPE_REF_SET,
    DISPLAY_PROPERTY_TYPE_REF_DISABLE,
    DISPLAY_PROPERTY_TYPE_FRZ_TEMP,
    DISPLAY_PROPERTY_TYPE_FRZ_SET,
    DISPLAY_PROPERTY_TYPE_VAR_TEMP,
    DISPLAY_PROPERTY_TYPE_INFANT_MODE,
    DISPLAY_PROPERTY_TYPE_VAR_BOTTOM,
    DISPLAY_PROPERTY_TYPE_VAR_TOP,
    DISPLAY_PROPERTY_TYPE_VAR_BOTTOMX,
    DISPLAY_PROPERTY_TYPE_ICEMAKER_TEST,
    DISPLAY_PROPERTY_TYPE_ICEMAKER_MODE,
    DISPLAY_PROPERTY_TYPE_ICEMAKER_STATE,
    DISPLAY_PROPERTY_TYPE_ICEMAKER_RESERVE,
    DISPLAY_PROPERTY_TYPE_ICEMAKER_VOLUME,
    DISPLAY_PROPERTY_TYPE_ICEMAKER_RESERVE_SW,
    DISPLAY_PROPERTY_TYPE_DEFORST_STATE,
    DISPLAY_PROPERTY_TYPE_DEFORST_SENSOR,
    DISPLAY_PROPERTY_TYPE_REFDAMPER,
    DISPLAY_PROPERTY_TYPE_FRZFAN,
    DISPLAY_PROPERTY_TYPE_COMPSTATE,
    DISPLAY_PROPERTY_TYPE_COOLFAN,
    DISPLAY_PROPERTY_TYPE_VARDAMPER,
    DISPLAY_PROPERTY_TYPE_ROOMTEMP,
    DISPLAY_PROPERTY_TYPE_HUMIDITY,
    DISPLAY_PROPERTY_TYPE_DOOR_STATE,
    DISPLAY_PROPERTY_TYPE_COMP_STATE,
    DISPLAY_PROPERTY_TYPE_SYSTEM_STATE,
    DISPLAY_PROPERTY_TYPE_COOLING_STATE,
    DISPLAY_PROPERTY_TYPE_REFFAN,
    DISPLAY_PROPERTY_TYPE_HEARTER_STATE,
    DISPLAY_PROPERTY_TYPE_PASSIVE_FAULT,
    DISPLAY_PROPERTY_TYPE_VALVE_STATE,
    DISPLAY_PROPERTY_TYPE_REFDEFORST_TEMP,
    DISPLAY_PROPERTY_TYPE_WIFI_STATE,
    DISPLAY_PROPERTY_TYPE_WIFI_RESTORE,
    DISPLAY_PROPERTY_TYPE_WIFI_FACTORY,
    DISPLAY_PROPERTY_TYPE_WIFI_MATCH,
    DISPLAY_PROPERTY_TYPE_DISPLAY_FORCE,
    DISPLAY_PROPERTY_TYPE_TEST_MODE,
    DISPLAY_PROPERTY_TYPE_INSPECTION,
    DISPLAY_PROPERTY_TYPE_RECOVERY,
    DISPLAY_PROPERTY_TYPE_REFION_STATE,
    DISPLAY_PROPERTY_TYPE_FRZION_STATE,
    DISPLAY_PROPERTY_TYPE_FOOD_ALARM,
    DISPLAY_PROPERTY_TYPE_FOOD_UNALARM,
    DISPLAY_PROPERTY_TYPE_FOOD_ALARM_ROOM,
    DISPLAY_PROPERTY_TYPE_FOOD_ALARM_DAYS,
    DISPLAY_PROPERTY_TYPE_SN_STATE,
    DISPLAY_PROPERTY_TYPE_INIT,
    DISPLAY_PROPERTY_TYPE_MAX
} display_property_type_e;

typedef struct
{
    sbus_slave_st slave;
    fireware_frame_st frame;
    uint16_t poll_count;
    uint16_t boot_count;
    uint16_t hwversion;
    uint16_t appversion;
    uint16_t appcrc;
    uint16_t bootVersion;
    uint16_t bootCrc;
    bool b_appversion;
    bool b_bootversion;
    bool dirty;
    bool response;
    bool init;
    bool alldirty;
    bool b_reboot_wifi;
    uint8_t wifi_timer;
    bool ion_on;
    uint16_t ion_timer;
    uint16_t mtype_timer;
} sbus_display_st;

enum
{
    eNull_Tone,
    eShort_Tone,
    eLong_Tone,
    eDoorAlarm_Tone,
    eDoorOpen_Tone,
    eInvalid_Tone,
    eMax_Tone
};
typedef uint8_t MusicType_t;

typedef enum
{
    TMODE_TT,
    TMODE_FORCE_COOLING,    // 强制制冷
    TMODE_FORCE_FRZCOOLING, // 强制冷冻制冷
    TMODE_FORCE_REFCOOLING, // 强制冷藏制冷
    TMODE_FORCE_DEFROST, // 强制化霜
    TMODE_FORCE_ENERGY, // 强制能效
} Tmode_em;


void Init_SbusDisplay(void);
void Handle_Display_Overtime(void);
void Update_Display_Param(void);
bool Get_WifiConnectState(void);
void Set_MusicType(MusicType_t type);
bool Get_DisplayCommErr(void);
int8_t GetDisplayPropertyValue(display_property_type_e type, void *data);
int8_t SetDisplayPropertyValue(display_property_type_e type, void *data);
void Handle_Display_Request();
uint8_t Get_TestMode(void);
int8_t GetDisplaySnPropertyValue(uint8_t *sn, uint8_t size);
bool GetDisplayPropertyState(display_property_type_e type);
uint32_t Get_DisplayBootVersion(void);
uint32_t Get_DisplayBootCrc(void);
uint32_t Get_DisplayAppVersion(void);
uint32_t Get_DisplayAppCrc(void);
sbus_slave_stats *Get_DisplayPacketStats(void);
#endif
