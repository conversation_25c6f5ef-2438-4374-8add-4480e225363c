/*!
 * @file
 * @brief Manages all the state variables of the zone parameter.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "Vartemp_State.h"
#include "ResolverDevice.h"
#include "FridgeRunner.h"
#include "Sbus_IceMaker.h"
#include "Driver_Emulator.h"

#define CYCLE_POLL_SECONDS (uint16_t)1

static st_CoreCallbackTimer st_CycleTimer;
static uint16_t Var_On = 500;
static uint16_t Var_Off = 490;
static int16_t offset = 0;
static uint16_t u16_Overload_TimeStart;
static uint16_t u16_Overload_Minute;
static uint16_t u16_Overload_Adjust = U16_OVERLOAD_FRESHMEAT_ADJSUT_TEMP;
static uint16_t u16_Overload_Temp = U16_OVERLOAD_FRESHMEAT_TEMP;
static uint16_t u16_VarRightLedOffStart;
static bool b_VarRightLedState;
static uint16_t u16_VarRightLedOnTime;
static uint16_t u16_Thaw_TimeStart;
static uint16_t u16_Thaw_Minute;
static bool Overload_protect_flag;
static bool Thaw_protect_flag;
static bool Thaw_control_flag;
static uint16_t u16_StageState1TimerCounter;
static uint16_t u16_StageState2TimerCounter;
static uint16_t u16_StageState3TimerCounter;
static SimpleFsm_t st_Fsm;
static VarControlStage_t VarControlStage;
static const int16_t ary_RefVarOffset[(RefVarSet_t)eRefVar_Max - REFVAR_LEVEL_OFFSET] = {
    5,
    5,
    5,
    0,
    0
};

static const ZoneOnOffTemp_st ary_RefVarOnOffTemp[][(RefVarSet_t)eRefVar_Max - REFVAR_LEVEL_OFFSET] = {
    //  on temp   off temp
    {
        // RT <= 13
        { 500 - 20, 500 - 25 }, // Fresh meat -1.5 degree
        { 500 - 05, 500 - 10 }, // Aquatic product -0.5 degree
        { 500 - 20, 500 - 25 }, // Thaw -1.5 degree
        { 500 - 45, 500 - 50 }, // Smoothie
        { 500 + 40, 500 + 30 }, // Ref
    },
    //  on temp   off temp
    {
        // 13 < RT <= 18
        { 500 - 20, 500 - 25 }, // Fresh meat -1.5 degree
        { 500 - 10, 500 - 15 }, // Aquatic product -0.5 degree
        { 500 - 20, 500 - 25 }, // Thaw -1.5 degree
        { 500 - 45, 500 - 50 }, // Smoothie
        { 500 + 40, 500 + 30 }, // Ref
    },
    //  on temp   off temp
    {
        // 18 < RT <= 23
        { 500 - 20, 500 - 25 }, // Fresh meat -1.5 degree
        { 500 - 10, 500 - 15 }, // Aquatic product -0.5 degree
        { 500 - 20, 500 - 25 }, // Thaw -1.5 degree
        { 500 - 45, 500 - 50 }, // Smoothie
        { 500 + 40, 500 + 30 }, // Ref
    },
    // on temp   off temp
    {
        // 23 < RT <= 28
        { 500 - 20, 500 - 25 }, // Fresh meat -1.5 degree
        { 500 - 10, 500 - 15 }, // Aquatic product -0.5 degree
        { 500 - 20, 500 - 25 }, // Thaw -1.5 degree
        { 500 - 45, 500 - 50 }, // Smoothie
        { 500 + 40, 500 + 30 }, // Ref
    },
    // on temp   off temp
    {
        // 28 < RT <= 35
        { 500 - 20, 500 - 25 }, // Fresh meat -1.5 degree
        { 500 - 10, 500 - 15 }, // Aquatic product -0.5 degree
        { 500 - 20, 500 - 25 }, // Thaw -1.5 degree
        { 500 - 45, 500 - 50 }, // Smoothie
        { 500 + 40, 500 + 30 }, // Ref
    },
    // on temp   off temp
    {
        // 35 < RT <= 40
        { 500 - 20, 500 - 25 }, // Fresh meat -1.5 degree
        { 500 - 15, 500 - 20 }, // Aquatic product -0.5 degree
        { 500 - 20, 500 - 25 }, // Thaw -1.5 degree
        { 500 - 45, 500 - 50 }, // Smoothie
        { 500 + 40, 500 + 30 }, // Ref
    },
    // on temp   off temp
    {
        // 40 < RT
        { 500 - 20, 500 - 25 }, // Fresh meat -1.5 degree
        { 500 - 15, 500 - 20 }, // Aquatic product -0.5 degree
        { 500 - 20, 500 - 25 }, // Thaw -1.5 degree
        { 500 - 45, 500 - 50 }, // Smoothie
        { 500 + 40, 500 + 30 }, // Ref
    },
};
enum
{
    Signal_Entry = SimpleFsmSignal_Entry,
    Signal_Exit = SimpleFsmSignal_Exit,
    Signal_PollTimerExpired = SimpleFsmSignal_UserStart,
};

static void StageState1(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void StageState2(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void StageState3(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);

static bool Get_VarSensor_Error(void)
{
    bool b_RefVarSnrError = false;

    if((true == Get_SensorError(SENSOR_ICEMAKER_TOP)) ||
       (true == Get_SensorError(SENSOR_ICEMAKER_BOTTOM)) ||
       (true == Get_SensorError(SENSOR_ICEMAKER_BOTTOMX)) ||
       (true == Get_IceMakerCommErr()))
    {
        b_RefVarSnrError = true;
    }
    return b_RefVarSnrError;
}

static bool Get_Event5_Status(void)
{
    uint16_t topVarTemp = Get_SensorValue((SensorType_t)SENSOR_ICEMAKER_TOP);

    if(Get_VarSensor_Error() == true)
    {
        return true;
    }

    if((topVarTemp <= (520 + offset)) && (topVarTemp >= (500 + offset)))
        return true;

    return false;
}

static bool Get_Event6_Status(void)
{
    DefrostMode_t defrostmode = Get_DefrostMode();
    uint16_t topVarTemp = Get_SensorValue((SensorType_t)SENSOR_ICEMAKER_TOP);

    if(Get_VarSensor_Error() == true)
    {
        return true;
    }
 
    if((defrostmode >= eDefrostMode_Defrosting) && (defrostmode <= eDefrostMode_AfterFan)) // 强制复位
    {
        if(topVarTemp > (600 + offset))
            return true;
    }

    if(topVarTemp > (520 + offset))
        return true;

    return false;
}

static void StageState1(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    uint16_t topVarTemp = Get_SensorValue((SensorType_t)SENSOR_ICEMAKER_TOP);
    bool b_sensor_error = Get_VarSensor_Error();

    switch(signal)
    {
        case Signal_Entry:
            u16_StageState1TimerCounter = 0;
            VarControlStage = eVarControl_Stage_First;
            break;
        case Signal_PollTimerExpired:
            u16_StageState1TimerCounter++;
            if(b_sensor_error || topVarTemp > (520 + offset))
            {
                Var_On = 520 + offset;
                Var_Off = 515 + offset;
            }
            else
            {
                SimpleFsm_Transition(&st_Fsm, StageState2);
            }
            break;
        case Signal_Exit:
            u16_StageState1TimerCounter = 0;
            break;
        default:
            break;
    }
}

static void StageState2(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    bool Event6_Flag = Get_Event6_Status();
    switch(signal)
    {
        case Signal_Entry:
            u16_StageState2TimerCounter = 0;
            VarControlStage = eVarControl_Stage_Second;
            break;
        case Signal_PollTimerExpired:
            u16_StageState2TimerCounter++;
            Var_On = 500 + offset;
            Var_Off = 495 + offset;
            if(u16_StageState2TimerCounter >= 2 * 60 * 60)
            {
                SimpleFsm_Transition(&st_Fsm, StageState3);
            }
            else if(Event6_Flag)
            {
                SimpleFsm_Transition(&st_Fsm, StageState1);
            }
            break;
        case Signal_Exit:
            u16_StageState2TimerCounter = 0;
            break;
        default:
            break;
    }
}

static void StageState3(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    bool Event5_Flag = Get_Event5_Status();
    bool Event6_Flag = Get_Event6_Status();
    RoomTempRange_t room_range = Get_RoomTempRange();
    uint8_t var_offset = Get_RefVarSetTemp() - REFVAR_LEVEL_OFFSET;
    uint8_t var_set = Get_RefVarSetTemp();
    uint16_t bottomVarTemp1 = Get_SensorValue((SensorType_t)SENSOR_ICEMAKER_BOTTOM);
    uint16_t bottomVarTemp2 = Get_SensorValue((SensorType_t)SENSOR_ICEMAKER_BOTTOMX);
    uint16_t bottom_min = MIN(bottomVarTemp1, bottomVarTemp2);
    switch(signal)
    {
        case Signal_Entry:
            u16_StageState3TimerCounter = 0;
            Overload_protect_flag = false;
            u16_Overload_TimeStart = Get_MinuteCount();
            VarControlStage = eVarControl_Stage_Third;
            break;
        case Signal_PollTimerExpired:
            u16_StageState3TimerCounter++;
            Var_On = ary_RefVarOnOffTemp[room_range][var_offset].u16_OnTemp + offset;
            Var_Off = ary_RefVarOnOffTemp[room_range][var_offset].u16_OffTemp + offset;
            if(var_set <= eRefVar_Thaw)
            {
                if(bottom_min > (u16_Overload_Temp + offset))
                {
                    u16_Overload_Minute = Get_MinuteElapsedTime(u16_Overload_TimeStart);
                    if(u16_Overload_Minute >= U16_OVERLOAD_TIME_MINUTE)
                    {
                        Overload_protect_flag = true;
                    }
                }
                else
                {
                    u16_Overload_TimeStart = Get_MinuteCount();
                    if(bottom_min < (U16_OVERLOAD_EXIT_TEMP + offset))
                    {
                        Overload_protect_flag = false;
                    }
                }

                if(Overload_protect_flag)
                {
                    Var_On = Var_On - u16_Overload_Adjust;
                    Var_Off = Var_Off - u16_Overload_Adjust;
                }
            }

            if(Event6_Flag)
            {
                SimpleFsm_Transition(&st_Fsm, StageState1);
            }
            else if(Event5_Flag || Thaw_protect_flag)
            {
                SimpleFsm_Transition(&st_Fsm, StageState2);
            }
            break;
        case Signal_Exit:
            u16_StageState3TimerCounter = 0;
            break;
        default:
            break;
    }
}

static void Expired_PollTimer(void)
{
    bool deforst_first_on_flag = Get_Deforst_First_On_Flag();
    CoolingCompState_t comp_state = Get_CoolingCompState();
    uint8_t var_set = Get_RefVarSetTemp();
    uint16_t bottomVarTemp1 = Get_SensorValue((SensorType_t)SENSOR_ICEMAKER_BOTTOM);
    uint16_t bottomVarTemp2 = Get_SensorValue((SensorType_t)SENSOR_ICEMAKER_BOTTOMX);
    uint16_t bottom_min = MIN(bottomVarTemp1, bottomVarTemp2);
    bool b_sensor_error = Get_VarSensor_Error();
    bool b_energy = Get_EnergyConsumptionModeState();
    RoomTempRange_t room_range = Get_RoomTempRange();

    SimpleFsm_SendSignal(&st_Fsm, Signal_PollTimerExpired, NULL);

    // 如果此时处于化霜后第一次开机到关机期间，按照-0.5控制
    if(deforst_first_on_flag) 
    {
        if(room_range <= RT_BELOW28 || 
           Get_CompStillOnTimeMinute() < U16_DEFORST_VARCOOL_TIME_MINUTE)
        {
            Var_On = 495 + offset;
            Var_Off = 490 + offset;
        }
    }

    if(b_sensor_error == false &&
      (var_set == eRefVar_FreshMeat || var_set == eRefVar_Aquatic))
    {
        if(bottom_min < (470 + offset))
        {
            Thaw_protect_flag = true;
        }
        else if((bottomVarTemp1 > (470 + offset)) && (bottomVarTemp2 > (470 + offset)))
        {
            Thaw_protect_flag = false;
        }
    }
    else
    {
        Thaw_protect_flag = false;
    }

    if(b_sensor_error == false && 
       (var_set == eRefVar_Thaw && bottom_min < (450 + offset)))
    {
        u16_Thaw_Minute = Get_MinuteElapsedTime(u16_Thaw_TimeStart);
        Thaw_control_flag = false;
        if(u16_Thaw_Minute >= 30)
        {
            Thaw_control_flag = true;
            Var_On = 550 + offset;
            Var_Off = 545 + offset;
        }
    }
    else
    {
        Thaw_control_flag = false;
        u16_Thaw_TimeStart = Get_MinuteCount();
    }

    if(b_energy == false &&
       (var_set == eRefVar_FreshMeat || var_set == eRefVar_Aquatic))
    {
        if(b_VarRightLedState == false &&
           Get_MinuteElapsedTime(u16_VarRightLedOffStart) > U16_VARRIGHT_LEDOFF_TIME_MINUTE)
        {
            b_VarRightLedState = true;
            u16_VarRightLedOnTime = 0;
            Vote_DeviceStatus_Immediate(FSM_NormalControl, DEVICE_VarRightLamp, DS_On);
        }
        else if(b_VarRightLedState == true)
        {
            u16_VarRightLedOnTime++;
            if(u16_VarRightLedOnTime > U16_VARRIGHT_LEDON_TIME_SECOND)
            {
                Vote_DeviceStatus_Immediate(FSM_NormalControl, DEVICE_VarRightLamp, DS_Off);
                b_VarRightLedState = false;
                u16_VarRightLedOffStart = Get_MinuteCount();
            }
        }
    }
    else
    {
        b_VarRightLedState = false;
        u16_VarRightLedOffStart = Get_MinuteCount();
        Vote_DeviceStatus_Immediate(FSM_NormalControl, DEVICE_VarRightLamp, DS_Off);
    }
}

static void Start_PollTimer(uint16_t tickSec)
{
    Core_CallbackTimer_TimerStart(
        &st_CycleTimer,
        Expired_PollTimer,
        tickSec,
        0,
        eCallbackTimer_Type_Periodic,
        eCallbackTimer_Priority_Normal);
}

static void Stop_PollTimer(void)
{
    Core_CallbackTimer_TimerStop(&st_CycleTimer);
}

void Var_Cycle_Init(void)
{
    uint16_t topVarTemp = Get_SensorValue((SensorType_t)SENSOR_ICEMAKER_TOP);
    if(topVarTemp > (520 + offset))
    {
        SimpleFsm_Init(&st_Fsm, StageState1, NULL);
    }
    else if((topVarTemp <= (520 + offset)) && (topVarTemp >= (500 + offset)))
    {
        SimpleFsm_Init(&st_Fsm, StageState2, NULL);
    }
    else
    {
        SimpleFsm_Init(&st_Fsm, StageState3, NULL);
    }

    Start_PollTimer(CYCLE_POLL_SECONDS);
}

void Var_Cycle_Exit(void)
{
    Stop_PollTimer();
}

uint16_t Get_Var_On(void)
{
    return Var_On;
}

uint16_t Get_Var_Off(void)
{
    return Var_Off;
}

void UpdateVartempOffset(void)
{
    uint8_t var_offset;
    uint8_t var_set = Get_RefVarSetTemp();
    static uint8_t var_set_save = eRefVar_FreshMeat;
    
    if( var_set_save != var_set)
    {
        var_set_save  = var_set;
        Overload_protect_flag = false;
        u16_Overload_TimeStart = Get_MinuteCount();
    }

    if(var_set == eRefVar_FreshMeat || var_set == eRefVar_Thaw)
    {
        u16_Overload_Temp = U16_OVERLOAD_FRESHMEAT_TEMP;
        u16_Overload_Adjust = U16_OVERLOAD_FRESHMEAT_ADJSUT_TEMP;
    }
    else if(var_set == eRefVar_Aquatic)
    {
        u16_Overload_Temp = U16_OVERLOAD_AQUATIC_TEMP;
        u16_Overload_Adjust = U16_OVERLOAD_AQUATIC_ADJSUT_TEMP;  
    }
    else
    {
        Overload_protect_flag = false; 
        u16_Overload_TimeStart = Get_MinuteCount();
    }

    var_offset = var_set - REFVAR_LEVEL_OFFSET;
    offset = ary_RefVarOffset[var_offset];
}

VarControlStage_t GetVarControlStage(void)
{
    return VarControlStage;
}

bool IsVarControlOverloadProtect(void)
{
    return Overload_protect_flag;
}

bool IsVarControlFreezeProtect(void)
{
    return Thaw_protect_flag;
}

bool IsVarControlThawControl(void)
{
    return Thaw_control_flag;
}
