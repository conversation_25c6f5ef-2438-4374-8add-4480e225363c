/*!
 * @file
 * @brief This file defines public constants, types and functions for the usart.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "Adpt_Flash.h"

void Board_InitFlash(void)
{
    Flash_Init(FLASH_CONFIG_FREQ_48MHZ, FALSE);
}

uint16_t FlashSectorNumber(uint32_t u32Size)
{
    uint16_t u32PageNum = u32Size / FLASH_SECTOR_SIZE;

    if((u32Size % FLASH_SECTOR_SIZE) != 0)
    {
        u32PageNum += 1u;
    }

    return u32PageNum;
}

void FlashReadBytes(uint32_t u32Addr, uint8_t *pu8ReadBuff, uint32_t u32ByteLength)
{
    uint32_t i;

    for(i = 0; i < u32ByteLength; i++)
    {
        pu8ReadBuff[i] = *((unsigned char *)(u32Addr + i));
    }
}

en_result_t FlashWriteBytes(uint32_t u32Addr, uint8_t pu8Data[], uint32_t u32Len)
{
    Flash_OpModeConfig(FlashWriteMode);
    return Flash_Write8(u32Addr, pu8Data, u32Len);
}

en_result_t FlashWriteHalfWords(uint32_t u32Addr, uint16_t pu16Data[], uint32_t u32Len)
{
    Flash_OpModeConfig(FlashWriteMode);
    return Flash_Write16(u32Addr, pu16Data, u32Len);
}

en_result_t FlashWriteWords(uint32_t u32Addr, uint32_t pu32Data[], uint32_t u32Len)
{
    Flash_OpModeConfig(FlashWriteMode);
    return Flash_Write32(u32Addr, pu32Data, u32Len);
}

en_result_t FlashSectorErase(uint32_t u32SectorAddr)
{
    if((u32SectorAddr % 4) != 0)
    {
        u32SectorAddr = (u32SectorAddr / FLASH_SECTOR_SIZE) * FLASH_SECTOR_SIZE;
    }

    Flash_OpModeConfig(FlashSectorEraseMode);
    return Flash_SectorErase(u32SectorAddr);
}

en_result_t FlashContWriteBytes(uint32_t u32Addr, uint8_t pu8Data[], uint32_t u32Len)
{
    Flash_OpModeConfig(FlashContinueWriteMode);
    return Flash_ContWrite_Byte(u32Addr, pu8Data, u32Len);
}

en_result_t FlashContWriteHalfWords(uint32_t u32Addr, uint16_t pu16Data[], uint32_t u32Len)
{
    Flash_OpModeConfig(FlashWriteMode);
    return Flash_ContWrite_HalfWord(u32Addr, pu16Data, u32Len);
}

en_result_t FlashContWriteWords(uint32_t u32Addr, uint32_t pu32Data[], uint32_t u32Len)
{
    Flash_OpModeConfig(FlashWriteMode);
    return Flash_ContWrite_Word(u32Addr, pu32Data, u32Len);
}

en_result_t FlashEraseAll(void)
{
    return Flash_Chip_Erase();
}
