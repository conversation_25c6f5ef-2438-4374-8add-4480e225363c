/*!
 * @file
 * @brief Manages all the state variables of the frz fan resolver.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "Core_Assert.h"
#include "ResolverDevice.h"
#include "ResolverDevicePriority.h"

// #if(ASSERT_TYPE == ASSERT_LOG_VC)
// DEFINE_THIS_FILE; // This definition is required to access ASSERT
// #endif

static uint8_t Search_ValueInArray(const uint8_t *pu8_TargetArray,
    uint8_t u8_TargetValue,
    uint8_t u8_ArrayLimit);

void Init_ResolverDeviceData(void)
{
    // Temporary Device ID
    uint8_t u8_DeviceID;
    // Temporary priority
    uint8_t u8_Priority;
    // Device Status Priority Pointer
    int8_t *pi8_DeviceStatusPriority;
    uint8_t const *p_DefaultStateArray;
    p_DefaultStateArray = au8_DeviceDefaultState;

    // Initialize all Resolver Devices
    for(u8_DeviceID = (uint8_t)ZERO; u8_DeviceID < (uint8_t)DEVICE_LastDevice;
        u8_DeviceID++)
    {
        // Set devices status priority pointer
        pi8_DeviceStatusPriority =
            asVoteDeviceStatus[u8_DeviceID].pai8_DeviceStatusData;

        for(u8_Priority = (uint8_t)ZERO;
            u8_Priority < asVoteDeviceStatus[u8_DeviceID].u8_MaxDevicePriority;
            u8_Priority++, pi8_DeviceStatusPriority++)
        {
            // assign don't care to all voting data
            *pi8_DeviceStatusPriority = (int8_t)DS_DontCare;
        }

        // Initialize resolver data to default state for all devices.
        au8_ResolverDeviceStatus[u8_DeviceID] = p_DefaultStateArray[u8_DeviceID];
    }
}

void Vote_DeviceStatus(uint8_t u8_FSM_ID, uint8_t u8_Device_ID, int8_t s8_DeviceState)
{
    // Temporary FSM priority
    uint8_t u8_FSMPriority;
    // Validate Device ID
    ENSURE(u8_Device_ID < (uint8_t)DEVICE_LastDevice);
    // Search FSM Priority
    u8_FSMPriority =
        Search_ValueInArray(apau8_DevicePriorityTable[u8_Device_ID],
            u8_FSM_ID,
            asVoteDeviceStatus[u8_Device_ID].u8_MaxDevicePriority);

    // FSM Priority is valid for this device set Requested state for it at
    // derived priority level.
    if(U8_INVALID_INDEX != u8_FSMPriority)
    {
        asVoteDeviceStatus[u8_Device_ID].pai8_DeviceStatusData[u8_FSMPriority] =
            s8_DeviceState;
    }
}

void Vote_DeviceStatus_Immediate(uint8_t u8_FSM_ID, uint8_t u8_Device_ID, int8_t s8_DeviceState)
{
    bool b_ValidVoteUpdated = false;
    uint8_t u8_Priority;
    uint8_t const *pu8_DevicePriorityTable;
    int8_t *pi8_DeviceStatusPriority;
    uint8_t u8_FSMPriority;
    uint8_t const *p_DefaultStateArray;

    p_DefaultStateArray = au8_DeviceDefaultState;

    if(u8_Device_ID >= (uint8_t)DEVICE_LastDevice)
    {
        return;
    }
    u8_FSMPriority =
        Search_ValueInArray(apau8_DevicePriorityTable[u8_Device_ID],
            u8_FSM_ID,
            asVoteDeviceStatus[u8_Device_ID].u8_MaxDevicePriority);

    if(U8_INVALID_INDEX != u8_FSMPriority)
    {
        asVoteDeviceStatus[u8_Device_ID].pai8_DeviceStatusData[u8_FSMPriority] =
            s8_DeviceState;
    }
    else
    {
        return;
    }

    pu8_DevicePriorityTable = apau8_DevicePriorityTable[u8_Device_ID];
    b_ValidVoteUpdated = false;
    pi8_DeviceStatusPriority = asVoteDeviceStatus[u8_Device_ID].pai8_DeviceStatusData;
    if(NULL == pi8_DeviceStatusPriority)
    {
        return;
    }

    for(u8_Priority = (uint8_t)ZERO;
        u8_Priority < asVoteDeviceStatus[u8_Device_ID].u8_MaxDevicePriority;
        u8_Priority++, pi8_DeviceStatusPriority++, pu8_DevicePriorityTable++)
    {
        if((int8_t)DS_DontCare != *pi8_DeviceStatusPriority)
        {
            if((int8_t)DS_NoChange != *pi8_DeviceStatusPriority)
            {
                au8_ResolverDeviceStatus[u8_Device_ID] = (uint8_t)*pi8_DeviceStatusPriority;
            }
            au8_DeviceControllingFSM[u8_Device_ID] = *pu8_DevicePriorityTable;
            b_ValidVoteUpdated = true;
            break;
        }
    }

    if(false == b_ValidVoteUpdated)
    {
        au8_DeviceControllingFSM[u8_Device_ID] = (uint8_t)FSM_DefaultControl;
        au8_ResolverDeviceStatus[u8_Device_ID] = p_DefaultStateArray[u8_Device_ID];
    }

    if(NULL != afp_DeviceControlFunctions[u8_Device_ID])
    {
        afp_DeviceControlFunctions[u8_Device_ID](au8_ResolverDeviceStatus[u8_Device_ID]);
    }
}

static uint8_t Search_ValueInArray(const uint8_t *pu8_TargetArray,
    uint8_t u8_TargetValue,
    uint8_t u8_ArrayLimit)
{
    // Temporary Array Index
    uint8_t u8_ArrayIndex;
    // Return Index
    uint8_t u8_ReturnIndex = U8_INVALID_INDEX;

    // Is Array pointer valid?
    if((uint8_t *)NULL != pu8_TargetArray)
    {
        // Array Pointer and Array Limit valid
        // Search requested value in Array by using array pointer and array limit
        for(u8_ArrayIndex = (uint8_t)ZERO; u8_ArrayIndex < u8_ArrayLimit;
            u8_ArrayIndex++, pu8_TargetArray++)
        {
            // Requested value is maching with array contant?
            if(*pu8_TargetArray == u8_TargetValue)
            {
                // Requested value is maching with array contant. Set Retern
                // value with Array index
                u8_ReturnIndex = u8_ArrayIndex;
                // value found stop searching.
                break;
            }
        }
    }

    return (u8_ReturnIndex);
}

void Process_ResolverDeviceData(void)
{
    // valid vote updated flag
    bool b_ValidVoteUpdated = false;
    // Temporary Device ID
    uint8_t u8_DeviceID;
    // Temporary priority
    uint8_t u8_Priority;
    // Device Priority Table pointer
    uint8_t const *pu8_DevicePriorityTable;
    // Device Status Priority Pointer
    int8_t *pi8_DeviceStatusPriority;
    uint8_t const *p_DefaultStateArray;

    // Disable the interrupts since some loads are being updated for an ISR
    // because of timing constraints.
    // COREUSER_INTERRUPTS_CONTEXT_PROTECT;
    p_DefaultStateArray = au8_DeviceDefaultState;

    // Process voting data for all device
    for(u8_DeviceID = (uint8_t)ZERO; u8_DeviceID < (uint8_t)DEVICE_LastDevice;
        u8_DeviceID++)
    {
        // Set Device Priority table pointer
        pu8_DevicePriorityTable = apau8_DevicePriorityTable[u8_DeviceID];
        // Reset Valid Vote Updated flag
        b_ValidVoteUpdated = false;
        // Set devices status priority pointer
        pi8_DeviceStatusPriority = asVoteDeviceStatus[u8_DeviceID].pai8_DeviceStatusData;
        // Verify devices status priority pointer
        ENSURE((int8_t *)NULL != pi8_DeviceStatusPriority);

        // Resolve Voted data for this Device
        for(u8_Priority = (uint8_t)ZERO;
            u8_Priority < asVoteDeviceStatus[u8_DeviceID].u8_MaxDevicePriority;
            u8_Priority++, pi8_DeviceStatusPriority++, pu8_DevicePriorityTable++)
        {
            // If not Don't care, i.e. valid status
            if((int8_t)DS_DontCare != *pi8_DeviceStatusPriority)
            {
                // If not No Change update Resolved status; go for next device
                if((int8_t)DS_NoChange != *pi8_DeviceStatusPriority)
                {
                    // Update Valid status to the resolver Device status
                    // Here intentional uint8_t cast provided because at this
                    // point this pointer will point other than DS_DontCare and
                    // DS_NoChange so all of them will be positive.
                    au8_ResolverDeviceStatus[u8_DeviceID] = (uint8_t)*pi8_DeviceStatusPriority;
                }

                // Update Device controlling FSM information
                au8_DeviceControllingFSM[u8_DeviceID] = *pu8_DevicePriorityTable;
                // Valid Vote Updated
                b_ValidVoteUpdated = true;
                break;
            }
        }

        // If Valid Vote Updated flag is false, i.e. all vote are Don't care;
        if(false == b_ValidVoteUpdated)
        {
            // Update Device controlling FSM information with Default Control
            au8_DeviceControllingFSM[u8_DeviceID] = (uint8_t)FSM_DefaultControl;
            // All priority level status is don't care; update resolver data
            // with Default state.
            au8_ResolverDeviceStatus[u8_DeviceID] = p_DefaultStateArray[u8_DeviceID];
        }
    }

    for(u8_DeviceID = (uint8_t)ZERO; u8_DeviceID < (uint8_t)DEVICE_LastDevice;
        u8_DeviceID++)
    {
        if(NULL != afp_DeviceControlFunctions[u8_DeviceID])
        {
            afp_DeviceControlFunctions[u8_DeviceID](au8_ResolverDeviceStatus[u8_DeviceID]);
        }
    }

    // Revert back the flag register interrupt bit
    // COREUSER_INTERRUPTS_CONTEXT_UNPROTECT;
}

uint8_t Get_ResolvedDeviceStatus(uint8_t u8_DeviceID)
{
    // return device status
    uint8_t u8_ReturnDeviceStatus;
    // Validate Device ID
    ENSURE(u8_DeviceID < (uint8_t)DEVICE_LastDevice);
    // Return Resolved status for requested device.
    u8_ReturnDeviceStatus = au8_ResolverDeviceStatus[u8_DeviceID];
    return (u8_ReturnDeviceStatus);
}

int8_t Get_ResolvedDeviceStatusFSM(uint8_t u8_FSM_ID, uint8_t u8_Device_ID)
{
    uint8_t u8_FSMPriority;

    if(u8_Device_ID >= (uint8_t)DEVICE_LastDevice)
    {
        return DS_DontCare;
    }

    u8_FSMPriority =
        Search_ValueInArray(apau8_DevicePriorityTable[u8_Device_ID],
            u8_FSM_ID,
            asVoteDeviceStatus[u8_Device_ID].u8_MaxDevicePriority);

    if(U8_INVALID_INDEX != u8_FSMPriority)
    {
        return asVoteDeviceStatus[u8_Device_ID].pai8_DeviceStatusData[u8_FSMPriority];
    }
    else
    {
        return DS_DontCare;
    }  
}

uint8_t Get_DeviceControllingFSM(uint8_t u8_DeviceID)
{
    // return device controlling FSM ID
    uint8_t u8_DeviceControllingFSM_ID = (uint8_t)FSM_TotalFSMs;
    // Validate Device ID
    ENSURE(u8_DeviceID < (uint8_t)DEVICE_LastDevice);
    // Get FSM ID in which currently requested Device is in Control.
    u8_DeviceControllingFSM_ID = au8_DeviceControllingFSM[u8_DeviceID];
    return (u8_DeviceControllingFSM_ID);
}
