﻿// ImageHeader.cpp : 此文件包含 "main" 函数。程序执行将在此处开始并结束。
//

#include <iostream>
#include <fstream>
#include <stdio.h>
#include <windows.h>
#include <string>
#include <cstring>
#include <cstdlib>
#include "ImageHeader.h"

using namespace std;

#define U16_CRC_INITIAL_VALUE (uint16_t)0xFFFF

static const uint16_t ary_CRC16_Table0[16] = {
    0x0000U, 0xC0C1U, 0xC181U, 0x0140U, 0xC301U, 0x03C0U, 0x0280U, 0xC241U, 0xC601U, 0x06C0U, 0x0780U, 0xC741U, 0x0500U, 0xC5C1U, 0xC481U, 0x0440U
};

static const uint16_t ary_CRC16_Table1[16] = {
    0x0000U, 0xCC01U, 0xD801U, 0x1400U, 0xF001U, 0x3C00U, 0x2800U, 0xE401U, 0xA001U, 0x6C00U, 0x7800U, 0xB401U, 0x5000U, 0x9C01U, 0x8801U, 0x4400U
};

static uint32_t convert_to_bigendian32(uint32_t val)
{
    uint32_t temp = 0;
    uint8_t* p = (uint8_t *)&temp;

    p[0] = (val >> 24) & 0xFF;
    p[1] = (val >> 16) & 0xFF;
    p[2] = (val >> 8)  & 0xFF;
    p[3] = (val) & 0xFF;

    return temp;
}

static uint16_t convert_to_bigendian16(uint16_t val)
{
    uint16_t temp = 0;
    uint8_t* p = (uint8_t*)&temp;

    p[0] = (val >> 8) & 0xFF;
    p[1] = (val) & 0xFF;

    return temp;
}

uint16_t Cal_CRC_SingleData(uint16_t u16_crcvalue, uint8_t u8_data)
{
    uint8_t u8_databyte = 0;
    uint8_t u8_low_halfbyte_index = 0;
    uint8_t u8_high_halfbyte_index = 0;
    uint16_t u16_crcvalue_table0 = 0;
    uint16_t u16_crcvalue_table1 = 0;
    uint16_t u16_crcvalue_highbyte = 0;
    u8_databyte = (uint8_t)(u16_crcvalue & 0x00ff);
    u8_databyte ^= u8_data;
    u8_low_halfbyte_index = u8_databyte & 0x0f;
    u8_high_halfbyte_index = (u8_databyte >> 4) & 0x0f;
    u16_crcvalue_table0 = ary_CRC16_Table0[u8_low_halfbyte_index];
    u16_crcvalue_table1 = ary_CRC16_Table1[u8_high_halfbyte_index];
    u16_crcvalue_highbyte = (u16_crcvalue >> 8) & 0x00ff;
    u16_crcvalue =
        u16_crcvalue_table0 ^ u16_crcvalue_table1 ^ u16_crcvalue_highbyte;
    return (u16_crcvalue);
}

int main(int argc, const char *argv[])
{
    app_imageheader_st header;
    uint32_t version;
    uint32_t maxsize;
    ifstream imagef;
    ofstream imageh;
    string path;
    size_t pos;
    size_t len;
    char buf[1024];
    size_t wlen = 0;
    size_t rlen = 0;
    char pad = 0xff;
    uint16_t crc = U16_CRC_INITIAL_VALUE;
    uint16_t index;
    
    if (argc < 4)
    {
        cout << "Use ImageHeader.exe version  file maxsize" << endl;
        goto out;
    }

    path = argv[2];
    memset(&header, 0, sizeof(app_imageheader_st));
    version = atoi(argv[1]);
    if (version == 0)
    {
        cout << "version:" << version << " is not excepted!" << endl;
        goto out;
    }

    cout << "version:" << version << endl;
    imagef.open(path, ios::binary | ios::in);
    if (!imagef)
    {
        cout << "file: " << path << " is not exsited" << endl;
        goto out;
    }
    if(strncmp(argv[3], "0x", 2))
    {
        cout << "maxsize  is a hex with 0x" << endl;
        goto out;
    }
    maxsize = std::stoul(argv[3], nullptr, 16);
    if (maxsize == 0)
    {
        cout << "maxsize:" << maxsize << "error!" << endl;
        goto out;
    }
    imagef.seekg(0, ios::end);
    len = imagef.tellg();
    imagef.seekg(0, ios::beg);
    len += sizeof(app_imageheader_st);
    if (len % 128 != 0)
    {
        len = (len / 128 + 1) * 128;
    }
    if (len > maxsize)
    {
        cout << "calc out file len:" << len << " is larger than maxsize:" << maxsize << endl;
        goto out;
    }

    cout << "calc out file len:" << len << endl;

    pos = path.find_last_of('.');
    path.insert(pos, "_header");
    cout << "out file " << path << " start" << endl;
    imageh.open(path, ios::binary | ios::out | ios::trunc);
    header.magic = convert_to_bigendian32(MAGIC_NUM);
    header.length = convert_to_bigendian32(len);
    header.version = convert_to_bigendian32(version);
    header.crc16 = 0;
    imageh.write((char *)&header, sizeof(header));
    wlen += sizeof(header);
    while(!(imagef.rdstate() & ios_base::eofbit))
    {
        imagef.read(buf, 1024);
        rlen = imagef.gcount();
        for (index = 0; index < rlen; index++)
        {
            crc = Cal_CRC_SingleData(crc, buf[index]);
        }
        imageh.write(buf, rlen);
        wlen += rlen;
    }

    len -= wlen;
    while (len--)
    {
        imageh.write(&pad, 1);
        crc = Cal_CRC_SingleData(crc, pad);
    }
    cout << "CRC:" << hex << crc << endl;
    header.crc16 = convert_to_bigendian16(crc);
    imageh.seekp(ios::beg);
    imageh.write((char*)&header, sizeof(header));
    imageh.seekp(ios::end);
    imageh.close();
    imagef.close();
    cout << "out file " << path << " end" << endl;
    return 0;
 
out:
    while (1)
    {
        Sleep(30);
    }
}

// 运行程序: Ctrl + F5 或调试 >“开始执行(不调试)”菜单
// 调试程序: F5 或调试 >“开始调试”菜单

// 入门使用技巧: 
//   1. 使用解决方案资源管理器窗口添加/管理文件
//   2. 使用团队资源管理器窗口连接到源代码管理
//   3. 使用输出窗口查看生成输出和其他消息
//   4. 使用错误列表窗口查看错误
//   5. 转到“项目”>“添加新项”以创建新的代码文件，或转到“项目”>“添加现有项”以将现有代码文件添加到项目
//   6. 将来，若要再次打开此项目，请转到“文件”>“打开”>“项目”并选择 .sln 文件
