/**
 *******************************************************************************
 * @file  wdt.c
 * @brief This file provides firmware functions to manage the WDT.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-03-15       MADS            First version
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2023, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 *******************************************************************************
 */

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "wdt.h"

/**
 * @addtogroup HC32L186_DDL 驱动库
 * @{
 */

/**
 * @defgroup DDL_WDT WDT模块驱动库
 * @brief WDT Driver Library WDT模块驱动库
 * @{
 */

/*******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/*******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/

/*******************************************************************************
 * Global variable definitions (declared in header file with 'extern')
 ******************************************************************************/

/*******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/

/*******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/

/*******************************************************************************
 * Function implementation - global ('extern') and local ('static')
 ******************************************************************************/
/**
 * @defgroup WDT_Global_Functions WDT全局函数定义
 * @{
 */

/**
 * @brief  WDT溢出时间设置函数
 * @param [in] u8LoadValue 溢出时间
 * @retval None
 */
void Wdt_WriteWdtLoad(uint8_t u8LoadValue)
{
    M0P_WDT->CON_f.WOV = u8LoadValue;
}
/**
 * @brief  WDT初始化函数
 * @param [in] enFunc @ref en_wdt_func_t
 * @param [in] enTime @ref en_wdt_time_t
 * @retval en_result_t
 *         - Ok: No error
*/
en_result_t Wdt_Init(en_wdt_func_t enFunc, en_wdt_time_t enTime)
{
    en_result_t enRet = Error;

    Wdt_WriteWdtLoad(enTime);
    M0P_WDT->CON_f.WINT_EN = enFunc;
    enRet = Ok;
    return enRet;
}
/**
 * @brief  WDT启动函数
 * @retval None
*/
void Wdt_Start(void)
{
    M0P_WDT->RST = 0x1E;
    M0P_WDT->RST = 0xE1;
}

/**
 * @brief  WDT喂狗
 * @retval None
 */
void Wdt_Feed(void)
{
    M0P_WDT->RST = 0x1E;
    M0P_WDT->RST = 0xE1;
}

/**
 * @brief  WDT中断标志清除
 * @retval None
*/
void Wdt_IrqClr(void)
{
    M0P_WDT->RST = 0x1E;
    M0P_WDT->RST = 0xE1;
}

/**
 * @brief  WDT读取当前计数值函数
 * @retval uint8_t: 计数值
*/
uint8_t Wdt_ReadWdtValue(void)
{
    uint8_t u8Count;

    u8Count = M0P_WDT->CON_f.WCNTL;

    return u8Count;
}
/**
 * @brief  WDT读取当前运行状态
 * @retval boolean_t: 状态值
*/
boolean_t Wdt_ReadwdtStatus(void)
{
    if (M0P_WDT->CON & 0x10u)
    {
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}

/**
 * @brief WDT 中断状态标记获取
 * @retval boolean_t: 中断状态
*/
boolean_t Wdt_GetIrqStatus(void)
{
    if (M0P_WDT->CON & 0x80u)
    {
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}

/**
 * @}
 */

/**
 * @}
 */

/**
 * @}
 */

/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/
