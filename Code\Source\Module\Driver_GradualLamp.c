
/*!
 * @file
 * @brief This module is the gradual lamp drive module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "Driver_GradualLamp.h"
#include "Adpt_PWM.h"

static const GradualLampUpdateParm_st ary_GradualLampUpdateParm[] = {
    { 300, 10 },
    { 600, 15 },
    { 1000, 20 },
    { 1500, 25 },
    { 2100, 30 },
    { 2900, 40 },
    { 3900, 50 },
    { 5100, 60 },
    { 6400, 70 }
};

static GradualLampParm_st ary_GradualLampParm[(uint8_t)GRADUAL_LAMP_MAX];
static uint16_t Calculate_GradualLampDutyValue(uint8_t u8_lamp_duty);
static void Set_GradualLampID0PWMDutyValue(uint16_t u16_duty_value);
static ConGradualLampParm_st ary_ConGradualLampParm[(uint8_t)GRADUAL_LAMP_MAX] = {
    // ID0
    {
        Set_GradualLampID0PWMDutyValue,

        U16_GRADUAL_LAMP_REAL_CYCLE_VALUE,
        100,
        0,
        GRADUAL_LAMP_TYPE_BRIGHTENING }
};

static uint16_t Calculate_GradualLampDutyValue(uint8_t u8_lamp_duty)
{
    uint16_t u16_pwm_duty_value = 0;

    if(u8_lamp_duty >= U8_LAMP_DUTY_MAX_VALUE)
    {
        u16_pwm_duty_value = U16_GRADUAL_LAMP_CYCLE_MAX_VALUE;
    }
    else if(u8_lamp_duty > 0)
    {
        u16_pwm_duty_value = (uint16_t)u8_lamp_duty;
        u16_pwm_duty_value = (uint16_t)((uint32_t)u16_pwm_duty_value *
            (uint32_t)U16_GRADUAL_LAMP_CYCLE_MAX_VALUE / (uint32_t)U8_LAMP_DUTY_MAX_VALUE);
    }

    return u16_pwm_duty_value;
}

static void Set_GradualLampID0PWMDutyValue(uint16_t u16_duty_value)
{
    uint16_t u16_period = ary_ConGradualLampParm[(uint8_t)GRADUAL_LAMP_ID0].u16_PWMCycleValue;
    uint16_t u16_pulse;
    if(u16_duty_value >= u16_period)
    {
        u16_duty_value = u16_period - 1;
    }
    u16_pulse = u16_period - u16_duty_value;
    IO_REF_TOP_LAMP(u16_pulse);
}

void Init_GradualLamp(void)
{
    uint8_t u8_index = 0;
    GradualLampParm_st *pst_parm = (GradualLampParm_st *)NULL;

    for(u8_index = 0; u8_index < (uint8_t)GRADUAL_LAMP_MAX; u8_index++)
    {
        pst_parm = &ary_GradualLampParm[u8_index];

        if((false == pst_parm->f_Init) || (true == pst_parm->f_FactoryTest))
        {
            pst_parm->f_Init = true;
            pst_parm->f_FactoryTest = false;
            pst_parm->f_LampState = false;
            pst_parm->em_GradualLampState = GRADUAL_LAMP_STATE_STOP;
            pst_parm->u16_TimeMselCount = 0;
            pst_parm->u16_PWMDutyValue = 0;
            pst_parm->u16_PWMDutyRealValue = 0;

            pst_parm->u16_PWMSetMaxDutyValve =
                Calculate_GradualLampDutyValue(ary_ConGradualLampParm[u8_index].u8_PWMMaxDuty);

            pst_parm->u16_PWMSetMinDutyValve =
                Calculate_GradualLampDutyValue(ary_ConGradualLampParm[u8_index].u8_PWMMinDuty);
        }
    }

    // Set_GradualLampState(REF_SURFACE_LAMP, true);
}

void Test_GradualLamp(uint8_t u8_gradual_lamp_ID, bool b_test_state)
{
    if(u8_gradual_lamp_ID < (uint8_t)GRADUAL_LAMP_MAX)
    {
        if(true == b_test_state)
        {
            ary_GradualLampParm[u8_gradual_lamp_ID].f_LampState = true;
            ary_GradualLampParm[u8_gradual_lamp_ID].f_FactoryTest = true;
        }
        else
        {
            ary_GradualLampParm[u8_gradual_lamp_ID].f_LampState = false;
            ary_GradualLampParm[u8_gradual_lamp_ID].f_FactoryTest = false;
        }
    }
}

void Set_GradualLampMaxDuty(const GradualLampID_em em_gradual_lamp_ID, uint8_t u8_max_duty)
{
    if(em_gradual_lamp_ID < GRADUAL_LAMP_MAX)
    {
        if(u8_max_duty > U8_LAMP_DUTY_MAX_VALUE)
        {
            u8_max_duty = U8_LAMP_DUTY_MAX_VALUE;
        }
        else if(u8_max_duty < ary_ConGradualLampParm[(uint8_t)em_gradual_lamp_ID].u8_PWMMinDuty)
        {
            u8_max_duty = ary_ConGradualLampParm[(uint8_t)em_gradual_lamp_ID].u8_PWMMinDuty;
        }

        ary_GradualLampParm[(uint8_t)em_gradual_lamp_ID].u16_PWMSetMaxDutyValve =
            Calculate_GradualLampDutyValue(u8_max_duty);
    }
}

void Set_GradualLampState(const GradualLampID_em em_gradual_lamp_ID, const bool b_lamp_state)
{
    if((em_gradual_lamp_ID < GRADUAL_LAMP_MAX) && (false == ary_GradualLampParm[(uint8_t)em_gradual_lamp_ID].f_FactoryTest))
    {
        if(true == b_lamp_state)
        {
            ary_GradualLampParm[(uint8_t)em_gradual_lamp_ID].f_LampState = true;
        }
        else
        {
            ary_GradualLampParm[(uint8_t)em_gradual_lamp_ID].f_LampState = false;
        }
    }
}

bool Get_GradualLampState(const GradualLampID_em em_gradual_lamp_ID)
{
    bool b_lamp_state = false;

    if(em_gradual_lamp_ID < GRADUAL_LAMP_MAX)
    {
        b_lamp_state = ary_GradualLampParm[(uint8_t)em_gradual_lamp_ID].f_LampState;
    }

    return b_lamp_state;
}

void Driver_GradualLamp(void)
{
    uint8_t u8_index = 0;
    uint8_t u8_segment_index = 0;
    uint16_t u16_duty_value = 0;
    GradualLampParm_st *pst_parm = (GradualLampParm_st *)NULL;
    ConGradualLampParm_st *pst_config_parm = (ConGradualLampParm_st *)NULL;

    for(u8_index = 0; u8_index < (uint8_t)GRADUAL_LAMP_MAX; u8_index++)
    {
        pst_parm = &ary_GradualLampParm[u8_index];
        pst_config_parm = &ary_ConGradualLampParm[u8_index];
        u16_duty_value = pst_parm->u16_PWMDutyValue;

        if(true == pst_parm->f_FactoryTest)
        {
            if(true == pst_parm->f_LampState)
            {
                u16_duty_value = U16_GRADUAL_LAMP_CYCLE_MAX_VALUE;
            }
            else
            {
                u16_duty_value = 0;
            }
        }
        else
        {
            switch(pst_parm->em_GradualLampState)
            {
                case GRADUAL_LAMP_STATE_STOP:
                    if(false == pst_parm->f_LampState)
                    {
                        u16_duty_value = 0;
                    }
                    else
                    {
                        pst_parm->u16_TimeMselCount = 0;

                        if(GRADUAL_LAMP_TYPE_FADING != pst_config_parm->em_GradualLampType)
                        {
                            u16_duty_value = pst_parm->u16_PWMSetMinDutyValve;
                            pst_parm->em_GradualLampState = GRADUAL_LAMP_STATE_BRIGHTENING;
                        }
                        else
                        {
                            u16_duty_value = pst_parm->u16_PWMSetMaxDutyValve;
                            pst_parm->em_GradualLampState = GRADUAL_LAMP_STATE_KEEP_HIGH_BRIGHT;
                        }
                    }
                    break;
                case GRADUAL_LAMP_STATE_BRIGHTENING:
                    if(false == pst_parm->f_LampState)
                    {
                        if(GRADUAL_LAMP_TYPE_BRIGHTENING == pst_config_parm->em_GradualLampType)
                        {
                            u16_duty_value = 0;
                            pst_parm->em_GradualLampState = GRADUAL_LAMP_STATE_STOP;
                        }
                        else
                        {
                            pst_parm->u16_TimeMselCount = 0;
                            pst_parm->em_GradualLampState = GRADUAL_LAMP_STATE_FADING;
                        }
                    }
                    else if(u16_duty_value >= pst_parm->u16_PWMSetMaxDutyValve)
                    {
                        pst_parm->u16_TimeMselCount = 0;
                        u16_duty_value = pst_parm->u16_PWMSetMaxDutyValve;
                        pst_parm->em_GradualLampState = GRADUAL_LAMP_STATE_KEEP_HIGH_BRIGHT;
                    }
                    else
                    {
                        pst_parm->u16_TimeMselCount++;

                        if(((GRADUAL_LAMP_TYPE_BREATHING == pst_config_parm->em_GradualLampType) &&
                               (pst_parm->u16_TimeMselCount >= U16_BREATH_LAMP_UPDATE_INTERVAL_TIME_MSELS)) ||
                            ((GRADUAL_LAMP_TYPE_BREATHING != pst_config_parm->em_GradualLampType) &&
                                (pst_parm->u16_TimeMselCount >= U16_GRADUAL_LAMP_UPDATE_INTERVAL_TIME_MSELS)))
                        {
                            pst_parm->u16_TimeMselCount = 0;

                            for(u8_segment_index = 0; u8_segment_index < U8_SEGMENT_NUMBER; u8_segment_index++)
                            {
                                if(u16_duty_value < ary_GradualLampUpdateParm[u8_segment_index].u16_SegmentValue)
                                {
                                    u16_duty_value += ary_GradualLampUpdateParm[u8_segment_index].u16_SetpValue;
                                    break;
                                }
                            }
                        }
                    }
                    break;
                case GRADUAL_LAMP_STATE_KEEP_HIGH_BRIGHT:
                    if(false == pst_parm->f_LampState)
                    {
                        if(GRADUAL_LAMP_TYPE_BRIGHTENING == pst_config_parm->em_GradualLampType)
                        {
                            u16_duty_value = 0;
                            pst_parm->em_GradualLampState = GRADUAL_LAMP_STATE_STOP;
                        }
                        else
                        {
                            pst_parm->u16_TimeMselCount = 0;
                            pst_parm->em_GradualLampState = GRADUAL_LAMP_STATE_FADING;
                        }
                    }
                    else if(GRADUAL_LAMP_TYPE_BREATHING == pst_config_parm->em_GradualLampType)
                    {
                        pst_parm->u16_TimeMselCount++;

                        if(pst_parm->u16_TimeMselCount >= U16_BREATH_LAMP_KEEP_HIGH_BRIGHT_TIME_MSELS)
                        {
                            pst_parm->u16_TimeMselCount = 0;
                            pst_parm->em_GradualLampState = GRADUAL_LAMP_STATE_FADING;
                        }
                    }
                    break;
                case GRADUAL_LAMP_STATE_FADING:
                    pst_parm->u16_TimeMselCount++;
                    if(((GRADUAL_LAMP_TYPE_BREATHING ==
                            pst_config_parm->em_GradualLampType) &&
                           (pst_parm->u16_TimeMselCount >=
                               U16_BREATH_LAMP_UPDATE_INTERVAL_TIME_MSELS)) ||
                        ((GRADUAL_LAMP_TYPE_BREATHING !=
                             pst_config_parm->em_GradualLampType) &&
                            (pst_parm->u16_TimeMselCount >=
                                U16_GRADUAL_LAMP_UPDATE_INTERVAL_TIME_MSELS)))
                    {
                        pst_parm->u16_TimeMselCount = 0;

                        for(u8_segment_index = 0; u8_segment_index < U8_SEGMENT_NUMBER; u8_segment_index++)
                        {
                            if(u16_duty_value <= ary_GradualLampUpdateParm[u8_segment_index].u16_SegmentValue)
                            {
                                if(u16_duty_value <= ary_GradualLampUpdateParm[u8_segment_index].u16_SetpValue)
                                {
                                    u16_duty_value = 0;
                                }
                                else
                                {
                                    u16_duty_value -= ary_GradualLampUpdateParm[u8_segment_index].u16_SetpValue;
                                }

                                if(false == pst_parm->f_LampState)
                                {
                                    if(0 == u16_duty_value)
                                    {
                                        pst_parm->em_GradualLampState = GRADUAL_LAMP_STATE_STOP;
                                    }
                                }
                                else if(GRADUAL_LAMP_TYPE_BREATHING != pst_config_parm->em_GradualLampType)
                                {
                                    pst_parm->em_GradualLampState = GRADUAL_LAMP_STATE_BRIGHTENING;
                                }
                                else if(u16_duty_value <= pst_parm->u16_PWMSetMinDutyValve)
                                {
                                    u16_duty_value = pst_parm->u16_PWMSetMinDutyValve;
                                    pst_parm->em_GradualLampState = GRADUAL_LAMP_STATE_KEEP_LOW_BRIGHT;
                                }
                                break;
                            }
                        }
                    }
                    break;
                case GRADUAL_LAMP_STATE_KEEP_LOW_BRIGHT:
                    if(false == pst_parm->f_LampState)
                    {
                        pst_parm->u16_TimeMselCount = 0;
                        pst_parm->em_GradualLampState = GRADUAL_LAMP_STATE_FADING;
                    }
                    else
                    {
                        pst_parm->u16_TimeMselCount++;

                        if(pst_parm->u16_TimeMselCount >= U16_BREATH_LAMP_KEEP_LOW_BRIGHT_TIME_MSELS)
                        {
                            pst_parm->u16_TimeMselCount = 0;
                            pst_parm->em_GradualLampState = GRADUAL_LAMP_STATE_BRIGHTENING;
                        }
                    }
                    break;
                default:
                    if(0 == u16_duty_value)
                    {
                        pst_parm->em_GradualLampState = GRADUAL_LAMP_STATE_STOP;
                    }
                    else if(true == pst_parm->f_LampState)
                    {
                        pst_parm->em_GradualLampState = GRADUAL_LAMP_STATE_BRIGHTENING;
                    }
                    else
                    {
                        pst_parm->em_GradualLampState = GRADUAL_LAMP_STATE_FADING;
                    }
                    break;
            }
        }

        if(pst_parm->u16_PWMDutyValue != u16_duty_value)
        {
            pst_parm->u16_PWMDutyValue = u16_duty_value;

            if(U16_GRADUAL_LAMP_CYCLE_MAX_VALUE == pst_config_parm->u16_PWMCycleValue)
            {
                pst_parm->u16_PWMDutyRealValue = u16_duty_value;
            }
            else
            {
                pst_parm->u16_PWMDutyRealValue = (uint16_t)((uint32_t)u16_duty_value *
                    (uint32_t)pst_config_parm->u16_PWMCycleValue /
                    (uint32_t)U16_GRADUAL_LAMP_CYCLE_MAX_VALUE);
            }

            pst_config_parm->Set_LampPWMDutyValue(pst_parm->u16_PWMDutyRealValue);
        }
    }
}

static bool b_ToggleTest;

void Toggle_LampTest(void)
{
    if(false == b_ToggleTest)
    {
        b_ToggleTest = true;
        Set_GradualLampState(REF_SURFACE_LAMP, true);
    }
    else
    {
        b_ToggleTest = false;
        Set_GradualLampState(REF_SURFACE_LAMP, false);
    }
}
