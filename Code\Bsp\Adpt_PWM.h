/*!
 * @file
 * @brief This file defines public constants, types and functions for the pwm adapter.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef _Adpt_PWM_H_
#define _Adpt_PWM_H_

#include <stdint.h>
#include "adt.h"
#include "pca.h"
#include "bt.h"
#include "gpio.h"
#include "timer3.h"

void TIM2_CHA_SetCmp(uint16_t compareValue);    //面光源
void TIM3_CH2A_SetCmp(uint16_t compareValue);   //ID0
void TIM4_CHB_SetCmp(uint16_t compareValue);   //ID1
void TIM6_CHB_SetCmp(uint16_t compareValue);   //ID2

#define TIME2_COMPARE_VALUE_STEP_MIN (50)
#define TIME3_COMPARE_VALUE_STEP_MIN (TIME3_COMPARE_VALUE * 3 / 100)
#define TIME3_COMPARE_VALUE_STEP_MAX (TIME3_COMPARE_VALUE * 97 / 100)
#define TIME4_COMPARE_VALUE_STEP_MIN (TIME4_COMPARE_VALUE * 3 / 100)
#define TIME4_COMPARE_VALUE_STEP_MAX (TIME4_COMPARE_VALUE * 97 / 100)
#define TIME6_COMPARE_VALUE_STEP_MIN (TIME6_COMPARE_VALUE * 3 / 100)
#define TIME6_COMPARE_VALUE_STEP_MAX (TIME6_COMPARE_VALUE * 97 / 100)

#define TIME3_PERIOD_VALUE (uint16_t)(SystemCoreClock / 25000 - 1) // period
#define TIME3_COMPARE_VALUE (TIME3_PERIOD_VALUE + 1) // off

#define TIME4_PERIOD_VALUE ((uint16_t)(SystemCoreClock / 25000 - 1)) // period
#define TIME4_COMPARE_VALUE (TIME4_PERIOD_VALUE + 1)

#define TIME6_PERIOD_VALUE ((uint16_t)(SystemCoreClock / 25000 - 1)) // period
#define TIME6_COMPARE_VALUE (TIME6_PERIOD_VALUE + 1)//#define TIME6_COMPARE_VALUE (TIME4_PERIOD_VALUE + 1)

#define IO_REF_TOP_LAMP(CompareValue)  TIM2_CHA_SetCmp(CompareValue)
#define U16_GRADUAL_LAMP_REAL_CYCLE_VALUE ((uint16_t)(48000000 / 1250))

// #define SET_FAN_DUTY_ID0(CompareValue) Tim3_M23_CCR_Set(Tim3CCR2A, (TIME3_PERIOD_VALUE - CompareValue))
#define SET_FAN_DUTY_ID0(CompareValue) TIM3_CH2A_SetCmp(CompareValue)
#define SET_FAN_DUTY_ID1(CompareValue) TIM4_CHB_SetCmp(CompareValue)
#define SET_FAN_DUTY_ID2(CompareValue) TIM6_CHB_SetCmp(CompareValue)

void Board_InitTim(void);
#endif
