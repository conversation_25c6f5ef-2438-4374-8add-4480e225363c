/**
 ******************************************************************************
 * @file   vc.h
 *
 * @brief This file contains all the functions prototypes of the VC driver
 *        library.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-03-15       MDAS            First version
 @endverbatim
 ******************************************************************************
 * Copyright (C) 2023, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 ******************************************************************************
 */

#ifndef __VC_H__
#define __VC_H__

/******************************************************************************
 * Include files
 ******************************************************************************/
#include "ddl.h"

/* C binding of definitions if building with C++ compiler */
#ifdef __cplusplus
extern "C"
{
#endif

/**
 * @addtogroup HC32L186_DDL 驱动库
 * @{
 */

/**
 * @addtogroup DDL_VC VC模块驱动库
 * @{
 */

/******************************************************************************
 * Global type definitions
 ******************************************************************************/
/**
 * @defgroup VC_Global_Types VC全局类型定义
 * @{
 */

/**
 * @brief  VC通道
 */
typedef enum
{
    VcChannel0 = 0u,                /*!< 通道0 */
    VcChannel1 = 1u,                /*!< 通道1 */
    VcChannel2 = 2u                 /*!< 通道2 */
} en_vc_channel_t;


/**
 * @brief  VC迟滞 VC_CR  VCx_HYS_SEL(x=0、1、2)
 */
typedef enum
{
    VcDelayoff  = 0u,            /*!<   迟滞关闭 */
    VcDelay10mv = 1u,            /*!<   迟滞10mv */
    VcDelay20mv = 2u,            /*!<   迟滞20mv */
    VcDelay30mv = 3u,            /*!<   迟滞30mv */
} en_vc_cmp_delay_t;

/**
 * @brief  VC偏置电流 VC_CR  VCx_BIAS_SEL(x=0、1、2)
 */
typedef enum
{
    VcBias300na  = 0u,          /*!< 偏置电流300nA */
    VcBias1200na = 1u,          /*!< 偏置电流1.2uA */
    VcBias10ua   = 2u,          /*!< 偏置电流10uA */
    VcBias20ua   = 3u,          /*!< 偏置电流20uA */
} en_vc_bias_current_t;


/**
 * @brief  VC输出滤波时间  VCx_CR  debounce_time(x=0、1、2)
 */
typedef enum
{
    VcFilter7us     = 0u,           /*!< 输出滤波时间7us */
    VcFilter14us    = 1u,           /*!< 输出滤波时间14us */
    VcFilter28us    = 2u,           /*!< 输出滤波时间28us */
    VcFilter112us   = 3u,           /*!< 输出滤波时间112us */
    VcFilter450us   = 4u,           /*!< 输出滤波时间450us */
    VcFilter1800us  = 5u,           /*!< 输出滤波时间1.8ms */
    VcFilter7200us  = 6u,           /*!< 输出滤波时间7.2ms */
    VcFilter28800us = 7u,           /*!< 输出滤波时间28.8ms */
} en_vc_resp_filter_t;


/**
 * @brief  VC P端输入
 */
typedef enum
{
    VcInPCh0 = 0u,                /*!< VC0输入通道0 PC0     VC1输入通道0 PA0     VC2输入通道0 PA5   */
    VcInPCh1 = 1u,                /*!< VC0输入通道1 PC1     VC1输入通道1 PA1     VC2输入通道1 PB1   */
    VcInPCh2 = 2u,                /*!< VC0输入通道2 PC2     VC1输入通道2 PA2     VC2输入通道2 PE9   */
    VcInPCh3 = 3u,                /*!< VC0输入通道3 PC3     VC1输入通道3 PA3     VC2输入通道3 PE10  */
    VcInPCh4 = 4u,                /*!< VC0输入通道4 PA0     VC1输入通道4 PA4     VC2输入通道4 PE11  */
    VcInPCh5 = 5u,                /*!< VC0输入通道5 PA1     VC1输入通道5 PA5     VC2输入通道5 PE13  */
    VcInPCh6 = 6u,                /*!< VC0输入通道6 PA2     VC1输入通道6 PB1     VC2输入通道6 PE14  */
    VcInPCh7 = 7u,                /*!< VC0输入通道7 PA3     VC1输入通道7 PB2     VC2输入通道7 PE15  */
    VcInPCh8 = 8u,                /*!< VC0输入通道8 PA4     VC1输入通道8 PB10    VC2输入通道8 PB11  */
    VcInPCh9 = 9u,                /*!< VC0输入通道9 PA5     VC1输入通道9 PB12    VC2输入通道9 PB14  */
    VcInPCh10 = 10u,              /*!< VC0输入通道10 PA6    VC1输入通道10 PB13   VC2输入通道10 PD9  */
    VcInPCh11 = 11u,              /*!< VC0输入通道11 PA7    VC1输入通道11 PB14   VC2输入通道11 PD10 */
    VcInPCh12 = 12u,              /*!< VC0输入通道12 PB4    VC1输入通道12 PB4    VC2输入通道12 PD11 */
    VcInPCh13 = 13u,              /*!< VC0输入通道13 PB5    VC1输入通道13 DAC    VC2输入通道13 PC7  */
    VcInPCh14 = 14u,              /*!< VC0输入通道14 PB6    VC1输入通道14 PB6    VC2输入通道14 DAC  */
    VcInPCh15 = 15u,              /*!< VC0输入通道15 DAC    VC1输入通道15 PB7    VC2输入通道15 DAC  */
} en_vc_input_p_src_t;

/**
 * @brief  VC N端输入
 */
typedef enum
{
    VcInNCh0    = 0u,           /*!< VC0输入通道0  PA0          VC1输入通道0 PC0                VC2输入通道0 PA5            */
    VcInNCh1    = 1u,           /*!< VC0输入通道1  PA1          VC1输入通道1 PC1                VC2输入通道1 PB1            */
    VcInNCh2    = 2u,           /*!< VC0输入通道2  PA2          VC1输入通道2 PC2                VC2输入通道2 PE11           */
    VcInNCh3    = 3u,           /*!< VC0输入通道3  PA3          VC1输入通道3 PC3                VC2输入通道3 PE15           */
    VcInNCh4    = 4u,           /*!< VC0输入通道4  PA4          VC1输入通道4 PA0                VC2输入通道4 PB11           */
    VcInNCh5    = 5u,           /*!< VC0输入通道5  PA5          VC1输入通道5 PA1                VC2输入通道5 PB14           */
    VcInNCh6    = 6u,           /*!< VC0输入通道6  PA6          VC1输入通道6 PB0                VC2输入通道6 PD10           */
    VcInNCh7    = 7u,           /*!< VC0输入通道7  PA7          VC1输入通道7 PB1                VC2输入通道7 PD11           */
    VcInNCh8    = 8u,           /*!< VC0输入通道8  PC4          VC1输入通道8 PB2                VC2输入通道8 PC7            */
    VcInNCh9    = 9u,           /*!< VC0输入通道9  PC5          VC1输入通道9 PB3                VC2输入通道9 DAC            */
    VcInNCh10   = 10u,          /*!< VC0输入通道10 DAC          VC1输入通道10 DAC               VC2输入通道10 DAC           */
    ResDivOut   = 11u,          /*!< VC0电阻分压                VC1电阻分压                     VC2电阻分压                 */
    AiTs        = 12u,          /*!< VC0 内部温度传感器输出电压  VC1 内部温度传感器输出电压     VC2 内部温度传感器输出电压  */
    AiAdcVref   = 14u,          /*!< VC0 ADC参考电压VREF        VC1 ADC参考电压VREF             VC2 ADC参考电压VREF         */
    AiLdo       = 15u,          /*!< VC0 LDO输出电压            VC1 LDO输出电压                 VC2 LDO输出电压             */
} en_vc_input_n_src_t;

/**
 * @brief  VC中断触发方式
 */
typedef enum
{
    VcIrqNone = 0u,            /*!< 无中断 */
    VcIrqRise = 1u,            /*!< 上升沿触发 */
    VcIrqFall = 2u,            /*!< 下降沿触发 */
    VcIrqHigh = 3u,            /*!< 高电平触发 */

} en_vc_irq_sel_t;

/**
 * @brief  VC状态
 */
typedef enum
{
    Vc0_Intf    = 0u,          /*!< VC0中断标志 */
    Vc1_Intf    = 1u,          /*!< VC1中断标志 */
    Vc0_Filter  = 2u,          /*!< VC0 Filter 后的状态 */
    Vc1_Filter  = 3u,          /*!< VC1 Filter 后的状态 */
    Vc2_Intf    = 4u,          /*!< VC2中断标志 */
    Vc2_Filter  = 5u           /*!< VC2 Filter 后的状态 */
} en_vc_ifr_t;

/**
 * @brief  VC输出配置 VCx_OUT_CFG(x=0、1、2)
 * @note  对于VC0，CHX = CHA；对于VC1 VC2，CHX = CHB
 */
typedef enum
{
    VcOutInvTimer = 0u,              /*!< 结果输出反向到各Timer0,1,2,3 REFCLR */
    VcOutTIM0RCLR = 1u,              /*!< 结果输出到TIM0 REFCLR使能控制 */
    VcOutTIM1RCLR = 2u,              /*!< 结果输出到TIM1 REFCLR使能控制 */
    VcOutTIM2RCLR = 3u,              /*!< 结果输出到TIM2 REFCLR使能控制 */
    VcOutTIM3RCLR = 4u,              /*!< 结果输出到TIM3 REFCLR使能控制 */
    VcOutTIMBK    = 5u,              /*!< 结果输出到Timer0,1,2,3刹车控制 */
    VcOutInvTIM4  = 9u,              /*!< 结果输出到Timer4反向使能 */
    VcOutTIM4     = 10u,             /*!< 结果输出到Timer4捕获输入CHX使能 */
    VcOutInvTIM5  = 11u,             /*!< 结果输出到Timer5反向使能 */
    VcOutTIM5     = 12u,             /*!< 结果输出到Timer5捕获输入CHX使能 */
    VcOutInvTIM6  = 13u,             /*!< 结果输出到Timer6反向使能 */
    VcOutTIM6     = 14u,             /*!< 结果输出到Timer6捕获输入CHX使能 */
    VcOutBrake    = 15u,             /*!< 结果作为Advanced Timer刹车控制 */
    VcOutDisable  = 16u              /*!< 结果输出除能 */
} en_vc_output_cfg_t;

/**
 * @brief  VC DIV参考电压Vref选择 VC_CR   VC_REF2P5_SEL
 */
typedef enum
{
    VcDivVrefAvcc = 0u,             /*!< AVCC */
    VcDivVrefAdc  = 1u,             /*!< ADC_CR0 SREF选择参考电压 */
} en_vc_div_vref_t;

/**
 * @brief  VC DIV配置
 */
typedef struct
{
    boolean_t           bDivEn;       /*!< VC_CR: VC_DIV_EN */
    uint8_t             u8DivVal;     /*!< VC_CR: VC_DIV 范围：0-63 */
    en_vc_div_vref_t    enDivVref;    /*!< VC_CR: VC_REF2P5_SEL @ref en_vc_div_vref_t */
} stc_vc_div_cfg_t;

/**
 * @brief  VC通道配置
 */
typedef struct
{
    en_vc_channel_t         enVcChannel;        /*!< VC通道选择 @ref en_vc_channel_t */
    en_vc_cmp_delay_t       enVcCmpDly;         /*!< VC迟滞 @ref en_vc_cmp_delay_t */
    en_vc_bias_current_t    enVcBiasCurrent;    /*!< VC功耗选择 @ref en_vc_bias_current_t */
    en_vc_resp_filter_t     enVcFilterTime;     /*!< 输出滤波时间 @ref en_vc_resp_filter_t */
    en_vc_input_p_src_t     enVcInPin_P;        /*!< P端输入 @ref en_vc_input_p_src_t */
    en_vc_input_n_src_t     enVcInPin_N;        /*!< N端输入 @ref en_vc_input_n_src_t */
    en_vc_output_cfg_t      enVcOutCfg;         /*!< 输出配置 @ref en_vc_output_cfg_t */
    boolean_t               bFlten;             /*!< 滤波输出使能 */
} stc_vc_channel_cfg_t;
/**
 * @}
 */
/******************************************************************************
 * Global definitions
 ******************************************************************************/

/******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/******************************************************************************
 * Global variable definitions ('extern')
 ******************************************************************************/

/******************************************************************************
 * Global function prototypes (definition in C source)
 ******************************************************************************/
/**
 * @addtogroup VC_Global_Functions VC全局函数定义
 * @{
 */
void Vc_CfgItType(en_vc_channel_t Channelx, en_vc_irq_sel_t ItType);
void  Vc_ItCfg(en_vc_channel_t Channelx, boolean_t NewStatus);
boolean_t Vc_GetItStatus(en_vc_ifr_t Result);
void Vc_ClearItStatus(en_vc_ifr_t VcxIntf);
en_result_t Vc_DivInit(stc_vc_div_cfg_t *pstcDacCfg);
void Vc_Init(stc_vc_channel_cfg_t *pstcChannelCfg);
void Vc_Cmd(en_vc_channel_t enChannel, boolean_t NewStatus);
/**
 * @}
 */


/**
 * @}
 */

/**
 * @}
 */
#ifdef __cplusplus
}
#endif

#endif /* __VC_H__ */
/******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/

