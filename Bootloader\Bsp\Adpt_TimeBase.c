/*!
 * @file
 * @brief Configures the SysTick.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "sysctrl.h"
#include "gpio.h"
#include "Timebase.h"

/**
 * @brief  This function handles SysTick Handler.
 */
void SysTick_IRQHandler(void)
{
    __disable_irq();
    ISR_Timer_1ms();
    __enable_irq();
}

void Board_InitSysTick(void)
{
    /* Setup SysTick Timer for 1 msec interrupts  */
    /* 内核函数，SysTick配置，定时1ms，系统时钟默认RCH 4MHz */
    if(SysTick_Config((SystemCoreClock) / 1000))
    {
        /* Capture error */
        while(1)
            ;
    }

}



void Board_DeinitSysTick(void)
{
    EnableNvic(SysTick_IRQn, IrqLevel3, FALSE);
}