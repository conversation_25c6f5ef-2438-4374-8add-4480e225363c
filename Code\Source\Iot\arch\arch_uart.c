//#include "cpu\inc\xmi_uart.h"

#include "arch_define.h"
//#include <stdint.h>
#include "arch_uart.h"
#include "string.h"
#include "Adpt_Usart.h"
//#include "MCUResgiterDefine.h"
#include "IotUsr.h"
#if 0

#define CPU_FREQ            (32000000UL)
#define COMM_BAUNDRATE      115200
#define COMM_RX_BUFF_SIZE   528 /* 512+16 */
#define COMM_TX_BUFF_SIZE   528 /* 512+16 */


typedef struct comm_uart
{
    uint16_t rx_write_idx;
    uint16_t rx_read_idx;
    uint16_t rx_len;
    uint8_t rx_queue[COMM_RX_BUFF_SIZE];
    uint8_t tx_write_idx;
    uint8_t tx_read_idx;
    uint16_t tx_len;
    uint8_t tx_queue[COMM_TX_BUFF_SIZE];
}comm_uart_t;


static comm_uart_t comm_uart_data; 
static int comm_tx_avaliable = 0;


// static void uart_enable_int(UART_COMM_T uart, bool enable)
// {
//     switch( uart )
//     {
//         case UART_MIIO_COMM:

//             if( enable )
//             {
//                 USCI2_ITConfig(ENABLE,LOW);
//             }
//             else{
//                 USCI2_ITConfig(DISABLE,LOW);
//             }
//             break;
//         default:
//             break;
//     }
// }

static int uart_write_txbuf(UART_COMM_T uart, uint8_t u8data)
{
    int data_len = 0;
    
       USART_ConfigInt(USART_WIFI, USART_INT_TXDE, DISABLE);//uart_enable_int(uart, DISABLE);
    if( comm_uart_data.tx_len < COMM_TX_BUFF_SIZE)
    {
        if( comm_tx_avaliable == 0 )
        {
            comm_tx_avaliable = 1;
            //USCI2_UART_SendData8(u8data);
            USART_SendData(USART_WIFI, u8data);
        }
        else{
            comm_uart_data.tx_queue[comm_uart_data.tx_write_idx]=u8data;
            comm_uart_data.tx_write_idx = (comm_uart_data.tx_write_idx+1)%COMM_TX_BUFF_SIZE;
            comm_uart_data.tx_len++;
        }
        data_len = 1;
    }
    USART_ConfigInt(USART_WIFI, USART_INT_TXDE, ENABLE);//uart_enable_int(uart, ENABLE);
    return data_len;
}

static int uart_read_txbuf(UART_COMM_T uart, uint8_t *p_u8data)
{
    int data_len = 0;
    
    if( UART_MIIO_COMM != uart )
        return 0;
    if( comm_uart_data.tx_len != 0)
    { 
        *p_u8data = comm_uart_data.tx_queue[comm_uart_data.tx_read_idx];
        comm_uart_data.tx_read_idx = (comm_uart_data.tx_read_idx+1)%COMM_TX_BUFF_SIZE;
        comm_uart_data.tx_len--;
        data_len = 1;

    }
    return data_len;
}

static int uart_write_rxbuf(UART_COMM_T uart, uint8_t u8data)
{
    int data_len = 0;
    //log_printf(0, "%c", u8data);
    if( UART_MIIO_COMM != uart )
        return 0;
    
    if( comm_uart_data.rx_len < COMM_RX_BUFF_SIZE )
    {
        comm_uart_data.rx_queue[comm_uart_data.rx_write_idx]=u8data;
        comm_uart_data.rx_write_idx = (comm_uart_data.rx_write_idx+1)%COMM_RX_BUFF_SIZE;
        comm_uart_data.rx_len++;
       data_len = 1;
    }
    return data_len;
}

static int uart_read_rxbuf(UART_COMM_T uart, uint8_t *p_u8data)
{
    int data_len = 0;

    if( UART_MIIO_COMM != uart )
        return 0;
    //log_printf(0, "wifi uart_read_rxbuf  : %d",comm_uart_data.rx_len);
    //USART_ConfigInt(USART_WIFI, USART_INT_RXDNE, DISABLE);//uart_enable_int(uart, DISABLE);
    if( comm_uart_data.rx_len != 0)
    {
        *p_u8data = comm_uart_data.rx_queue[comm_uart_data.rx_read_idx];
        comm_uart_data.rx_read_idx = (comm_uart_data.rx_read_idx+1)%COMM_RX_BUFF_SIZE;
        comm_uart_data.rx_len--;
        data_len = 1;
        //log_printf(0, "%c", *p_u8data);
    }
   //USART_ConfigInt(USART_WIFI, USART_INT_RXDNE, ENABLE);//uart_enable_int(uart, ENABLE);
    
    return data_len;
}


// void USCI2Interrupt()       interrupt 16    
// {
//     if(USCI2_GetFlagStatus(USCI2_UART_FLAG_TI))
//     {
//         uint8_t tx_data;
//         USCI2_ClearFlag(USCI2_UART_FLAG_TI);
//         comm_tx_avaliable = uart_read_txbuf(UART_MIIO_COMM, &tx_data);
//         if( comm_tx_avaliable )
//             USCI2_UART_SendData8(tx_data);
//     }
//     if(USCI2_GetFlagStatus(USCI2_UART_FLAG_RI))
//     {
//         USCI2_ClearFlag(USCI2_UART_FLAG_RI);
//         uart_write_rxbuf(UART_MIIO_COMM, USCI2_UART_ReceiveData8());
//     }

// }

/**
 * @brief  This function handles USARTz global interrupt request.
 */
void USART_WIFI_IRQHandler(void)
{

    if (USART_GetIntStatus(USART_WIFI, USART_INT_RXDNE) != RESET)
    {
        //USART_ConfigInt(USART_WIFI, USART_INT_RXDNE, DISABLE);
        uart_write_rxbuf(UART_MIIO_COMM, USART_ReceiveData(USART_WIFI));
    }

    if (USART_GetIntStatus(USART_WIFI, USART_INT_TXDE) != RESET)
    {
         uint8_t tx_data;
        USART_ConfigInt(USART_WIFI, USART_INT_TXDE, DISABLE);
        comm_tx_avaliable = uart_read_txbuf(UART_MIIO_COMM, &tx_data);
        if( comm_tx_avaliable )
            USART_SendData(USART_WIFI,tx_data);
    }
		
    // if(USART_GetFlagStatus(USART_WIFI, USART_FLAG_IDLEF | USART_FLAG_OREF | USART_FLAG_NEF | USART_FLAG_FEF | USART_FLAG_PEF) != RESET)
    // {
    //     USART_ReceiveData(USART_WIFI);		
    // }
}

static void SC_USCI2_Init(void)
{
    comm_tx_avaliable = 0;
    memset(&comm_uart_data, 0, sizeof(comm_uart_t));
    // GPIO_Init(GPIO4, GPIO_PIN_5,GPIO_MODE_IN_PU);
    // GPIO_Init(GPIO4, GPIO_PIN_4,GPIO_MODE_IN_PU);
    // USCI2_ITConfig(ENABLE,LOW);
    // USCI2_UART_Init(CPU_FREQ,COMM_BAUNDRATE,USCI2_UART_Mode_10B,USCI2_UART_RX_ENABLE);
}
#endif
typedef enum{
    MD_OK,
    MD_BUSY1
}MD_STATUS;

#define USART_CH2     2
#define USART2_QUEUE_SIZE 528
MD_STATUS g_UartA1TxEnd = MD_OK; /* UARTA1 transmission end */
TByte g_uart2_tx_queue[USART2_QUEUE_SIZE] = {0};
TWord g_uart2_tx_front = 0;
TWord g_uart2_tx_rear = 0;
TByte g_uart2_rx_queue[USART2_QUEUE_SIZE] = {0};
TWord g_uart2_rx_front = 0; 
TWord g_uart2_rx_rear = 0; 
TBool g_uart2_recv_done = FALSE;


TBool uart_rx_queue_is_empty(TByte ch)
{
    if(ch == USART_CH2)
    {
	    return ( g_uart2_rx_rear == g_uart2_rx_front );
    }
    return FALSE;
}

TBool uart_rx_queue_is_full(TByte ch)
{
    if(ch == USART_CH2)
	{
		return ( (g_uart2_rx_rear + 1 ) % USART2_QUEUE_SIZE == g_uart2_rx_front );
	}

	return FALSE;
}
TBool uart_tx_queue_is_empty(TByte ch)
{
    if(ch == USART_CH2)
	{
		return ( g_uart2_tx_rear == g_uart2_tx_front );
	}

	return FALSE;
}
TBool uart_tx_queue_is_full(TByte ch)
{
    if(ch == USART_CH2)
	{
		return ( (g_uart2_tx_rear + 1 ) % USART2_QUEUE_SIZE == g_uart2_tx_front );
	}
	return FALSE;
}

void USART_WIFI_IRQHandler(void)
{
    if(Uart_GetStatus(USART_WIFI, UartFE))
    {
        Uart_ClrStatus(USART_WIFI, UartFE);
    }

    if(Uart_GetStatus(USART_WIFI, UartPE))
    {
        Uart_ClrStatus(USART_WIFI, UartPE);
    }

    if(Uart_GetStatus(USART_WIFI, UartRC))
    {
        Uart_ClrStatus(USART_WIFI, UartRC);

       if (uart_rx_queue_is_full(USART_CH2))
       {
           g_uart2_rx_queue [g_uart2_rx_rear++] = Uart_ReceiveData(USART_WIFI);
           g_uart2_rx_rear %= USART2_QUEUE_SIZE;
           g_uart2_rx_front = g_uart2_rx_rear = 0;
           g_uart2_rx_queue[g_uart2_rx_front] = 0;
       }
       else
       {
           g_uart2_rx_queue [g_uart2_rx_rear++] = Uart_ReceiveData(USART_WIFI);
           g_uart2_rx_rear %= USART2_QUEUE_SIZE;
       }
    }

    if(Uart_GetStatus(USART_WIFI, UartTC))
    {
        Uart_ClrStatus(USART_WIFI, UartTC);

        if (uart_tx_queue_is_empty(USART_CH2) == TRUE)
        {
            g_UartA1TxEnd = MD_OK;
            //USART_ConfigInt(USART_WIFI, USART_INT_TXDE, DISABLE);
        }
        else
        {
            g_UartA1TxEnd = MD_BUSY1;
            Uart_SendDataIt(USART_WIFI, g_uart2_tx_queue[g_uart2_tx_front++]);
            g_uart2_tx_front %= USART2_QUEUE_SIZE;
            //APP_LOG("%c",TXBA1);
        }
    }
}

void Wifi_UARTA1_Send_Byte(TByte byte)
{
	//g_Sending_cmd[USART_CH2].timeout = BUS_TIMEOUT_TIME;

	if (uart_tx_queue_is_empty(USART_CH2) == FALSE)
	{
        /* Disable UARTA1 interrupt operation */
        //USART_ConfigInt(USART_WIFI, USART_INT_TXDE, DISABLE);
        Uart_SendDataIt(USART_WIFI,byte);
        g_UartA1TxEnd = MD_BUSY1;//zhl add
        /* Enable UARTA1 interrupt operation */
        //USART_ConfigInt(USART_WIFI, USART_INT_TXDE, ENABLE);
	}
	//APP_LOG("%c",byte);
}

TBool Wifi_Uart_Get_Send_Idel_Flag(void)
{
   if (g_UartA1TxEnd == MD_OK)
    {
        return TRUE;
    }
   return FALSE;
}

TByte uart_send_data_with_interrupt(TByte ch,TByte *send_buffer,TByte length)
{
	TByte i;
	if (ch == USART_CH2)
	{
		for (i = 0; i < length; i++)
		{
			g_uart2_tx_queue[g_uart2_tx_rear++] = *(send_buffer+i);
			g_uart2_tx_rear %= USART2_QUEUE_SIZE;
		}
		//if ((Wifi_Uart_Get_Send_Idel_Flag()== TRUE)&&(uart_tx_queue_is_full(ch) == FALSE)) //zhl
		if (Wifi_Uart_Get_Send_Idel_Flag()== TRUE)
		{
			Wifi_UARTA1_Send_Byte(g_uart2_tx_queue[g_uart2_tx_front]);
			g_uart2_tx_front++; //zhl
			g_uart2_tx_front %= USART2_QUEUE_SIZE;
			return length;
		}
		return length; //zhl
	}

	return 0;
}

TBool uart_receive_data_with_interrupt(TByte ch, TByte *recv_buf, TByte recv_len)
{
	if(recv_buf == NULL || recv_len == 0)
	{
		return FALSE;
	}
    //USART_ConfigInt(USART_WIFI, USART_INT_RXDNE, ENABLE);
	if(uart_rx_queue_is_empty(ch) == TRUE)
	{
		return FALSE;
	}
	else
	{
		if(ch == USART_CH2)
		{
			*recv_buf = g_uart2_rx_queue[g_uart2_rx_front++];
			g_uart2_rx_front %= USART2_QUEUE_SIZE;
        }
	}

	return TRUE;
}

int uart_init(UART_COMM_T uart)
{
#if 0
    /* only support UART_MIIO_COMM now */
    if( UART_MIIO_COMM != uart )
        return -1;
#endif
    
    //SC_USCI2_Init();
   // enableInterrupts();
    return 0;
}

int uart_send_byte(UART_COMM_T uart, const unsigned char u8data)
{

    return (int)uart_send_data_with_interrupt(USART_CH2, (TByte *)&u8data, 1);
    //return uart_write_txbuf(uart, u8data);

     //uint16_t i = 0;
    /* Write one byte to the transmit data register */
    // USART_SendData(USART_WIFI, u8data);

    // /* Loop until USARTz DAT register is empty */
    // while (USART_GetFlagStatus(USART_WIFI, USART_FLAG_TXDE) == RESET)
    // {
    //     if(i > 5000)
    //     {
    //         break;
    //     }
    //     i++;
    // }
	// return 1;
}

int uart_recv_byte(UART_COMM_T uart, unsigned char *p_u8data)
{
     //return uart_read_rxbuf(uart, p_u8data);
    
	if( uart_receive_data_with_interrupt(USART_CH2, (TByte *)p_u8data, 1) == TRUE )
		return 1;
	return 0;

//     uint16_t i = 0;
//    /* Store the received byte in RxBuffer */
//     while (USART_GetFlagStatus(USART_WIFI, USART_FLAG_RXDNE) == RESET)
//     {
//         if(i > 9000)
//         {
//             break;
//         }
//         i++;
//     }
//     /* Store the received byte in RxBuffer */
//     *p_u8data = (unsigned char)USART_ReceiveData(USART_WIFI);
//     return 1;


}


int uart_recv_buffer_is_empty(void)
{
	// if (USART_GetFlagStatus(USART_WIFI, USART_FLAG_RXDNE) == RESET)
	// {
	// 	  return 1;
	// }
	
	// return 0;
    return uart_rx_queue_is_empty(USART_CH2);
}

void APP_LOG(char *pbuf, ...)
{
    //log_printf(0, "%c\n", pbuf);
}

void APP_LOG_IOT(char *pbuf, ...)
{
    //log_printf(0, "%c\n", pbuf);
}


