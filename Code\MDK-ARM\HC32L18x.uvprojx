<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>MainBoard</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6160000::V6.16::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>HC32L186KATH</Device>
          <Vendor>HDSC</Vendor>
          <PackID>HDSC.HC32L18x.1.0.0</PackID>
          <PackURL>https://raw.githubusercontent.com/hdscmcu/pack/master/</PackURL>
          <Cpu>IRAM(0x20000000,0x8000) IROM(0x00000000,0x20000) CPUTYPE("Cortex-M0+") CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0HC32L18x_128KB -FS00 -FL020000 -FP0($$Device:HC32L186KATH$Flash\HC32L18x_128KB.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:HC32L186KATH$Device\Include\HC32L186.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:HC32L186KATH$SVD\HC32L18x_LQFP80_LQFP64.sfr</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\output\release\</OutputDirectory>
          <OutputName>HC32L18x</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\output\release\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>.\param_build.bat</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>1</RunUserProg2>
            <UserProg1Name>.\output\release\Keil5_disp_size_bar.exe</UserProg1Name>
            <UserProg2Name>C:\Keil_v5\ARM\ARMCC\bin\fromelf.exe --bin -o .\output\release\HC32L18x.bin   .\output\release\HC32L18x.axf</UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>  </SimDllArguments>
          <SimDlgDll>DARMCM1.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM0+</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> </TargetDllArguments>
          <TargetDlgDll>TARMCM1.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM0+</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M0+"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x8000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x20000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x6000</StartAddress>
                <Size>0x30e00</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x6000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>2</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>4</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>5</v6Lang>
            <v6LangP>0</v6LangP>
            <vShortEn>0</vShortEn>
            <vShortWch>0</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls> </MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath>..\Bsp;..\Device;..\Hardware;..\PeriphDrivers;..\Source\Application;..\Source\Core;..\Source\CoreUser;..\Source\Module;..\Source;..\Startup;..\Device;..\Debug;..\Debug\cm_backtrace;..\Source\Iot;..\Source\Iot\arch;..\Source\Iot\miio;..\Source\Iot\spec;..\Source\Iot\user;..\Source\Iot\miio\device\codec;..\Source\Iot\miio\uart;..\Source\Iot\miio\util;..\Source\Iot\user\app;..\Source\RTT;..\Parameter</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>HC32L18x.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc>--keep=*Handler</Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Bsp</GroupName>
          <Files>
            <File>
              <FileName>Adpt_ADC.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Bsp\Adpt_ADC.c</FilePath>
            </File>
            <File>
              <FileName>Adpt_Clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Bsp\Adpt_Clock.c</FilePath>
            </File>
            <File>
              <FileName>Adpt_GPIO.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Bsp\Adpt_GPIO.c</FilePath>
            </File>
            <File>
              <FileName>Adpt_Iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Bsp\Adpt_Iwdg.c</FilePath>
            </File>
            <File>
              <FileName>Adpt_PWM.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Bsp\Adpt_PWM.c</FilePath>
            </File>
            <File>
              <FileName>Adpt_TimeBase.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Bsp\Adpt_TimeBase.c</FilePath>
            </File>
            <File>
              <FileName>Adpt_Usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Bsp\Adpt_Usart.c</FilePath>
            </File>
            <File>
              <FileName>Adpt_ADC.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Bsp\Adpt_ADC.h</FilePath>
            </File>
            <File>
              <FileName>Adpt_Clock.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Bsp\Adpt_Clock.h</FilePath>
            </File>
            <File>
              <FileName>Adpt_GPIO.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Bsp\Adpt_GPIO.h</FilePath>
            </File>
            <File>
              <FileName>Adpt_Iwdg.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Bsp\Adpt_Iwdg.h</FilePath>
            </File>
            <File>
              <FileName>Adpt_PWM.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Bsp\Adpt_PWM.h</FilePath>
            </File>
            <File>
              <FileName>Adpt_TimeBase.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Bsp\Adpt_TimeBase.h</FilePath>
            </File>
            <File>
              <FileName>Adpt_Usart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Bsp\Adpt_Usart.h</FilePath>
            </File>
            <File>
              <FileName>Adpt_Reset.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Bsp\Adpt_Reset.c</FilePath>
            </File>
            <File>
              <FileName>Adpt_Reset.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Bsp\Adpt_Reset.h</FilePath>
            </File>
            <File>
              <FileName>Adpt_Flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Bsp\Adpt_Flash.c</FilePath>
            </File>
            <File>
              <FileName>Adpt_Flash.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Bsp\Adpt_Flash.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Device</GroupName>
          <Files>
            <File>
              <FileName>base_types.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Device\base_types.h</FilePath>
            </File>
            <File>
              <FileName>board_stkhc32l186.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Device\board_stkhc32l186.h</FilePath>
            </File>
            <File>
              <FileName>HC32L186.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Device\HC32L186.h</FilePath>
            </File>
            <File>
              <FileName>RTE_Components.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Device\RTE_Components.h</FilePath>
            </File>
            <File>
              <FileName>system_hc32l186.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Device\system_hc32l186.c</FilePath>
            </File>
            <File>
              <FileName>system_hc32l186.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Device\system_hc32l186.h</FilePath>
            </File>
            <File>
              <FileName>interrupts_hc32l186.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Device\interrupts_hc32l186.c</FilePath>
            </File>
            <File>
              <FileName>interrupts_hc32l186.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Device\interrupts_hc32l186.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Hardware</GroupName>
          <Files>
            <File>
              <FileName>Init_Mcu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Hardware\Init_Mcu.c</FilePath>
            </File>
            <File>
              <FileName>Init_Mcu.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Hardware\Init_Mcu.h</FilePath>
            </File>
            <File>
              <FileName>TestUsart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Hardware\TestUsart.c</FilePath>
            </File>
            <File>
              <FileName>TestUsart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Hardware\TestUsart.h</FilePath>
            </File>
            <File>
              <FileName>Timebase.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Hardware\Timebase.c</FilePath>
            </File>
            <File>
              <FileName>Timebase.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Hardware\Timebase.h</FilePath>
            </File>
            <File>
              <FileName>InverterUsart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Hardware\InverterUsart.c</FilePath>
            </File>
            <File>
              <FileName>InverterUsart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Hardware\InverterUsart.h</FilePath>
            </File>
            <File>
              <FileName>IO_Device.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Hardware\IO_Device.c</FilePath>
            </File>
            <File>
              <FileName>IO_Device.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Hardware\IO_Device.h</FilePath>
            </File>
            <File>
              <FileName>HeartbeatLed.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Hardware\HeartbeatLed.c</FilePath>
            </File>
            <File>
              <FileName>HeartbeatLed.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Hardware\HeartbeatLed.h</FilePath>
            </File>
            <File>
              <FileName>SbusUsart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Hardware\SbusUsart.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>PeriphDrivers</GroupName>
          <Files>
            <File>
              <FileName>adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\adc.c</FilePath>
            </File>
            <File>
              <FileName>adc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\adc.h</FilePath>
            </File>
            <File>
              <FileName>adt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\adt.c</FilePath>
            </File>
            <File>
              <FileName>adt.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\adt.h</FilePath>
            </File>
            <File>
              <FileName>aes.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\aes.c</FilePath>
            </File>
            <File>
              <FileName>aes.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\aes.h</FilePath>
            </File>
            <File>
              <FileName>bgr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\bgr.c</FilePath>
            </File>
            <File>
              <FileName>bgr.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\bgr.h</FilePath>
            </File>
            <File>
              <FileName>bt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\bt.c</FilePath>
            </File>
            <File>
              <FileName>bt.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\bt.h</FilePath>
            </File>
            <File>
              <FileName>crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\crc.c</FilePath>
            </File>
            <File>
              <FileName>crc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\crc.h</FilePath>
            </File>
            <File>
              <FileName>ctrim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\ctrim.c</FilePath>
            </File>
            <File>
              <FileName>ctrim.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\ctrim.h</FilePath>
            </File>
            <File>
              <FileName>dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\dac.c</FilePath>
            </File>
            <File>
              <FileName>dac.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\dac.h</FilePath>
            </File>
            <File>
              <FileName>ddl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\ddl.c</FilePath>
            </File>
            <File>
              <FileName>ddl.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\ddl.h</FilePath>
            </File>
            <File>
              <FileName>debug.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\debug.c</FilePath>
            </File>
            <File>
              <FileName>debug.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\debug.h</FilePath>
            </File>
            <File>
              <FileName>dmac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\dmac.c</FilePath>
            </File>
            <File>
              <FileName>dmac.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\dmac.h</FilePath>
            </File>
            <File>
              <FileName>flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\flash.c</FilePath>
            </File>
            <File>
              <FileName>flash.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\flash.h</FilePath>
            </File>
            <File>
              <FileName>gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\gpio.c</FilePath>
            </File>
            <File>
              <FileName>gpio.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\gpio.h</FilePath>
            </File>
            <File>
              <FileName>i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\i2c.c</FilePath>
            </File>
            <File>
              <FileName>i2c.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\i2c.h</FilePath>
            </File>
            <File>
              <FileName>lcd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\lcd.c</FilePath>
            </File>
            <File>
              <FileName>lcd.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\lcd.h</FilePath>
            </File>
            <File>
              <FileName>lpm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\lpm.c</FilePath>
            </File>
            <File>
              <FileName>lpm.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\lpm.h</FilePath>
            </File>
            <File>
              <FileName>lptim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\lptim.c</FilePath>
            </File>
            <File>
              <FileName>lptim.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\lptim.h</FilePath>
            </File>
            <File>
              <FileName>lpuart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\lpuart.c</FilePath>
            </File>
            <File>
              <FileName>lpuart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\lpuart.h</FilePath>
            </File>
            <File>
              <FileName>lvd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\lvd.c</FilePath>
            </File>
            <File>
              <FileName>lvd.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\lvd.h</FilePath>
            </File>
            <File>
              <FileName>opa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\opa.c</FilePath>
            </File>
            <File>
              <FileName>opa.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\opa.h</FilePath>
            </File>
            <File>
              <FileName>pca.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\pca.c</FilePath>
            </File>
            <File>
              <FileName>pca.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\pca.h</FilePath>
            </File>
            <File>
              <FileName>pcnt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\pcnt.c</FilePath>
            </File>
            <File>
              <FileName>pcnt.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\pcnt.h</FilePath>
            </File>
            <File>
              <FileName>ram.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\ram.c</FilePath>
            </File>
            <File>
              <FileName>ram.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\ram.h</FilePath>
            </File>
            <File>
              <FileName>reset.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\reset.c</FilePath>
            </File>
            <File>
              <FileName>reset.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\reset.h</FilePath>
            </File>
            <File>
              <FileName>rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\rtc.c</FilePath>
            </File>
            <File>
              <FileName>rtc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\rtc.h</FilePath>
            </File>
            <File>
              <FileName>spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\spi.c</FilePath>
            </File>
            <File>
              <FileName>spi.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\spi.h</FilePath>
            </File>
            <File>
              <FileName>sysctrl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\sysctrl.c</FilePath>
            </File>
            <File>
              <FileName>sysctrl.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\sysctrl.h</FilePath>
            </File>
            <File>
              <FileName>timer3.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\timer3.c</FilePath>
            </File>
            <File>
              <FileName>timer3.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\timer3.h</FilePath>
            </File>
            <File>
              <FileName>trim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\trim.c</FilePath>
            </File>
            <File>
              <FileName>trim.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\trim.h</FilePath>
            </File>
            <File>
              <FileName>trng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\trng.c</FilePath>
            </File>
            <File>
              <FileName>trng.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\trng.h</FilePath>
            </File>
            <File>
              <FileName>uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\uart.c</FilePath>
            </File>
            <File>
              <FileName>uart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\uart.h</FilePath>
            </File>
            <File>
              <FileName>vc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\vc.c</FilePath>
            </File>
            <File>
              <FileName>vc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\vc.h</FilePath>
            </File>
            <File>
              <FileName>wdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\wdt.c</FilePath>
            </File>
            <File>
              <FileName>wdt.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\wdt.h</FilePath>
            </File>
            <File>
              <FileName>wwdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\PeriphDrivers\wwdt.c</FilePath>
            </File>
            <File>
              <FileName>wwdt.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\PeriphDrivers\wwdt.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Source/Application</GroupName>
          <Files>
            <File>
              <FileName>CoolingCycle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\CoolingCycle.c</FilePath>
            </File>
            <File>
              <FileName>CoolingCycle.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Application\CoolingCycle.h</FilePath>
            </File>
            <File>
              <FileName>DataManager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\DataManager.c</FilePath>
            </File>
            <File>
              <FileName>DataManager.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Application\DataManager.h</FilePath>
            </File>
            <File>
              <FileName>Defrosting.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\Defrosting.c</FilePath>
            </File>
            <File>
              <FileName>Defrosting.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Application\Defrosting.h</FilePath>
            </File>
            <File>
              <FileName>DisplayInterface.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\DisplayInterface.c</FilePath>
            </File>
            <File>
              <FileName>DisplayInterface.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Application\DisplayInterface.h</FilePath>
            </File>
            <File>
              <FileName>FridgeRunner.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\FridgeRunner.c</FilePath>
            </File>
            <File>
              <FileName>FridgeRunner.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Application\FridgeRunner.h</FilePath>
            </File>
            <File>
              <FileName>Parameter_Device.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\Parameter_Device.c</FilePath>
            </File>
            <File>
              <FileName>Parameter_Device.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Application\Parameter_Device.h</FilePath>
            </File>
            <File>
              <FileName>Parameter_TemperatureZone.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\Parameter_TemperatureZone.c</FilePath>
            </File>
            <File>
              <FileName>Parameter_TemperatureZone.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Application\Parameter_TemperatureZone.h</FilePath>
            </File>
            <File>
              <FileName>SystemManager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\SystemManager.c</FilePath>
            </File>
            <File>
              <FileName>SystemManager.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Application\SystemManager.h</FilePath>
            </File>
            <File>
              <FileName>VerticalBeamHeater.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\VerticalBeamHeater.c</FilePath>
            </File>
            <File>
              <FileName>VerticalBeamHeater.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Application\VerticalBeamHeater.h</FilePath>
            </File>
            <File>
              <FileName>ResolverDevice.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\ResolverDevice.c</FilePath>
            </File>
            <File>
              <FileName>ResolverDevice.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Application\ResolverDevice.h</FilePath>
            </File>
            <File>
              <FileName>ResolverDevicePriority.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Application\ResolverDevicePriority.h</FilePath>
            </File>
            <File>
              <FileName>FaultCode.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\FaultCode.c</FilePath>
            </File>
            <File>
              <FileName>FaultCode.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Application\FaultCode.h</FilePath>
            </File>
            <File>
              <FileName>FactoryMode.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\FactoryMode.c</FilePath>
            </File>
            <File>
              <FileName>FactoryMode.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Application\FactoryMode.h</FilePath>
            </File>
            <File>
              <FileName>FunctionalCircuitTest.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\FunctionalCircuitTest.c</FilePath>
            </File>
            <File>
              <FileName>ShowroomMode.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\ShowroomMode.c</FilePath>
            </File>
            <File>
              <FileName>TestMode.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\TestMode.c</FilePath>
            </File>
            <File>
              <FileName>Vartemp_State.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\Vartemp_State.c</FilePath>
            </File>
            <File>
              <FileName>CloudControl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\CloudControl.c</FilePath>
            </File>
            <File>
              <FileName>CustomerInstall.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\CustomerInstall.c</FilePath>
            </File>
            <File>
              <FileName>Diagnostic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Application\Diagnostic.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Source/Core</GroupName>
          <Files>
            <File>
              <FileName>Core_Assert.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Core\Core_Assert.c</FilePath>
            </File>
            <File>
              <FileName>Core_Assert.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Core\Core_Assert.h</FilePath>
            </File>
            <File>
              <FileName>Core_CallBackTimer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Core\Core_CallBackTimer.c</FilePath>
            </File>
            <File>
              <FileName>Core_CallBackTimer.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Core\Core_CallBackTimer.h</FilePath>
            </File>
            <File>
              <FileName>Core_Init.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Core\Core_Init.c</FilePath>
            </File>
            <File>
              <FileName>Core_Init.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Core\Core_Init.h</FilePath>
            </File>
            <File>
              <FileName>Core_Scheduler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Core\Core_Scheduler.c</FilePath>
            </File>
            <File>
              <FileName>Core_Scheduler.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Core\Core_Scheduler.h</FilePath>
            </File>
            <File>
              <FileName>Core_TimeBase.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Core\Core_TimeBase.c</FilePath>
            </File>
            <File>
              <FileName>Core_TimeBase.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Core\Core_TimeBase.h</FilePath>
            </File>
            <File>
              <FileName>Core_TimerLibrary.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Core\Core_TimerLibrary.c</FilePath>
            </File>
            <File>
              <FileName>Core_TimerLibrary.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Core\Core_TimerLibrary.h</FilePath>
            </File>
            <File>
              <FileName>Core_Types.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Core\Core_Types.h</FilePath>
            </File>
            <File>
              <FileName>List.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Core\List.c</FilePath>
            </File>
            <File>
              <FileName>List.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Core\List.h</FilePath>
            </File>
            <File>
              <FileName>SimpleFsm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Core\SimpleFsm.c</FilePath>
            </File>
            <File>
              <FileName>SimpleFsm.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Core\SimpleFsm.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Source/CoreUser</GroupName>
          <Files>
            <File>
              <FileName>CoreUser_CallBackTimer_Config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\CoreUser\CoreUser_CallBackTimer_Config.h</FilePath>
            </File>
            <File>
              <FileName>CoreUser_Init_Table.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\CoreUser\CoreUser_Init_Table.h</FilePath>
            </File>
            <File>
              <FileName>CoreUser_Scheduler_Table.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\CoreUser\CoreUser_Scheduler_Table.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Source/Module</GroupName>
          <Files>
            <File>
              <FileName>Crc16_CCITT_FALSE.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Module\Crc16_CCITT_FALSE.c</FilePath>
            </File>
            <File>
              <FileName>Crc16_CCITT_FALSE.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Module\Crc16_CCITT_FALSE.h</FilePath>
            </File>
            <File>
              <FileName>Driver_AdSample.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Module\Driver_AdSample.c</FilePath>
            </File>
            <File>
              <FileName>Driver_AdSample.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Module\Driver_AdSample.h</FilePath>
            </File>
            <File>
              <FileName>Driver_CompFrequency.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Module\Driver_CompFrequency.c</FilePath>
            </File>
            <File>
              <FileName>Driver_CompFrequency.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Module\Driver_CompFrequency.h</FilePath>
            </File>
            <File>
              <FileName>Driver_DoorSwitch.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Module\Driver_DoorSwitch.c</FilePath>
            </File>
            <File>
              <FileName>Driver_DoorSwitch.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Module\Driver_DoorSwitch.h</FilePath>
            </File>
            <File>
              <FileName>Driver_Fan.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Module\Driver_Fan.c</FilePath>
            </File>
            <File>
              <FileName>Driver_Fan.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Module\Driver_Fan.h</FilePath>
            </File>
            <File>
              <FileName>Driver_GradualLamp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Module\Driver_GradualLamp.c</FilePath>
            </File>
            <File>
              <FileName>Driver_GradualLamp.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Module\Driver_GradualLamp.h</FilePath>
            </File>
            <File>
              <FileName>SystemTimerModule.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Module\SystemTimerModule.c</FilePath>
            </File>
            <File>
              <FileName>SystemTimerModule.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Module\SystemTimerModule.h</FilePath>
            </File>
            <File>
              <FileName>Driver_Flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Module\Driver_Flash.c</FilePath>
            </File>
            <File>
              <FileName>Driver_Flash.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Module\Driver_Flash.h</FilePath>
            </File>
            <File>
              <FileName>Driver_AdTemperature.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Module\Driver_AdTemperature.h</FilePath>
            </File>
            <File>
              <FileName>Drive_Valve.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Module\Drive_Valve.c</FilePath>
            </File>
            <File>
              <FileName>Driver_Emulator.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Module\Driver_Emulator.c</FilePath>
            </File>
            <File>
              <FileName>Sbus_Core.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Module\Sbus_Core.c</FilePath>
            </File>
            <File>
              <FileName>Sbus_Display.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Module\Sbus_Display.c</FilePath>
            </File>
            <File>
              <FileName>Sbus_IceMaker.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Module\Sbus_IceMaker.c</FilePath>
            </File>
            <File>
              <FileName>FirewareComm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Module\FirewareComm.c</FilePath>
            </File>
            <File>
              <FileName>Driver_SingleDamper.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Module\Driver_SingleDamper.c</FilePath>
            </File>
            <File>
              <FileName>Sbus_Nfc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Module\Sbus_Nfc.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Source/Iot</GroupName>
          <Files>
            <File>
              <FileName>arch_define.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Iot\arch\arch_define.h</FilePath>
            </File>
            <File>
              <FileName>arch_os.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Iot\arch\arch_os.c</FilePath>
            </File>
            <File>
              <FileName>arch_os.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Iot\arch\arch_os.h</FilePath>
            </File>
            <File>
              <FileName>arch_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Iot\arch\arch_uart.c</FilePath>
            </File>
            <File>
              <FileName>arch_uart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Iot\arch\arch_uart.h</FilePath>
            </File>
            <File>
              <FileName>iot_operation_decoder.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Iot\miio\device\codec\iot_operation_decoder.c</FilePath>
            </File>
            <File>
              <FileName>iot_operation_decoder.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Iot\miio\device\codec\iot_operation_decoder.h</FilePath>
            </File>
            <File>
              <FileName>iot_operation_encoder.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Iot\miio\device\codec\iot_operation_encoder.c</FilePath>
            </File>
            <File>
              <FileName>iot_operation_encoder.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Iot\miio\device\codec\iot_operation_encoder.h</FilePath>
            </File>
            <File>
              <FileName>miio_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Iot\miio\uart\miio_uart.c</FilePath>
            </File>
            <File>
              <FileName>miio_uart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Iot\miio\uart\miio_uart.h</FilePath>
            </File>
            <File>
              <FileName>Iotlist.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Iot\miio\util\Iotlist.h</FilePath>
            </File>
            <File>
              <FileName>util.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Iot\miio\util\util.c</FilePath>
            </File>
            <File>
              <FileName>util.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Iot\miio\util\util.h</FilePath>
            </File>
            <File>
              <FileName>miio_api.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Iot\miio\miio_api.c</FilePath>
            </File>
            <File>
              <FileName>miio_api.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Iot\miio\miio_api.h</FilePath>
            </File>
            <File>
              <FileName>miio_define.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Iot\miio\miio_define.h</FilePath>
            </File>
            <File>
              <FileName>Iot_Spec.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Iot\spec\Iot_Spec.c</FilePath>
            </File>
            <File>
              <FileName>Iot_Spec.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Iot\spec\Iot_Spec.h</FilePath>
            </File>
            <File>
              <FileName>Iot_SpecHandler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Iot\spec\Iot_SpecHandler.c</FilePath>
            </File>
            <File>
              <FileName>Iot_SpecHandler.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Iot\spec\Iot_SpecHandler.h</FilePath>
            </File>
            <File>
              <FileName>user_app_func.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Iot\user\app\user_app_func.c</FilePath>
            </File>
            <File>
              <FileName>user_app_func.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Iot\user\app\user_app_func.h</FilePath>
            </File>
            <File>
              <FileName>user_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Iot\user\user_config.h</FilePath>
            </File>
            <File>
              <FileName>Iot.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\Iot\Iot.c</FilePath>
            </File>
            <File>
              <FileName>Iot.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Iot\Iot.h</FilePath>
            </File>
            <File>
              <FileName>IotUsr.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Source\Iot\IotUsr.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Source</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\main.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Startup</GroupName>
          <Files>
            <File>
              <FileName>startup_hc32l186.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\Startup\startup_hc32l186.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Debug</GroupName>
          <Files>
            <File>
              <FileName>ramlog.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Debug\ramlog.c</FilePath>
            </File>
            <File>
              <FileName>syslog.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Debug\syslog.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Debug/cm_backtrace</GroupName>
          <Files>
            <File>
              <FileName>cm_backtrace.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Debug\cm_backtrace\cm_backtrace.c</FilePath>
            </File>
            <File>
              <FileName>cm_backtrace.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Debug\cm_backtrace\cm_backtrace.h</FilePath>
            </File>
            <File>
              <FileName>cmb_cfg.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Debug\cm_backtrace\cmb_cfg.h</FilePath>
            </File>
            <File>
              <FileName>cmb_def.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Debug\cm_backtrace\cmb_def.h</FilePath>
            </File>
            <File>
              <FileName>cmb_fault.S</FileName>
              <FileType>2</FileType>
              <FilePath>..\Debug\cm_backtrace\fault_handler\keil\cmb_fault.S</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>RTT</GroupName>
          <Files>
            <File>
              <FileName>SEGGER_RTT.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\RTT\SEGGER_RTT.c</FilePath>
            </File>
            <File>
              <FileName>SEGGER_RTT_printf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Source\RTT\SEGGER_RTT_printf.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Parameter</GroupName>
          <Files>
            <File>
              <FileName>ParameterManager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Parameter\ParameterManager.c</FilePath>
            </File>
            <File>
              <FileName>Parameters.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Parameter\Parameters.c</FilePath>
            </File>
            <File>
              <FileName>MaintenanceManager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Parameter\MaintenanceManager.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components>
      <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="5.5.0" condition="ARMv6_7_8-M Device">
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="5.8.0"/>
        <targetInfos>
          <targetInfo name="MainBoard"/>
        </targetInfos>
      </component>
    </components>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>HC32L18x</LayName>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
