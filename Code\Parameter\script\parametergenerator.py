#!/usr/bin/python
# -*- coding: UTF-8 -*-

import sys
import struct
import binascii
import pandas as pd

ary_CRC16_Table0 = [0x0000, 0xC0C1, 0xC181, 0x0140, 0xC301, 0x03C0, 0x0280, 0xC241, 0xC601, 0x06C0, 0x0780, 0xC741, 0x0500, 0xC5C1, 0xC481, 0x0440]
ary_CRC16_Table1 = [0x0000, 0xCC01, 0xD801, 0x1400, 0xF001, 0x3C00, 0x2800, 0xE401, 0xA001, 0x6C00, 0x7800, 0xB401, 0x5000, 0x9C01, 0x8801, 0x4400]
def Cal_CRC_SingleData(crcvalue, data):
    databyte = 0
    low_halfbyte_index = 0
    high_halfbyte_index = 0
    crcvalue_table0 = 0
    crcvalue_table1 = 0
    crcvalue_highbyte = 0
    databyte = crcvalue & 0x00ff
    databyte = databyte ^ data
    low_halfbyte_index = databyte & 0x0f
    high_halfbyte_index = (databyte >> 4) & 0x0f
    crcvalue_table0 = ary_CRC16_Table0[low_halfbyte_index]
    crcvalue_table1 = ary_CRC16_Table1[high_halfbyte_index]
    crcvalue_highbyte = (crcvalue >> 8) & 0x00ff
    crcvalue = crcvalue_table0 ^ crcvalue_table1 ^ crcvalue_highbyte
    return crcvalue

class MaintenanceParams:
    def __init__(self, len):
        self.len = len >> 1
        self.crc = 0
        self.data = []
    def transform(self, xls_file, model):
        if len(sys.argv) > 1:
            writer = pd.ExcelWriter(sys.argv[1] + '.\\maintenance_' + model + '.xlsx', engine='openpyxl')
        else:
            writer = pd.ExcelWriter('.\\maintenance_' + model + '.xlsx', engine='openpyxl')
        xf = pd.read_excel(xls_file, sheet_name='Maintenance')
        coldata = xf[model].fillna(0)
        if len(coldata) > self.len:
            print("maintenance" + ' is out of range(' + str(self.len) + ')')
            input('Press any key exit...')
            quit()
        self.data.clear()
        for data in coldata:
            self.data.append(int(data))
        for i in range(len(self.data), self.len):
            self.data.append(0)
        self.crc = 0xFFFF
        index = 0
        crc = 0xFFFF
        for data in self.data:
            if index != 0:
                crc = Cal_CRC_SingleData(crc, (int(data) >> 8) & 0x00ff)
                crc = Cal_CRC_SingleData(crc, (int(data)) & 0x00ff)
            index = index + 1
        self.data[0] = crc
        xf.loc[0, model]= crc
        xf.to_excel(writer, sheet_name='Maintenance', index=False)
        writer.close()

class ParamSection:
    def __init__(self, rowdata):
        self.name = rowdata.iloc[0]
        self.id = rowdata.iloc[1]
        self.size = rowdata.iloc[2]
        self.version = rowdata.iloc[3]
        self.crc = 0
        self.offset = 0
        self.data = []
        self.is_maintenance = False
        if self.name == "Maintenance":
            self.is_maintenance = True
            self.mp = MaintenanceParams(self.size)
    def transform(self, model, xls_file, pf):
        if self.is_maintenance:
            self.mp.transform(xls_file, model)
            coldata = self.mp.data
        else:
            sf = pd.read_excel(xls_file, sheet_name=self.name)
            coldata = sf[model].fillna(0xffff)
        self.data.clear()
        for data in coldata:
            self.data.append((int(data) >> 8) & 0x00ff)
            self.data.append(int(data) & 0x00ff)
        if len(self.data) > self.size:
            print(self.name + ' is out of range(' + str(self.size) + ')')
            input('Press any key exit...')
            quit()
        for i in range(len(self.data), self.size):
            self.data.append(0xff)
        self.crc = 0xFFFF
        for data in self.data:
            self.crc = Cal_CRC_SingleData(self.crc, data)
    
    def getcontent(self):
        return self.data
    
    def savecontent(self, pf, offset):
        pf.write('{')
        pf.write(hex(self.id) + ',')
        pf.write(hex(self.version) + ',')
        pf.write(hex(offset) + ',')
        pf.write(hex(self.size) + ',')
        pf.write(hex(self.crc) + '},')
        self.offset = offset
        return offset + len(self.data)
    def to_bin(self, bf):
        bf.write(struct.pack('>I', self.id))
        bf.write(struct.pack('>I', self.version))
        bf.write(struct.pack('>I', self.offset))
        bf.write(struct.pack('>I', self.size))
        bf.write(struct.pack('>I', self.crc))
class ModelParams:
    def __init__(self, index, model, sections):
        self.sections = sections
        self.magic = 0x22446688
        self.model = model
        self.models = model.split('&')
        self.crc = 0
        self.index = index
        self.data = []

    def transform(self, xls_file, pf, pname):
        offset = 0
        for section in self.sections:
             section.transform(self.model, xls_file, pf)
             for data in section.getcontent():
                self.data.append(data)
        self.crc = 0xFFFF
        self.len = len(self.data)
        for data in self.data:
            self.crc = Cal_CRC_SingleData(self.crc, data)
        if self.index > 4:
            pf.write('static ' + 'const ' + pname + ' ' + self.models[0] + ' =\n')
        else:
            pf.write('static ' + 'const ' + pname + ' ' + self.models[0] + ' =\n')
        pf.write('{\n')
        pf.write(hex(self.magic)+ ',\n')
        pf.write('{')
        for section in self.sections:
            offset = section.savecontent(pf, offset)
        for i in range(len(self.sections), 10):
            pf.write('{''0}')
            if i != 9:
                pf.write(',')
        pf.write('},\n{')
        index = 0
        for model in self.models:
            index += 1
            if index != len(self.models):
                pf.write('\"' + model + '\",')
            else:
                pf.write('\"' + model + '\"}')
        pf.write(',\n' + hex(self.len))
        pf.write(',\n' + hex(self.crc))
        pf.write(',\n{''0}')
        pf.write(',\n{')
        index = 0
        for data in self.data:
            index = index + 1
            pf.write(hex(data))
            if index != self.len:
                pf.write(',')
                if index % 20 == 0:
                    pf.write('\n ')
            else:
                pf.write('\n}\n')
        pf.write('};\n')

    def to_bin(self):
        print('generate bin file\n')
        if len(sys.argv) > 1:
            path = sys.argv[1]
        else:
            path = ''
        with open(path + '.\\' + self.model + '_params.bin', 'wb') as bf:
            data = struct.pack('>I', self.magic)
            bf.write(data)
            for section in self.sections:
                offset = section.to_bin(bf)
            for i in range(len(self.sections), 10):
                bf.write(struct.pack('>I', 0))
                bf.write(struct.pack('>I', 0))
                bf.write(struct.pack('>I', 0))
                bf.write(struct.pack('>I', 0))
                bf.write(struct.pack('>I', 0))
            for model in self.models:
                binary_data = binascii.a2b_qp(model)
                bf.write(binary_data)
                for i in range(len(binary_data), 8):
                    bf.write(struct.pack('B', 0))
            for index in range(len(self.models), 5):
                for i in range(0, 8):
                    bf.write(struct.pack('B', 0))
            bf.write(struct.pack('>I', self.len))
            bf.write(struct.pack('>I', self.crc))
            bf.write(struct.pack('>I', 0))
            for byte in self.data:
                bf.write(struct.pack('B', byte))

def generate_params_file(xls_file, params_file):
    sections = []
    models = []
    is_model = False
    length = 0
    xf = pd.read_excel(xls_file, sheet_name='Table')
    for index, row in xf.iterrows():
        sections.append(ParamSection(row))
    
    if len(sections) > 11:
        print('sections ' + 'is out of range(' + str(len(sections)) + ')')
        input('Press any key exit...')
        quit()     

    mf = pd.read_excel(xls_file, sheet_name='Main')
    index = 0
    for model in mf.columns:
        if model == 'step':
           is_model = True
        else:
            if is_model:
                index = index + 1
                models.append(ModelParams(index, model, sections))
    for section in sections:
        length = length + section.size
    with open(params_file, 'w') as pf:
        pf.write('#include "Parameter.h"\n')
        pf.write('typedef struct{\n')
        pf.write('uint32_t magic;\n')
        pf.write('parameter_fireware_header_st fheader[PARAMETER_FIREWARE_MAX];\n')
        pf.write('uint8_t  hw_id_str[PARAMETER_ID_MAX][PARAMETER_ID_LEN];\n')
        pf.write('uint32_t len;\n')
        pf.write('uint32_t crc;\n')
        pf.write('uint32_t fill[PARAMETER_FIREWARE_FILL];\n')
        pf.write('uint8_t data[' + str(length) + '];\n')
        pname = 'parameter_fireware_st_' + str(length)
        pf.write('}' + pname + ';\n')
        for model in models:
            model.transform(xls_file, pf, pname)
            model.to_bin()
        pf.write('parameter_fireware_st *model_parameters[' + str(len(models) + 1) + '] = {\n')
        for model in models:
             pf.write('(parameter_fireware_st *)' + '&' + model.models[0] + ',\n')
        pf.write('NULL\n')
        pf.write('};\n')

if __name__ == '__main__':
    if len(sys.argv) > 1 :
        generate_params_file(sys.argv[1] + '.\\parameters.xls', sys.argv[1] + '.\\Parameters.c')
    else :
        generate_params_file('.\\parameters.xls', '.\\Parameters.c')
       
