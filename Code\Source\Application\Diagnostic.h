/*!
 * @file
 * @brief This file defines public constants, types and functions for the cooling controller.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef _DIAGNOSTIC_H
#define _DIAGNOSTIC_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include <stdlib.h>

#define DIAGNOSTIC_POWER_LOAD_COUNT_MAX 10

typedef enum{
    DIAGNOSTIC_POWER_LOAD_FRZ_DEFORST_HEARTER = 0,
    DIAGNOSTIC_POWER_LOAD_REF_DEFORST_HEARTER,
    DIAGNOSTIC_POWER_LOAD_COUNT
}diagnostic_power_load_e;

typedef struct{
    uint16_t min_power;
    uint16_t max_power;
    bool inner_range;
    bool (*calc_load_condtion)(void);
    uint16_t (*calc_load_power)(void);
}diagnostic_power_load_st;

void DiagnosticInit(void);
void DiagnosticRun(void);
uint8_t IsDiagnosticPowerLoadFault(diagnostic_power_load_e load);
#endif

