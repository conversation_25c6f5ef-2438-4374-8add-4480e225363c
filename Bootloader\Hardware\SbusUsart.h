/*!
 * @file
 * @brief This file defines public constants, types and functions for INVERTER usart.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef SBUS_USART_
#define SBUS_USART_

#include <stdint.h>
#include <stdbool.h>
#include "FirewareComm.h"

#define U8_SBUS_SEND_TIMEOUT_MILLISECOND    ((uint8_t)250)
#define U16_SBUS_RECV_TIMEOUT_MILLISECOND    ((uint16_t)300)
#define U16_SBUS_RECE_DATA_ERROR_TIME_WITH_100MS     ((uint16_t)300)
#define U8_SBUS_DISPLAY_CRC_RETRY      ((uint8_t)25)

void Init_UartSbus(void);
void Handle_UartSbusOverTime(void);
void Handle_UartSbusSendData(void);
void Handle_UartSbusReceData(const uint8_t u8_rece_data);

#endif
