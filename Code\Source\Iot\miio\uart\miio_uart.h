/**
 * <AUTHOR>
 * @date    2019
 * @par     Copyright (c):
 *
 *    Copyright 2019 MIoT,MI
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */

#ifndef __MIIO_UART_H__
#define __MIIO_UART_H__

#include "arch_os.h"
#include "arch_uart.h"

typedef enum _uart_error_t {
    UART_OK = 0,
    UART_DESTROY_ERROR	= -1,
    UART_OPEN_ERROR		= -2,
    UART_SET_ARRT_ERROR	= -3,
    UART_SEND_ERROR		= -4,
    UART_RECV_ACK_ERROR	= -5,
    UART_RECV_ERROR		= -6,
} uart_error_t;

int miio_uart_init(void);

int miio_uart_send_str(const char* str);

int miio_uart_send_str_wait_ack(const char* str);

int miio_uart_recv_str(uint8_t* pbuf, int buf_len, uint32_t timeout_ms);
int miio_uart_recv_str_aync(uint8_t** ppbuf);
int miio_uart_has_more_data(void);


#endif
