/*!
 * @file
 * @brief door switch detection and alarm.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef _DRIVER_DOORSWITCH_H_
#define _DRIVER_DOORSWITCH_H_

#include <stdint.h>
#include <stdbool.h>

#define DOOR_OPEN true // 门开
#define DOOR_CLOSE false // 门关

#define IO_LEVEL_HIGH true // 端口高电平
#define IO_LEVEL_LOW false // 端口低电平

#define U8_ANTI_SHAKE_TIMER (uint8_t)3 // 门开关信号防抖
#define U16_OPEN_DOOR_ALARM_DELAY_TIME_SECOND (uint16_t)(2 * 60) // 开门报警延迟时间
#define U16_OPEN_DOOR_ALARM_MAX_INTERVAL_TIME_SECOND (uint16_t)30 // 门开关报警最大间隔时间
#define U16_OPEN_DOOR_ALARM_MIN_INTERVAL_TIME_SECOND (uint16_t)30 // 门开关报警最小间隔时间
#define U16_DOOR_OPEN_CLOSE_LAMP_TIME_SECOND (uint16_t)(5 * 60) // 开门关灯时间
#define U16_DOOR_OPEN_DEFAULT_CLOSE_TIME_SECOND (uint16_t)(10 * 60) // 开门视为已关时间
#define U16_DOOR_OPEN_SWITCH_ERROR_TIME_SECOND (uint16_t)(10 * 60) // 门开关故障判断时间
#define U16_DOOR_OPEN_COOL_FAN_TIME_SECOND (uint16_t)(5 * 60) // 冷凝风扇恢复工作时间
#define U8_FAN_DELAY_TIMER_AFTER_DOOR_CLOSE ((uint8_t)15)
#define U8_VERTICALBEAMHEATER_DELAY_TIMER_AFTER_DOOR_CLOSE ((uint8_t)30)
#define U8_COMPFREQUENCY_DELAY_TIMER_AFTER_DOOR_CLOSE ((uint8_t)30)
#define U8_COMPFREQUENCY_DELAY_TIMER_AFTER_DEFAULT_CLOSE ((uint8_t)15)
#define U16_FRZLED_DELAY_TIMER_AFTER_CLOSE ((uint16_t)1000)

#define U8_MIN_UINT8 ((uint8_t)0)
#define U8_MAX_UINT8 ((uint8_t)255)

#define U16_MIN_UINT16 ((uint16_t)0)
#define U16_MAX_UINT16 ((uint16_t)65535)

typedef enum
{
    DOOR_REF_LEFT = 0,
    DOOR_REF_RIGHT,
    DOOR_FRZ_LEFT,
    DOOR_FRZ_RIGHT,
    DOOR_REF,
    DOOR_FRZ,
    DOOR_ALL,
    DOOR_MAX_NUMBER
} DoorState_em;
typedef uint8_t DoorTypeId_t;

typedef struct
{
    void (*p_JudgeDoorSwitchState)(void); // 检测门状态函数
    void (*p_HandleDoorSwitchState)(void); // 处理门状态函数
    uint8_t u8_DoorOpenSwitchState; // 开门端口状态
} DoorSwitchState_st;

typedef struct
{
    uint16_t u16_DoorOpenSecondCounter; // 门开时间
    uint16_t u16_DoorOpenTotalSecondCounter; // 总开门时间
    uint16_t u16_DoorCloseSecondCounter; // 门关时间

    uint8_t u8_JudgeDoorState10MsCounter; // 判断门状态防抖10Ms计时
    uint8_t u8_DoorOpenCloseTotalCounter; // 门开关总次数

    bool f_DoorSwitchState; // 门开关端口状态
    bool f_DoorState; // 门开、关
    bool f_DoorOpenClose; // 门开关一次
    bool f_DoorOpenAlarm; // 门开报警
    bool f_DoorOpenLamp; // 门开关灯
    bool f_DoorDefaultClose; // 门视为已关
    bool f_DoorSwitchError; // 门开关故障
} DoorState_st;

void Update_AllDoorsSwitchState(void);
void Handle_AllDoorsState(void);
uint16_t Get_DoorOpenTimeSecond(DoorTypeId_t typeId); // 取开门时间
uint8_t Get_DoorOpenCloseCounter(DoorTypeId_t typeId);
void Clear_DoorOpenTimeSecond(DoorTypeId_t typeId); // 清零开门累计时间
bool Get_DoorSwitchState(DoorTypeId_t typeId); // 取门开关状态
bool Get_DoorSwitchAlarmState(DoorTypeId_t typeId); // 取门开关报警状态
bool Get_DoorSwitchErrorState(DoorTypeId_t typeId); // 取门开关故障状态
uint8_t Get_DoorSwitchflagState(DoorTypeId_t typeId); // 取门开关数据
#endif
