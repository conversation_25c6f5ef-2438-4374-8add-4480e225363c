/*!
 * @file
 * @brief Configuration for CallbackTimer module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stdint.h>
#include <stdbool.h>

#ifndef __COREUSER_CALLBACKTIMER_H__
#define __COREUSER_CALLBACKTIMER_H__

#define CALLBACK_TIMER_CALLBACK_DATA_ENABLED (false)

#define CALLBACK_TIMER_NORMAL_PRIORITY_ENABLED (true)
#define CALLBACK_TIMER_HIGH_PRIORITY_ENABLED (false)

#define U8_CORE_CALLBACK_TIMER_POOL_SIZE_NORMAL ((uint8_t)20)
#define U8_CORE_CALLBACK_TIMER_POOL_SIZE_HIGH ((uint8_t)0)

#endif // #ifndef __COREUSER_CALLBACKTIMER_H__
