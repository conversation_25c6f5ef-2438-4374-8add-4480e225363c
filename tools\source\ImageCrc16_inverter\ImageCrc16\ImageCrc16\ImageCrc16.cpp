
#include <iostream>
#include <fstream>
#include <stdio.h>
#include <windows.h>
#include <cstring>
#include <iomanip>
#include <sstream>
#include "ImageHeader.h"

using namespace std;

#define U16_CRC_INITIAL_VALUE (uint16_t)0xFFFF

static const uint16_t ary_CRC16_Table0[16] = {
    0x0000U, 0xC0C1U, 0xC181U, 0x0140U, 0xC301U, 0x03C0U, 0x0280U, 0xC241U, 0xC601U, 0x06C0U, 0x0780U, 0xC741U, 0x0500U, 0xC5C1U, 0xC481U, 0x0440U
};

static const uint16_t ary_CRC16_Table1[16] = {
    0x0000U, 0xCC01U, 0xD801U, 0x1400U, 0xF001U, 0x3C00U, 0x2800U, 0xE401U, 0xA001U, 0x6C00U, 0x7800U, 0xB401U, 0x5000U, 0x9C01U, 0x8801U, 0x4400U
};

uint32_t convert_from_bigendian32(uint32_t val)
{
    uint32_t temp = 0;
    uint8_t *p = (uint8_t *)&val;

    temp = (p[0] << 24) |
        (p[1] << 16) |
        (p[2] << 8) |
        p[3];
    return temp;
}

uint16_t convert_from_bigendian16(uint16_t val)
{
    uint16_t temp = 0;
    uint8_t *p = (uint8_t *)&val;

    temp = (p[0] << 8) | p[1];
    return temp;
}


uint16_t Cal_CRC_SingleData(uint16_t u16_crcvalue, uint8_t u8_data)
{
    uint8_t u8_databyte = 0;
    uint8_t u8_low_halfbyte_index = 0;
    uint8_t u8_high_halfbyte_index = 0;
    uint16_t u16_crcvalue_table0 = 0;
    uint16_t u16_crcvalue_table1 = 0;
    uint16_t u16_crcvalue_highbyte = 0;
    u8_databyte = (uint8_t)(u16_crcvalue & 0x00ff);
    u8_databyte ^= u8_data;
    u8_low_halfbyte_index = u8_databyte & 0x0f;
    u8_high_halfbyte_index = (u8_databyte >> 4) & 0x0f;
    u16_crcvalue_table0 = ary_CRC16_Table0[u8_low_halfbyte_index];
    u16_crcvalue_table1 = ary_CRC16_Table1[u8_high_halfbyte_index];
    u16_crcvalue_highbyte = (u16_crcvalue >> 8) & 0x00ff;
    u16_crcvalue =
        u16_crcvalue_table0 ^ u16_crcvalue_table1 ^ u16_crcvalue_highbyte;
    return (u16_crcvalue);
}

std::string uint16ToHex(uint16_t value)
{
    std::ostringstream stream;
    stream << std::setfill('0') << std::setw(sizeof(uint16_t) * 2) << std::hex << value;
    return stream.str();
}

int main(int argc, const char *argv[])
{
    app_imageheader_st header;
    ifstream imagef;
    uint32_t version;
    string path;
    string bootver;
    uint16_t bootcrc;
    string appver;
    uint16_t appcrc;
    size_t pos;
    size_t len;
    size_t total;
    char data;
    uint16_t crc = U16_CRC_INITIAL_VALUE;
    string filename;

    if(argc < 6)
    {
        cout << "Use ImageCrc16.exe flashbin bootver bootsize appver appheader" << endl;
        goto out;
    }

    path = argv[1];
    bootver = argv[2];
    appver = argv[4];
    if(strncmp(argv[3], "0x", 2))
    {
        cout << "bootsize is a hex with 0x" << endl;
        goto out;
    }
    len = std::stoul(argv[3], nullptr, 16);
    if(len <= 0)
    {
        cout << "boot size:" << len << " is not vaild" << endl;
        goto out;
    }
    memset(&header, 0, sizeof(app_imageheader_st));

    imagef.open(path, ios::binary | ios::in);
    if(!imagef)
    {
        cout << "file: " << path << " is not exsited" << endl;
        goto out;
    }
    imagef.seekg(0, ios::end);
    total = imagef.tellg();
    if(len >= total)
    {
        cout << "boot size: " << len << " is out of total:" << total<<endl;
        goto out; 
    }
    imagef.seekg(0, ios::beg);
    while(!(imagef.rdstate() & ios_base::eofbit))
    {
        imagef.read(&data, 1);
        crc = Cal_CRC_SingleData(crc, data);
        if (--len == 0)
        {
            bootcrc = crc;
            break;
        }
    }
    if(strncmp(argv[5], "0x", 2))
    {
        cout << "app header offset is a hex with 0x" << endl;
        goto out;
    }
    pos = std::stoul(argv[5], nullptr, 16);
    imagef.seekg(pos, ios::beg);
    if(pos + sizeof(app_imageheader_st)  >= total)
    {
        cout << "app header off: " << pos << " is out of total:" << total << endl;
        goto out;
    }
    
    imagef.read((char *)&header, sizeof(app_imageheader_st));
    len = imagef.gcount();
    if(len != sizeof(app_imageheader_st))
    {
        cout << "app header read fail"<<endl;
        goto out;
    }

    appcrc = convert_from_bigendian16(header.crc16);
    imagef.close();
    cout << "boot version:" << bootver << endl;
    cout << "boot crc:" << std::hex << bootcrc << endl;
    cout << "app version:" << appver << endl;
    cout << "app crc:" << std::hex << appcrc << endl;
    filename = path + "_" + bootver + "_" + uint16ToHex(bootcrc) + "_" + appver + "_" + uint16ToHex(appcrc) + ".bin";
    std::rename(path.c_str(), filename.c_str());
    return 0;

out:
    while(1)
    {
        Sleep(30);
    }
}