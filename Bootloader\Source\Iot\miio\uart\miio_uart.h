/**
 * <AUTHOR>
 * @date    2019
 * @par     Copyright (c):
 *
 *    Copyright 2019 MIoT,MI
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */

#ifndef __MIIO_UART_H__
#define __MIIO_UART_H__

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdarg.h>
#include <stddef.h>
#include <ctype.h>
#include <stdint.h>
#include "arch_os.h"

typedef enum _uart_error_t {
    UART_OK = 0,
    UART_DESTROY_ERROR	= -1,
    UART_OPEN_ERROR		= -2,
    UART_SET_ARRT_ERROR	= -3,
    UART_SEND_ERROR		= -4,
    UART_RECV_ACK_ERROR	= -5,
    UART_RECV_ERROR		= -6,
} uart_error_t;

#define CMD_STR_MAX_LEN             (528) 
#define CMD_METHOD_LEN_MAX          (32)
#define CMD_RESULT_BUF_SIZE         (256)
#define ACK_BUF_SIZE                (20)
#define DATA_STRING_MAX_LENGTH      (2)
#define USER_UART_RXBUF_SIZE        (256)
#define USER_UART_TXBUF_SIZE        (256)
#define USER_POLL_INTERVAL_MS       (200)
#define USER_UART_TIMEOUT_MS        (500)
#define USER_RECEIVE_RETRY          (25)


int miio_uart_init(void);
int miio_uart_send_str(uint8_t ch, const char* str);
int miio_uart_send_str_wait_ack(uint8_t ch, const char* str, uint32_t timeout_ms);
int miio_uart_recv_str(uint8_t ch, uint8_t* pbuf, int buf_len, uint32_t timeout_ms);
int miio_uart_recv_str_aync(uint8_t ch, uint8_t** ppbuf);
int miio_uart_has_more_data(uint8_t ch);
int miio_uart_send_byte(uint8_t ch, unsigned char c);
int miio_xmodem_recv_str(uint8_t ch, uint8_t* pbuf,uint32_t len, uint32_t timeout_ms);
int miio_xmodem_recv_byte(uint8_t ch, unsigned char *c, uint32_t timeout_ms);
void miio_uart_data_flush(uint8_t ch);
#endif
