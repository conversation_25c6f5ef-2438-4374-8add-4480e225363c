#ifndef __UART_COMM_H__
#define __UART_COMM_H__

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include <stdio.h>
#include "FirewareComm.h"

typedef struct 
{
    void (*enableTx)(bool enable);
    void (*enableRx)(bool enable);
    void (*sendOneData)(uint8_t data);
}uartcomm_ops_s;


typedef struct
{
    uint8_t* sendPos;
    uint8_t* recvPos;
    uint16_t u16_ReceCrcValue;
    fireware_frame_st sendPacket;
    fireware_frame_st recvPacket;
    uint8_t u8_SendDataState;        // 发送数据状态
    uint8_t u8_ReceDataState;        // 接收数据状态
    uint8_t u8_SendLength;           // 发送长度
    uint8_t u8_SendCount;            // 发送计数
    uint8_t u8_ReceLength;           // 接收长度
    uint8_t u8_ReceCount;            // 接收计数
    uint8_t u8_SendTimeOut;
    uint16_t u16_ReceTimeOut;
    uint8_t u16_RecvDataError;
    uint8_t u8_SendTimeOutCount;     // 发送超时计数
    uint16_t u16_ReceTimeOutCount;     // 接收超时计数
    uint16_t u16_RecvDataErrorCount;
    bool f_SendIE;                   // 发送允许
    bool f_HaveFrameToSend;
    bool f_FrameSending;             // 报文发送
    bool f_FrameReceiving;
    bool f_FrameHandle;              // 报文处理
    bool b_ReceiveError;
    bool f_FrameSendTimeout;
    bool f_FrameRecvTimeout;
    uartcomm_ops_s uart_ops;
} UartComm_st;


void UartCommInit(UartComm_st *uart, uint8_t send_timeout,
                       uint16_t recv_timeout, uint16_t recv_dataerror);
int32_t UartCommTransmit(fireware_comm_st *fw);
int32_t UartCommReceive(fireware_comm_st *fw);
int32_t UartCommTransmit(fireware_comm_st *fw);
void Handle_UartCommSendData(UartComm_st *uart);
void Handle_UartCommOverTime(UartComm_st *uart);
void Handle_UartCommReceData(UartComm_st *uart, const uint8_t u8_rece_data);

#endif
