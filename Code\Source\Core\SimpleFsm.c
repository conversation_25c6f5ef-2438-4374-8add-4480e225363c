/*!
 * @file
 * @brief
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include <stddef.h>
#include "SimpleFsm.h"

void SimpleFsm_SendSignal(SimpleFsm_t *instance, SimpleFsmSignal_t signal, const void *data)
{
    instance->currentState(instance, signal, data);
}

void SimpleFsm_Init(SimpleFsm_t *instance, SimpleFsmState_t initialState, void *_context)
{
    instance->currentState = initialState;
    instance->context = _context;
    SimpleFsm_SendSignal(instance, SimpleFsmSignal_Entry, NULL);
}

void SimpleFsm_Transition(SimpleFsm_t *instance, SimpleFsmState_t targetState)
{
    SimpleFsm_SendSignal(instance, SimpleFsmSignal_Exit, NULL);
    instance->currentState = targetState;
    SimpleFsm_SendSignal(instance, SimpleFsmSignal_Entry, NULL);
}
