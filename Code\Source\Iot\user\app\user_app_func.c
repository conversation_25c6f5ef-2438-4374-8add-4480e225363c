#include "miio_define.h"
#include "miio_api.h"
#include "miio_uart.h"

#include "user_app_func.h"
#include "Driver_Flash.h"
#include "CloudControl.h"
#include "ParameterManager.h"
#include "MaintenanceManager.h"

int app_func_get_time(char* pResult)
{
	strcpy(pResult, "time\r");
    //miio_uart_send_str("time\r");

    return 0;
}

int app_func_get_mac(char* pResult)
{
	strcpy(pResult, "mac\r");
    //miio_uart_send_str("mac\r");

    return 0;
}

int app_func_get_model(char* pResult)
{
	strcpy(pResult, "model\r");
    //miio_uart_send_str("model\r");

    return 0;
}

int app_func_get_version(char* pResult)
{
	strcpy(pResult, "version\r");
    //miio_uart_send_str("version\r");

    return 0;
}

int app_func_getwifi(char* pResult)
{
	strcpy(pResult, "getwifi\r");
    //miio_uart_send_str("getwifi\r");

    return 0;
}

int app_func_get_arch_platform(char* pResult)
{
	strcpy(pResult, "arch\r");
    //miio_uart_send_str("arch\r");

    return 0;
}

int app_func_get_net_state(char* pResult)
{
	strcpy(pResult, "net\r");
    //miio_uart_send_str("net\r");
    return 0;
}

int app_func_reboot(char* pResult)
{
#if 0
    if (miio_uart_send_str_wait_ack("reboot\r") == UART_RECV_ACK_ERROR)
    	return -1;
#else
	strcpy(pResult, "reboot\r");
	//miio_uart_send_str("reboot\r");
#endif	

    return 0;
}

int app_func_restore(char* pResult)
{

#if 0
    if (miio_uart_send_str_wait_ack("restore\r") == UART_RECV_ACK_ERROR)
    	return -1;
#else
	strcpy(pResult, "restore\r");
	//miio_uart_send_str("restore\r");
#endif

    return 0;

}



int app_func_setwifi(char* pResult)
{

	strcpy(pResult, "setwifi "USER_SSID" "USER_PASSWD"\r");
    //miio_uart_send_str("setwifi "USER_SSID" "USER_PASSWD"\r");
    return 0;
}



int app_func_set_mcu_version(char* pResult)
{
#if 0
    if(miio_uart_send_str_wait_ack("mcu_version "USER_MCU_VERSION"\r") == UART_RECV_ACK_ERROR)
    	return -1;
#else
    sprintf(pResult, "mcu_version %04d\r", GetMcuVersion());

	//miio_uart_send_str("mcu_version "USER_MCU_VERSION"\r");
#endif
    return 0;
}



int app_func_factory(char* pResult)
{
#if 0
    if (miio_uart_send_str_wait_ack("factory\r") == UART_RECV_ACK_ERROR)
    	return -1;
#else
	strcpy(pResult, "factory\r");
	//miio_uart_send_str("factory\r");
#endif
    return 0;
}

int app_func_sn(char* pResult)
{
    uint8_t fct_sn[PRODUCT_SN_SIZE+1] = {0};

    if(ReadProductSn(fct_sn, PRODUCT_SN_SIZE) < 0)
    {
        strcpy((char *)fct_sn, "null");
    }
    strcpy(pResult, "sn ");
    strcat(pResult, (const char*)fct_sn);
    strcat(pResult, "\r");
    return 0;
}

int app_func_get_sn(char* pResult)
{
    strcpy(pResult, "sn\r");
    return 0;
}

int app_func_model(char* pResult)
{
    uint8_t model[PRODUCT_MODEL_SIZE+1] = {0};

    ReadProductUserModel(model, PRODUCT_MODEL_SIZE);
    strcpy(pResult, "model ");
    strcat(pResult, (const char *)model);
    strcat(pResult, "\r");
    return 0;
}

int app_func_ble_conf(char* pResult)
{
    uint8_t *pid;

    pid = GetMachinePid();
    if(pid != NULL)
    {
        sprintf(pResult, "ble_config set %s %04d\r", pid, GetMcuVersion());
    }
    return 0;
}


int app_func_maintence_upload(char *pResult)
{
    return UploadMaintenanceParam(pResult);
}

int app_func_lypower_upload(char *pResult)
{
    return UploadLinYunPowerParam(pResult);
}

int app_func_getdid(char* pResult)
{
    strcpy(pResult, "getdid\r");
    return 0;
}


