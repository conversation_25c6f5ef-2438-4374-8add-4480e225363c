1.构建好主控boot和app;
2.获取display和inverter的ota升级bin，inverter_ota.bin 和 diaplay_ota.bin要求128字节对齐，并带有版本和CRC头部；
4.修改build_bin.bat中对应版本变量
set MODEL=bs32s（产品model型号）
set OTA_VERSION=0007（OTA总版本号）
set APP_VERSION=0001 (主控APP版本号)
set INVERTER_VERSION=0032 (变频APP版本号)
set INVERTER_CRC=0xBBE0 （变频APP CRC）
set BOOT_VERSION=0001     （主控BOOT版本号）
5.运行build_bin.bat构建ota整机升级包，注意观察输出信息是否符合打包目标，输出在out目录下：
xxx_main_flash_xxx.bin                    主控flash刷机包
xxx_ota_xxx.bin                                整机ota升级包，可以用于上传到ota平台进行升级的文件
