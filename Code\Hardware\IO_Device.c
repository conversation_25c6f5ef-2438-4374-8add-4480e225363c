/*!
 * @file
 * @brief This module is device IO.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "IO_Device.h"
#include "Driver_GradualLamp.h"
#include "SystemTimerModule.h"

static bool b_IonGenerator_En = false;
static bool b_FrzIonGenerator_En = false;
static uint16_t u16_IonGenerator_TotalOnMinutes;
static uint16_t u16_IonGenerator_StartMinute;
static uint16_t u16_FrzIonGenerator_TotalOnMinutes;
static uint16_t u16_FrzIonGenerator_StartMinute;
static bool b_defrost_heater_state = false;
static bool b_vertical_beam_heater_state = false;

void Set_DefrostHeaterState(bool state)
{
    if(true == state)
    {
        IO_FRZ_DEFROST_HEATER_ENABLE;
        b_defrost_heater_state = true;
    }
    else
    {
        IO_FRZ_DEFROST_HEATER_DISABLE;
        b_defrost_heater_state = false;
    }
}

bool Get_DefrostHeaterState(void)
{
    return b_defrost_heater_state;
}

void Set_RefHeaterState(bool state)
{
    if(true == state)
    {
        IO_AC_REF_DEFROST_HEATER_ENABLE;
    }
    else
    {
        IO_AC_REF_DEFROST_HEATER_DISABLE;
    }
}

void Set_IonGeneratorState(bool state)
{
    if(true == state)
    {
        if(b_IonGenerator_En == false)
        {
            u16_IonGenerator_StartMinute = Get_MinuteCount();
            b_IonGenerator_En = true;
        }
        IO_LZ_LED_ENABLE;
    }
    else
    {
        if(b_IonGenerator_En)
        {
            b_IonGenerator_En = false;
            u16_IonGenerator_TotalOnMinutes += Get_MinuteElapsedTime(u16_IonGenerator_StartMinute);
        }
        IO_LZ_LED_DISABLE;
    }
}

bool Get_IonGeneratorState(void)
{
    return b_IonGenerator_En;
}

uint16_t Get_IonGeneratorOnMinutes(void)
{
    if(b_IonGenerator_En)
    {
        return u16_IonGenerator_TotalOnMinutes + Get_MinuteElapsedTime(u16_IonGenerator_StartMinute);
    }
    else
    {
        return u16_IonGenerator_TotalOnMinutes;
    }
}

void Clear_IonGeneratorOnMinutes(void)
{
    u16_IonGenerator_TotalOnMinutes = 0;
    u16_IonGenerator_StartMinute = Get_MinuteElapsedTime(u16_IonGenerator_StartMinute);
}

void Set_VerticalBeamHeaterState(bool state)
{
    bool b_ref_lamp = Get_GradualLampState(REF_SURFACE_LAMP);

    if((true == state) && (false == b_ref_lamp))
    {
        IO_CBX_LED_ENABLE;
        b_vertical_beam_heater_state = true;
    }
    else
    {
        IO_CBX_LED_DISABLE;
        b_vertical_beam_heater_state = false;
    }
}

bool Get_VerticalBeamHeaterDeviceState(void)
{
    return b_vertical_beam_heater_state;
}

void Set_FrzRightLamp(bool state)
{
    if(true == state)
    {
        IO_V_TOP_LED_ENABLE;
    }
    else
    {
        IO_V_TOP_LED_DISABLE;
    }
}

void Set_FrzLeftLamp(bool state)
{
    if(true == state)
    {
        IO_FRZ_LED_ENABLE;
    }
    else
    {
        IO_FRZ_LED_DISABLE;
    }
}


void Set_VarDamperHeaterState(bool state)
{
    if(true == state)
    {
        IO_RDAMP_HEATER_ENABLE;
    }
    else
    {
        IO_RDAMP_HEATER_DISABLE;
    }
}

void Set_FrzIonGeneratorState(bool state)
{
    if(true == state)
    {
        if(b_FrzIonGenerator_En == false)
        {
            u16_FrzIonGenerator_StartMinute = Get_MinuteCount();
            b_FrzIonGenerator_En = true;
        }
        IO_VDAMP_HEATER_ENABLE;
    }
    else
    {
        if(b_FrzIonGenerator_En)
        {
            b_FrzIonGenerator_En = false;
            u16_FrzIonGenerator_TotalOnMinutes += Get_MinuteElapsedTime(u16_FrzIonGenerator_StartMinute);
        }
        IO_VDAMP_HEATER_DISABLE;
    }
}

bool Get_FrzIonGeneratorState(void)
{
    return b_FrzIonGenerator_En;
}

uint16_t Get_FrzIonGeneratorOnMinutes(void)
{
    if(b_FrzIonGenerator_En)
    {
        return u16_FrzIonGenerator_TotalOnMinutes + Get_MinuteElapsedTime(u16_FrzIonGenerator_StartMinute);
    }
    else
    {
        return u16_FrzIonGenerator_TotalOnMinutes;
    }
}

void Clear_FrzIonGeneratorOnMinutes(void)
{
    u16_FrzIonGenerator_TotalOnMinutes = 0;
    u16_FrzIonGenerator_StartMinute = Get_MinuteElapsedTime(u16_FrzIonGenerator_StartMinute);
}


