/*!
 * @file
 * @brief Assert implementation.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef CORE_ASSERT_H
#define CORE_ASSERT_H

#define ASSERT_RESET (0) // Assertion functionality with only Reset(without logging file info)
#define ASSERT_LOG_VC (1) // this is VC testing only,application firmware should not set it
#define ASSERT_DISABLE (2) // No Assertion functionality
#define ASSERT_LOG (3) // Assertion functionality with Logging available

#define ASSERT_TYPE (ASSERT_RESET)

#if(ASSERT_TYPE == ASSERT_DISABLE)

#define CORE_ASSERT(test_) \
    if(test_)              \
    {                      \
    }                      \
    else
{
}

#elif(ASSERT_TYPE == ASSERT_LOG_VC)

extern void OnAssert__(char const *file, unsigned line);

#define DEFINE_THIS_FILE \
    static const char sTHIS_FILE__[] = __FILE__

#define CORE_ASSERT(test_) \
    if(test_)
{
}
else
{
    OnAssert__(sTHIS_FILE__, __LINE__);
}

#else

extern void OnAssert__(void);

#define CORE_ASSERT(test_) \
    if(test_)              \
    {                      \
    }                      \
    else                   \
    {                      \
        OnAssert__();      \
    }

#endif

#define ENSURE(test_) CORE_ASSERT(test_)

#endif
