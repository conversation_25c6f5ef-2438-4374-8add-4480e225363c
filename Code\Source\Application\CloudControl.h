/*!
 * @file
 * @brief This file defines public constants, types and functions for the cooling controller.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef _CLOUD_CONTROL_H
#define _CLOUD_CONTROL_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include <stdlib.h>

#define U16_PEEK_VALLEY_ON_MINUTES ((uint16_t)(60 * 24))
#define U16_PEEK_VALLEY_OFF_MINUTES ((uint16_t)(60 * 8))
#define U16_DOOR_OPEN_AI_CLOSE_TIME_SECOND (uint16_t)(3 * 60)
#define U16_NOISE_REDUCE_OFF_MINUTES ((uint16_t)(30))
#define U16_DOOR_OPEN_SECOND ((uint16_t)(180))
#define LINYUN_POWERPARAM_GAP_MINUTES (3)
#define LINYUN_POWERPARAM_GAP_COUNT   (2)
#define LINYUN_POWER_COMPON_MINUTES   (4)
#define LINYUN_POWER_EXIT_COMPON_MINUTES  (6 * 60)
#define LINYUN_RETRY_UPLOAD_MINUTES  ((uint16_t)(30))
#define LINYUN_POWERPARAM_UPLOAD_CYCLES (60)
#define LINYUN_POWERPARAM_UPLOAD_TIMEOUT (75)
#define STRONG_COOL_REF_SNR_OFF_TEMP_OFFSET (uint16_t)20
#define STRONG_MUTE_REF_FAN_DUTY (uint8_t)30
#define STRONG_MUTE_FRZ_FAN_DUTY (uint8_t)50
#define CLOUD_NET_OFFLINE_TIME (uint16_t)(3 * 60)
#define CLOUD_NET_ONLINE_TIME (uint16_t)(3)
#define CLOUD_NET_POLL_SECONDS (uint8_t)(60)
#define MUTE_NORMAL_HIGHLOAD_MINUTES (uint16_t)(2 * 60)
#define MUTE_COMPON_MAX_MINUTES (uint16_t)(6 * 60)
#define MUTE_DEEP_MINUTES (uint16_t)(8 * 60)
#define SMARTGRID_DELAY_MINUTES (uint16_t)(30)
#define SMARTGRID_DELAYLOAD_MINUTES (uint16_t)(4 * 60)
#define SMARTGRID_REDUCELOAD_MINUTES (uint16_t)(30)
#define SMARTGRID_REDUCELOAD_COMPON_MINUTES (uint16_t)(7)
#define SMARTGRID_DELAYLOAD_ONOFF_OFFSET (uint16_t)15

#define U16_APP_ION_ENABLE_TOTAL_CYCLES ((uint16_t)(20))
#define U16_APP_REF_ION_CYCLE_SECOND ((uint16_t)(180))
#define U16_APP_REF_ION_CYCLE_ONSECOND ((uint16_t)(60))
#define U16_APP_REF_ION_CYCLE_OFFSECOND ((uint16_t)(120))
#define U16_APP_FRZ_ION_CYCLE_SECOND ((uint16_t)(180))
#define U16_APP_FRZ_ION_CYCLE_ONSECOND ((uint16_t)(60))
#define U16_APP_FRZ_ION_CYCLE_OFFSECOND ((uint16_t)(120))

#define LINYUN_PRESERVE_UPLOAD_CYCLE (uint16_t)15

typedef enum{
    LINYUN_POWER_PARAM_STATE = 0,
    LINYUN_POWER_PARAM_COMPFREQ,
    LINYUN_POWER_PARAM_VALVE,
    LINYUN_POWER_PARAM_FRZFAN,
    LINYUN_POWER_PARAM_REFFAN,
    LINYUN_POWER_PARAM_COOLFAN,
    LINYUN_POWER_PARAM_VARDAMPER,
    LINYUN_POWER_PARAM_REFDAMPER,
    LINYUN_POWER_PARAM_REFDAMPER1,
    LINYUN_POWER_PARAM_COUNT
}linyun_power_param_e;

typedef struct{
    int8_t state;
    uint8_t freq;
    uint8_t valve;
    uint8_t frzfan;
    uint8_t reffan;
    uint8_t coolfan;
    uint8_t vardamper;
    uint8_t refdamper;
    uint8_t refdamper1;
} linyun_power_param_st;

typedef enum{
    LINYUN_MUTE_LOCAL = 0,
    LINYUN_MUTE_NORMAL,
    LINYUN_MUTE_DEEP,
    LINYUN_MUTE_MAX
}linyun_mute_mode_e;

typedef enum{
    SMARTGRID_LOAD_NONE = 0,
    SMARTGRID_LOAD_DELAY,
    SMARTGRID_LOAD_REDUCE,
    SMARTGRID_LOAD_MAX
}smartgrid_load_mode_e;

void CloudControlFunc(void);
void Set_PeekValleyFrzSet(uint8_t u8_SetTemp);
void Set_PeekValleyRefSet(uint8_t u8_SetTemp);
void Clean_PeekValleyFrzSet(void);
void Clean_PeekValleyRefSet(void);
uint8_t GetPeekValleyPowerFrzTemp(void);
uint8_t GetPeekValleyPowerRefTemp(void);
uint8_t GetPowerSaveFrzTemp(void);
uint8_t Get_FrzIonEnable(void);
void Set_FrzIonEnable(uint8_t enable);
uint8_t Get_RefIonEnable(void);
void Set_RefIonEnable(uint8_t enable);
uint8_t Get_LinYunPowerSave(void);
int Set_LinYunPowerParam(char *paramstrs);
void Disable_LinYunPowerSave(void);
linyun_power_param_st *Get_LinYunPowerParam(void);
int UploadLinYunPowerParam(char *pResult);
bool IsLinYunPowerHighLoad(void);
void Set_Strong_Cool(uint8_t enable);
bool Get_Strong_Cool(void);
void Set_Strong_Mute(uint8_t enable);
bool Get_Strong_Mute(void);
bool Get_Strong_Mute_Config(void);
void Set_Mute_Mode(uint8_t mode);
uint8_t Get_Mute_Mode(void);
uint8_t Get_SmartGrid_Deforst(void);
void Set_SmartGrid_Deforst(uint8_t enable);
uint8_t Get_SmartGrid_Forbid_Deforst();
void Set_SmartGrid_DelayLoad(uint8_t enable);
uint8_t Get_SmartGrid_DelayLoad(void);
void Set_SmartGrid_ReduceLoad(uint8_t enable);
uint8_t Get_SmartGrid_ReduceLoad(void);
uint8_t Get_SmartGrid_Forbid_LoadDeforst(void);
uint8_t Is_SmartGrid_DelayLoadOn(void);
uint8_t Is_SmartGrid_ReduceLoadOn(void);
void Disable_SmartGridFunc(void);
uint8_t GetSmartGridLoadMode(void);
void SetSmartGridLoadMode(uint8_t mode);
uint8_t GetLinYunPreserveCool(void);
void ClearLinYunPreserveCool(void);
uint8_t Is_SmartGrid_ReduceLoadCool(void);
void Disable_SmartGrid_ReduceLoadCool(void);
#endif

