/*!
 * @file
 * @brief This module covers the complete fan functionality.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "DisplayInterface.h"
#include "Parameter_TemperatureZone.h"
#include "Crc16_CCITT_FALSE.h"
#include "Core_CallBackTimer.h"
#include "Adpt_Flash.h"
#include "Driver_Flash.h"
#include "Adpt_Iwdg.h"
#include "Init_Mcu.h"
#include "Core_Types.h"
#include "syslog.h"
#include "SystemManager.h"
#include "FirewareComm.h"
#include "Sbus_IceMaker.h"
#include "CloudControl.h"
#include "miio_api.h"
#include "Iot_SpecHandler.h"
#include "ParameterManager.h"
#include "Crc16_CCITT_FALSE.h"

static void UpdateProductProject(void);

static ota_param_st *ota_zone = (ota_param_st *)BOOT_PARA_ADDRESS;
static ota_param_st ota_zone_cache;
static param_manager_st param_manager[PARAMTYPE_MAX];
static sys_param_st sysparam_latest;
static sys_param_st sysparam_history;
static sys_param_st sysparam_default = {
    .inspection = 0,
    .ref_temp = REF_LEVEL_5,
    .frz_temp = FRZ_LEVEL_F18,
    .infant_mode = eRefVar_FreshMeat,
    .user_mode = eManual_Mode,
    .linyun_power = 0,
    .linyun_mute = 0,
    .icemaker_func = eIceMaker_Stop_Mode,
    .peek_valley_power = 0,
    .icemaker_reserve_0 = 0,
    .icemaker_reserve_1 = 0,
    .icemaker_reserve_2 = 0,
    .icemaker_reserve_3 = 0,
    .icemaker_volume = 0,
    .ref_disable = 0,
    .smart_grid_deforst = 0,
    .ref_turbocool = 0,
    .frz_deepfreeze = 0,
};

static uint16_t maintenance_sections_default[MAINTENANCE_SECTION_NUM][MAINTENANCE_SECTION_PARAMETERS];
static uint16_t maintenance_sections_latest[MAINTENANCE_SECTION_NUM][MAINTENANCE_SECTION_PARAMETERS];
static app_promote_st *app_promote = (app_promote_st *)APP_PROMOTE_ADDR;
static st_CoreCallbackTimer app_promote_timer;
static product_sn_st *product_sn = (product_sn_st *)FACTORY_ADDRESS;
static product_sn_manager_st product_sn_manager;

static void StartAppPromoteTimer(uint16_t tickSec)
{
    Core_CallbackTimer_TimerStart(
        &app_promote_timer,
        ClearAppPromoteCount,
        tickSec,
        0,
        eCallbackTimer_Type_OneShot,
        eCallbackTimer_Priority_Normal);
}

static bool CheckAppPromoteCrc(void)
{
    uint16_t crc = Cal_CRC_MultipleData((uint8_t *)app_promote, offsetof(app_promote_st, crc16));
    if(app_promote->crc16 == crc)
    {
        return true;
    }
    return false;
}

static void UpdateAppPromoteCrc(void)
{
    app_promote->crc16 = Cal_CRC_MultipleData((uint8_t *)app_promote, offsetof(app_promote_st, crc16));
}

static void Init_AppPromote(void)
{
    if(app_promote->magic != OTA_MAGIC || CheckAppPromoteCrc() == false)
    {
        app_promote->magic = OTA_MAGIC;
        app_promote->count = 0;
        UpdateAppPromoteCrc();
    }
    StartAppPromoteTimer(APP_PROMOTE_TIMER);
}

static bool ParamValid(void *param, uint16_t size)
{
    uint16_t crc16;
    uint16_t pcrc;

    memcpy(&pcrc, param + size, sizeof(uint16_t));
    crc16 = Cal_CRC_MultipleData(param, size);
    if(crc16 == pcrc)
    {
        return true;
    }
    return false;
}

static bool ParamWriteable(void *param, uint16_t len)
{
    uint8_t buf[4];
    uint8_t *temp;

    temp = (uint8_t *)param;
    memset(buf, FLASH_ERASE_STATE, sizeof(buf));

    while(len)
    {
        if(memcmp(temp, buf, MIN(len, 4)))
        {
            return false;
        }
        temp += MIN(len, 4);
        len -= MIN(len, 4);
    }
    return true;
}

static void *GetParamBlockUpdate(param_manager_st *pm)
{
    void *param;
    uint16_t psize;
    uint32_t offset = 0;

    psize = pm->param_size + PARM_CRC_LENGTH;
    offset += sizeof(param_header_st);
    param = (void *)(pm->start_addr + pm->blockno * FLASH_SECTOR_SIZE + sizeof(param_header_st));

    while(offset + psize <= FLASH_SECTOR_SIZE)
    {
        if(ParamWriteable(param, psize))
        {
            return param;
        }
        param += psize;
        offset += psize;
    }
    return NULL;
}

static void FormatParamBlock(param_manager_st *pm, uint16_t block)
{
    uint32_t addr;
    param_header_st hdr;

    hdr.magic = pm->magic;
    hdr.version = pm->version;
    hdr.expired = PARM_NOTEXPIRED;
    addr = pm->start_addr + block * FLASH_SECTOR_SIZE;
    FlashSectorErase(addr);
    FlashWriteBytes(addr, (uint8_t *)&hdr, sizeof(param_header_st));
}

static void ParamBlockExpire(param_manager_st *pm)
{
    uint16_t crc;
    uint32_t val;
    uint32_t addr;
    uint16_t hblock;
    uint32_t *expired;

    hblock = pm->blockno;
    addr = pm->start_addr + pm->blockno * FLASH_SECTOR_SIZE + offsetof(param_header_st, expired);
    expired = (uint32_t *)addr;
    if(*expired == PARM_NOTEXPIRED)
    {
        val = PARM_HISTORY;
        FlashWriteBytes(addr, (uint8_t *)&val, sizeof(uint32_t));
    }
    pm->blockno = (pm->blockno + 1) % pm->blocks;
    FormatParamBlock(pm, pm->blockno);
    pm->update = GetParamBlockUpdate(pm);
    if(pm->update)
    {
        crc = Cal_CRC_MultipleData(pm->latest, pm->param_size);
        FlashWriteBytes((uint32_t)pm->update, pm->latest, pm->param_size);
        pm->update += pm->param_size;
        FlashWriteBytes((uint32_t)pm->update, (uint8_t *)&crc, sizeof(crc));
        pm->update = GetParamBlockUpdate(pm);
    }

    if(hblock != pm->blockno)
    {
        val = PARM_EXPIRED;
        FlashWriteBytes(addr, (uint8_t *)&val, sizeof(uint32_t));
    }
}

static void ParamStartup(param_manager_st *pm)
{
    void *param;
    uint16_t psize;
    uint16_t block;
    uint8_t found = 0;
    uint32_t offset = 0;
    param_header_st *header;

    for(block = 0; block < pm->blocks; block++)
    {
        header = (param_header_st *)(pm->start_addr + block * FLASH_SECTOR_SIZE);
        if(header->magic == pm->magic && header->version == pm->version &&
            header->expired == PARM_HISTORY)
        {
            found = 1;
            pm->blockno = block;
            break;
        }
    }

    if(!found)
    {
        for(block = 0; block < pm->blocks; block++)
        {
            header = (param_header_st *)(pm->start_addr + block * FLASH_SECTOR_SIZE);
            if(header->magic == pm->magic && header->version == pm->version &&
                header->expired == PARM_NOTEXPIRED)
            {
                found = 1;
                pm->blockno = block;
                break;
            }
        }

        if(!found)
        {
            FormatParamBlock(pm, 0);
        }
    }
    param = (void *)(pm->start_addr + pm->blockno * FLASH_SECTOR_SIZE + sizeof(param_header_st));
    offset += sizeof(param_header_st);
    psize = pm->param_size + PARM_CRC_LENGTH;
    found = 0;
    while(offset + psize <= FLASH_SECTOR_SIZE)
    {
        if(ParamWriteable(param, psize))
        {
            if(offset > sizeof(param_header_st) && ParamValid(param - psize, pm->param_size))
            {
                memcpy(pm->latest, param - psize, pm->param_size);
            }
            else
            {
                memcpy(pm->latest, pm->defparam, pm->param_size);
                pm->is_default = true;
            }
            pm->update = param;
            found = 1;
            break;
        }

        if(ParamValid(param, pm->param_size) && pm->history != NULL)
        {
            memcpy(pm->history, param, pm->param_size);
            pm->has_history = true;
        }
        param += psize;
        offset += psize;
    }

    if(!found)
    {
        if(ParamValid(param - psize, pm->param_size))
        {
            memcpy(pm->latest, param - psize, pm->param_size);
        }
        else
        {
            memcpy(pm->latest, pm->defparam, pm->param_size);
            pm->is_default = true;
        }
        ParamBlockExpire(pm);
    }
}

static void Init_SysParam(void)
{
    param_manager[PARAMTYPE_SYSPARAM].blockno = 0;
    param_manager[PARAMTYPE_SYSPARAM].blocks = PARAM_SIZE / FLASH_SECTOR_SIZE;
    param_manager[PARAMTYPE_SYSPARAM].start_addr = PARAM_ADDRESS;
    param_manager[PARAMTYPE_SYSPARAM].latest = &sysparam_latest;
    param_manager[PARAMTYPE_SYSPARAM].history = &sysparam_history;
    param_manager[PARAMTYPE_SYSPARAM].defparam = &sysparam_default;
    param_manager[PARAMTYPE_SYSPARAM].param_size = sizeof(sys_param_st);
    param_manager[PARAMTYPE_SYSPARAM].magic = SYSPARM_MAGIC;
    param_manager[PARAMTYPE_SYSPARAM].version = SYSPARM_VERSION;
    param_manager[PARAMTYPE_SYSPARAM].cycles_100ms = SYSPARM_CYCLES;
    param_manager[PARAMTYPE_SYSPARAM].count_100ms = 0;

    ParamStartup(&param_manager[PARAMTYPE_SYSPARAM]);
    if(param_manager[PARAMTYPE_SYSPARAM].is_default &&
        param_manager[PARAMTYPE_SYSPARAM].has_history)
    {
        sysparam_latest.inspection = sysparam_history.inspection;
    }
    Set_FactoryEntryNumber(sysparam_latest.inspection);
    Update_RefSetTemp(sysparam_latest.ref_temp);
    Update_FrzSetTemp(sysparam_latest.frz_temp);
    Update_RefVarSetTemp(sysparam_latest.infant_mode);
    Set_UserMode(sysparam_latest.user_mode);
    Set_RefTurboCool(sysparam_latest.ref_turbocool);
    Set_FrzDeepFreeze(sysparam_latest.frz_deepfreeze);
    Set_RefDisable(sysparam_latest.ref_disable);
}

static void SaveParam(param_manager_st *pm)
{
    uint16_t crc;
    uint32_t val;
    uint32_t addr;

    if(NULL == pm->update)
    {
        ParamBlockExpire(pm);
        return;
    }
    crc = Cal_CRC_MultipleData(pm->latest, pm->param_size);
    FlashWriteBytes((uint32_t)pm->update, pm->latest, pm->param_size);
    pm->update += pm->param_size;
    FlashWriteBytes((uint32_t)pm->update, (uint8_t *)&crc, sizeof(crc));
    val = PARM_HISTORY;
    addr = pm->start_addr + pm->blockno * FLASH_SECTOR_SIZE + offsetof(param_header_st, expired);
    FlashWriteBytes(addr, (uint8_t *)&val, sizeof(uint32_t));
    pm->update = GetParamBlockUpdate(pm);
}

static void RecoveryParam(param_type_e pt)
{
    void *update;
    param_manager_st *pm;

    if(pt >= PARAMTYPE_MAX || pt < 0)
    {
        err("param type (%d) is invaild\n", pt);
        return;
    }

    pm = &param_manager[pt];
    pm->dirty = 0;
    pm->count_100ms = 0;
    if(pm->history)
    {
        memcpy(pm->history, pm->latest, pm->param_size);
    }
    memcpy(pm->latest, pm->defparam, pm->param_size);
}

static bool CheckProductSnCrc(void)
{
    uint16_t crc = Cal_CRC_MultipleData((uint8_t *)(&product_sn_manager.sn), offsetof(product_sn_st, crc16));
    if(product_sn_manager.sn.crc16 == crc)
    {
        return true;
    }
    return false;
}

static void Init_ProductSn(void)
{
    memset(&product_sn_manager, 0, sizeof(product_sn_manager_st));
    memcpy(&product_sn_manager.sn, product_sn, sizeof(product_sn_st));
    product_sn_manager.exist = false;
    if(product_sn_manager.sn.magic == PRODUCT_SN_MAGIC && true == CheckProductSnCrc())
    {
        UpdateProductProject();
    }
}

void Init_Maintenance(uint8_t *mdata)
{
    uint8_t index;
    uint16_t data;
    uint8_t section;
    uint16_t paramval;
    uint8_t paramcount;
    uint16_t crc = U16_CRC_INITIAL_VALUE;

    for(section = 0; section < MAINTENANCE_SECTION_NUM; section++)
    {
        for(index = 0; index < MAINTENANCE_SECTION_PARAMETERS; index++)
        {
            data = (*mdata) << 8;
            mdata++;
            data |= *mdata;
            mdata++;
            maintenance_sections_default[section][index] = data;
        }
    }

    for(index = 0; index < MAINTENANCE_SECTION_NUM; index++)
    {
        section = PARAMTYPE_MAINTENANCE_SECTION1 + index;
        param_manager[section].blockno = 0;
        param_manager[section].blocks = MAINTENANCE_SECTION_SIZE / FLASH_SECTOR_SIZE;
        param_manager[section].start_addr = MAINTENANCE_SECTION_ADDRESS + index * MAINTENANCE_SECTION_SIZE;
        param_manager[section].latest = maintenance_sections_latest[index];
        param_manager[section].history = NULL;
        param_manager[section].defparam = maintenance_sections_default[index];
        param_manager[section].param_size = sizeof(uint16_t) * MAINTENANCE_SECTION_PARAMETERS;
        param_manager[section].magic = SYSPARM_MAGIC;
        param_manager[section].version = SYSPARM_VERSION;
        param_manager[section].cycles_100ms = 0;
        param_manager[section].count_100ms = 0;
        ParamStartup(&param_manager[section]);
    }

     for(index = 0; index < MAINTENANCE_SECTION_NUM; index++)
     {
        paramcount = 0;
        if(index == 0)
        {
            paramcount = 1;
        }

        for( ; paramcount < MAINTENANCE_SECTION_PARAMETERS; paramcount++)
        {
            data = maintenance_sections_latest[index][paramcount];
            crc = Cal_CRC_SingleData(crc, (data >> 8) & 0xFF);
            crc = Cal_CRC_SingleData(crc, (data & 0xFF));
        }
     }

     if(maintenance_sections_latest[0][0] != crc)
     {
        for(index = 0; index < MAINTENANCE_SECTION_NUM; index++)
        {
            section = PARAMTYPE_MAINTENANCE_SECTION1 + index;
            RecoveryParam(section);
            SaveParam(&param_manager[section]);
        }
     }
}


void Init_Flash(void)
{
    memcpy(&ota_zone_cache, ota_zone, sizeof(ota_param_st));
    Init_SysParam();
    Init_AppPromote();
    Init_ProductSn();
}

uint32_t GetMcuVersion(void)
{
    if(ota_zone_cache.ota_magic != OTA_MAGIC ||
       (ota_zone_cache.ota_flag != OTA_DONE_FLAG &&
        ota_zone_cache.ota_flag != OTA_START_FLAG &&
        ota_zone_cache.ota_flag != OTA_TESTUART_FLAG) ||
        ota_zone_cache.ota_version > MCU_MAX_VERSION)
    {
        if (OTA_FACTORY_VERSION > 2)
        {
            return (uint32_t)(OTA_FACTORY_VERSION - 1);
        }
        else
        {
            return (uint32_t)OTA_FACTORY_VERSION;
        }
    }
    else
    {
        return ota_zone_cache.ota_version;
    }
}

void SetOtaFlag(bool quite)
{
    en_result_t res;
    uint32_t version;

    version = GetMcuVersion();
    debug("set ota flag start!");
    ota_zone_cache.ota_magic = OTA_MAGIC;
    ota_zone_cache.ota_flag = OTA_START_FLAG;
    ota_zone_cache.quite_flag = OTA_NORMAL_FLAG;
    if(quite)
    {
        ota_zone_cache.quite_flag = OTA_QUITE_FLAG;
    }
    ota_zone_cache.ota_version = version;
    res = FlashSectorErase(BOOT_PARA_ADDRESS);
    debug("FlashSectorErase %lx, %d\n", BOOT_PARA_ADDRESS, res);
    res = FlashWriteBytes(BOOT_PARA_ADDRESS, (uint8_t *)&ota_zone_cache, sizeof(ota_zone_cache));
    debug("FlashWriteBytes %lx, %d\n", BOOT_PARA_ADDRESS, res);
    debug("set ota flag success!\n");
    return;
}

void SetTestUartOtaFlag(void)
{
    en_result_t res;
    uint32_t version;

    version = GetMcuVersion();
    debug("set ota flag start!");
    ota_zone_cache.ota_magic = OTA_MAGIC;
    ota_zone_cache.ota_flag = OTA_TESTUART_FLAG;
    ota_zone_cache.quite_flag = OTA_NORMAL_FLAG;
    ota_zone_cache.ota_version = version;
    res = FlashSectorErase(BOOT_PARA_ADDRESS);
    debug("FlashSectorErase %lx, %d\n", BOOT_PARA_ADDRESS, res);
    res = FlashWriteBytes(BOOT_PARA_ADDRESS, (uint8_t *)&ota_zone_cache, sizeof(ota_zone_cache));
    debug("FlashWriteBytes %lx, %d\n", BOOT_PARA_ADDRESS, res);
    debug("set ota flag success!\n");
    return;
}

bool IsQuiteOtaBoot(void)
{
    uint32_t val;
    uint32_t addr;

    if(ota_zone_cache.ota_magic == OTA_MAGIC &&
        ota_zone_cache.ota_flag == OTA_DONE_FLAG &&
        ota_zone_cache.quite_flag == OTA_QUITE_FLAG)
    {
        if(ota_zone->quite_flag == OTA_QUITE_FLAG)
        {
            val = OTA_NORMAL_FLAG;
            addr = BOOT_PARA_ADDRESS + offsetof(ota_param_st, quite_flag);
            FlashWriteBytes(addr, (uint8_t *)&val, sizeof(uint32_t));
        }
        return true;
    }
    return false;
}

void ClearOtaParam(void)
{
    FlashSectorErase(BOOT_PARA_ADDRESS);
}

void UpdateSysParam(void)
{
    void *update;
    param_manager_st *pm;
    param_type_e pt = PARAMTYPE_SYSPARAM;

    for(; pt < PARAMTYPE_MAX; pt++)
    {
        pm = &param_manager[pt];
        if(pm->cycles_100ms > 0 &&
            ++pm->count_100ms >= pm->cycles_100ms)
        {
            pm->count_100ms = 0;
            if(pm->dirty)
            {
                pm->dirty = 0;
                SaveParam(pm);
            }
        }
    }
    return;
}

void RecoverySysParam(void)
{
    RecoveryParam(PARAMTYPE_SYSPARAM);
    SaveParam(&param_manager[PARAMTYPE_SYSPARAM]);
    Set_FactoryEntryNumber(sysparam_latest.inspection);
    Update_RefSetTemp(sysparam_latest.ref_temp);
    Update_FrzSetTemp(sysparam_latest.frz_temp);
    Update_RefVarSetTemp(sysparam_latest.infant_mode);
    Set_UserMode(sysparam_latest.user_mode);
    Set_RefTurboCool(sysparam_latest.ref_turbocool);
    Set_FrzDeepFreeze(sysparam_latest.frz_deepfreeze);
    Set_RefDisable(sysparam_latest.ref_disable);
}

void RecoveryUsrSysParam(void)
{
    RecoveryParam(PARAMTYPE_SYSPARAM);
    sysparam_latest.inspection = sysparam_history.inspection;
    SaveParam(&param_manager[PARAMTYPE_SYSPARAM]);
    Set_FactoryEntryNumber(sysparam_latest.inspection);
    Update_RefSetTemp(sysparam_latest.ref_temp);
    Update_FrzSetTemp(sysparam_latest.frz_temp);
    Update_RefVarSetTemp(sysparam_latest.infant_mode);
    Set_UserMode(sysparam_latest.user_mode);
    Set_RefTurboCool(sysparam_latest.ref_turbocool);
    Set_FrzDeepFreeze(sysparam_latest.frz_deepfreeze);
    Set_RefDisable(sysparam_latest.ref_disable);
    trig_system_reset();
}

void RecoveryFactoryParam(void)
{
    RecoveryParam(PARAMTYPE_SYSPARAM);
    sysparam_latest.inspection = sysparam_history.inspection;
    SaveParam(&param_manager[PARAMTYPE_SYSPARAM]);
    Set_FactoryEntryNumber(sysparam_latest.inspection);
    Update_RefSetTemp(sysparam_latest.ref_temp);
    Update_FrzSetTemp(sysparam_latest.frz_temp);
    Update_RefVarSetTemp(sysparam_latest.infant_mode);
    Set_UserMode(sysparam_latest.user_mode);
    Set_RefTurboCool(sysparam_latest.ref_turbocool);
    Set_FrzDeepFreeze(sysparam_latest.frz_deepfreeze);
    Set_RefDisable(sysparam_latest.ref_disable);
}

int GetSysParam(sysparam_type_e type, uint8_t *val)
{
    switch(type)
    {
        case SYSPARAM_INSPECTION:
            *val = sysparam_latest.inspection;
            break;
        case SYSPARAM_REFTEMP:
            *val = sysparam_latest.ref_temp;
            break;
        case SYSPARAM_FRZTEMP:
            *val = sysparam_latest.frz_temp;
            break;
        case SYSPARAM_INFANT_MODE:
            *val = sysparam_latest.infant_mode;
            break;
        case SYSPARAM_USER_MODE:
            *val = sysparam_latest.user_mode;
            break;
        case SYSPARAM_LINGYUN_POWER:
            *val = sysparam_latest.linyun_power;
            break;
        case SYSPARAM_LINGYUN_MUTE:
            *val = sysparam_latest.linyun_mute;
            break;
        case SYSPARAM_ICEMAKER_FUNC:
            *val = sysparam_latest.icemaker_func;
            break;
        case SYSPARAM_PEEK_VALLEY_POWER:
            *val = sysparam_latest.peek_valley_power;
            break;
        case SYSPARAM_ICEMAKER_RESERVE0:
            *val = sysparam_latest.icemaker_reserve_0;
            break;
        case SYSPARAM_ICEMAKER_RESERVE1:
            *val = sysparam_latest.icemaker_reserve_1;
            break;
        case SYSPARAM_ICEMAKER_RESERVE2:
            *val = sysparam_latest.icemaker_reserve_2;
            break;
        case SYSPARAM_ICEMAKER_RESERVE3:
            *val = sysparam_latest.icemaker_reserve_3;
            break;
        case SYSPARAM_ICEMAKER_VOLUME:
            *val = sysparam_latest.icemaker_volume;
            break;
        case SYSPARAM_REF_DISABLE:
            *val = sysparam_latest.ref_disable;
            break;
        case SYSPARAM_SMARTGRID_DEFORST:
            *val = sysparam_latest.smart_grid_deforst;
            break;
        case SYSPARAM_REFTURBOCOOL:
            *val = sysparam_latest.ref_turbocool;
            break;
        case SYSPARAM_FRZDEEPFREEZE:
            *val = sysparam_latest.frz_deepfreeze;
            break;
        default:
            return -1;
    }
    return 0;
}

int SetSysParam(sysparam_type_e type, uint8_t val)
{
    switch(type)
    {
        case SYSPARAM_INSPECTION:
            if(sysparam_latest.inspection != val)
            {
                sysparam_latest.inspection = val;
                SaveParam(&param_manager[PARAMTYPE_SYSPARAM]);
                param_manager[PARAMTYPE_SYSPARAM].dirty = 0;
            }
            break;
        case SYSPARAM_REFTEMP:
            if(sysparam_latest.ref_temp != val)
            {
                sysparam_latest.ref_temp = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_FRZTEMP:
            if(sysparam_latest.frz_temp != val)
            {
                sysparam_latest.frz_temp = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_INFANT_MODE:
            if(sysparam_latest.infant_mode != val)
            {
                sysparam_latest.infant_mode = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_USER_MODE:
            if(sysparam_latest.user_mode != val)
            {
                sysparam_latest.user_mode = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_LINGYUN_POWER:
            if(sysparam_latest.linyun_power != val)
            {
                sysparam_latest.linyun_power = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_LINGYUN_MUTE:
            if(sysparam_latest.linyun_mute != val)
            {
                sysparam_latest.linyun_mute = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_ICEMAKER_FUNC:
            if(sysparam_latest.icemaker_func != val)
            {
                sysparam_latest.icemaker_func = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_PEEK_VALLEY_POWER:
            if(sysparam_latest.peek_valley_power != val)
            {
                sysparam_latest.peek_valley_power = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_ICEMAKER_RESERVE0:
            if(sysparam_latest.icemaker_reserve_0 != val)
            {
                sysparam_latest.icemaker_reserve_0 = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_ICEMAKER_RESERVE1:
            if(sysparam_latest.icemaker_reserve_1 != val)
            {
                sysparam_latest.icemaker_reserve_1 = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_ICEMAKER_RESERVE2:
            if(sysparam_latest.icemaker_reserve_2 != val)
            {
                sysparam_latest.icemaker_reserve_2 = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_ICEMAKER_RESERVE3:
            if(sysparam_latest.icemaker_reserve_3 != val)
            {
                sysparam_latest.icemaker_reserve_3 = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }

            break;
        case SYSPARAM_ICEMAKER_VOLUME:
            if(sysparam_latest.icemaker_volume != val)
            {
                sysparam_latest.icemaker_volume = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_REF_DISABLE:
            if(sysparam_latest.ref_disable != val)
            {
                sysparam_latest.ref_disable = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_SMARTGRID_DEFORST:
            if(sysparam_latest.smart_grid_deforst != val)
            {
                sysparam_latest.smart_grid_deforst = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;               
            }
            break;
        case SYSPARAM_REFTURBOCOOL:
            if(sysparam_latest.ref_turbocool != val)
            {
                sysparam_latest.ref_turbocool = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;  
            }
            break;
        case SYSPARAM_FRZDEEPFREEZE:
            if(sysparam_latest.frz_deepfreeze != val)
            {
                sysparam_latest.frz_deepfreeze = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;  
            }
            break;
        default:
            return -1;
    }

    return 0;
}

int GetSysParamFromFlash(sysparam_type_e type, uint8_t *val)
{
    void *param;
    uint16_t psize;
    uint16_t block;
    uint8_t found = 0;
    uint32_t offset = 0;
    sys_param_st *sp = NULL;
    param_header_st *header;
    param_manager_st * pm = &param_manager[PARAMTYPE_SYSPARAM];

    param = (void *)(pm->start_addr + pm->blockno * FLASH_SECTOR_SIZE + sizeof(param_header_st));
    offset += sizeof(param_header_st);
    psize = pm->param_size + PARM_CRC_LENGTH;
    found = 0;
    while(offset + psize <= FLASH_SECTOR_SIZE)
    {
        if(ParamWriteable(param, psize))
        {
            break;
        }
        sp = param;
        param += psize;
        offset += psize;
    }

    if(sp == NULL || ParamValid(sp, pm->param_size) == false)
    {
        return -1;
    }

    switch(type)
    {
        case SYSPARAM_INSPECTION:
            *val = sp->inspection;
            break;
        case SYSPARAM_REFTEMP:
            *val = sp->ref_temp;
            break;
        case SYSPARAM_FRZTEMP:
            *val = sp->frz_temp;
            break;
        case SYSPARAM_INFANT_MODE:
            *val = sp->infant_mode;
            break;
        case SYSPARAM_USER_MODE:
            *val = sp->user_mode;
            break;
        case SYSPARAM_LINGYUN_POWER:
            *val = sp->linyun_power;
            break;
        case SYSPARAM_LINGYUN_MUTE:
            *val = sp->linyun_mute;
            break;
        case SYSPARAM_ICEMAKER_FUNC:
            *val = sp->icemaker_func;
            break;
        case SYSPARAM_PEEK_VALLEY_POWER:
            *val = sp->peek_valley_power;
            break;
        case SYSPARAM_ICEMAKER_RESERVE0:
            *val = sp->icemaker_reserve_0;
            break;
        case SYSPARAM_ICEMAKER_RESERVE1:
            *val = sp->icemaker_reserve_1;
            break;
        case SYSPARAM_ICEMAKER_RESERVE2:
            *val = sp->icemaker_reserve_2;
            break;
        case SYSPARAM_ICEMAKER_RESERVE3:
            *val = sp->icemaker_reserve_3;
            break;
        case SYSPARAM_ICEMAKER_VOLUME:
            *val = sp->icemaker_volume;
            break;
        case SYSPARAM_REF_DISABLE:
            *val = sp->ref_disable;
            break;
        case SYSPARAM_SMARTGRID_DEFORST:
            *val = sp->smart_grid_deforst;
            break;
        case SYSPARAM_REFTURBOCOOL:
            *val = sp->ref_turbocool;
            break;
        case SYSPARAM_FRZDEEPFREEZE:
            *val = sp->frz_deepfreeze;
            break;
        default:
            return -1;
    }
    return 0;
}

uint32_t GetAppPromoteCount(void)
{
    return app_promote->count;
}

void ClearAppPromoteCount(void)
{
    info("app is promote\n");
    app_promote->count = 0;
    UpdateAppPromoteCrc();
}

uint32_t GetBootVersion(void)
{
    return app_promote->boot_version;
}

uint32_t GetBootCrc(void)
{
    return app_promote->boot_crc;
}

uint32_t GetAppVersion(void)
{
    app_imageheader_st header;
    unsigned char *buf;
    uint32_t len;

    memcpy(&header, (void *)(APP_ADDRESS - APP_HEADER_SIZE), sizeof(app_imageheader_st));
    header.magic = convert_from_bigendian32(header.magic);
    header.version = convert_from_bigendian32(header.version);
    if(header.magic == APP_MAGIC_NUM)
    {
        return header.version;
    }
    return 0;
}

uint32_t GetAppCrc(void)
{
    return app_promote->app_crc;
}

void SaveOfflineLog(void *buf, uint32_t len)
{
    uint32_t addr = PANIC_LOG_ADDRESS;
    uint32_t offset = 0;

    for(; offset < PANIC_LOG_SIZE; offset += FLASH_SECTOR_SIZE)
    {
        FlashSectorErase(addr + offset);
    }

    len = len > PANIC_LOG_SIZE ? PANIC_LOG_SIZE : len;
    offset = 0;
    while(len > 0)
    {
        if(len >= FLASH_SECTOR_SIZE)
        {
            FlashWriteBytes(addr + offset, buf + offset, FLASH_SECTOR_SIZE);
            len -= FLASH_SECTOR_SIZE;
            offset += FLASH_SECTOR_SIZE;
            continue;
        }
        FlashWriteBytes(addr + offset, buf + offset, len);
        len = 0;
    }
}

void SaveEeParam(void *buf, uint8_t packno)
{
    uint32_t addr = EEPARAM_ADDRESS;
    uint8_t ppb = FLASH_SECTOR_SIZE / FIREWARE_PARAM_PACKET_LEN;
    uint32_t offset = packno * FIREWARE_PARAM_PACKET_LEN;

    if(offset >= EEPARAM_SIZE)
    {
        return;
    }

    if(packno % ppb == 0)
    {
        FlashSectorErase(addr + offset);
    }

    FlashWriteBytes(addr + offset, buf, FIREWARE_PARAM_PACKET_LEN);
}

void ReadEeParam(void *buf, uint32_t len)
{
    void *addr = (void *)EEPARAM_ADDRESS;

    if(len > EEPARAM_SIZE)
    {
        return;
    }

    memcpy(buf, addr, len);
}

int GetMaintenanceParam(uint8_t index, uint16_t *val)
{
    uint16_t *data;

    if(index < PARAMTYPE_MAINTENANCE_SECTION1
       || index > PARAMTYPE_MAINTENANCE_SECTION8)
    {
        return -1;
    }

    data = param_manager[index].latest;
    memcpy(val, data, MAINTENANCE_SECTION_PARAMETERS * sizeof(uint16_t));
    return 0;
}

int SetMaintenanceParam(uint8_t index, uint16_t *val)
{
    uint16_t *data;

    if(index < PARAMTYPE_MAINTENANCE_SECTION1
       || index > PARAMTYPE_MAINTENANCE_SECTION8)
    {
        return -1;
    }

    data = param_manager[index].latest;
    if(memcmp(data, val, MAINTENANCE_SECTION_PARAMETERS * sizeof(uint16_t)) != 0)
    {
        memcpy(data, val, MAINTENANCE_SECTION_PARAMETERS * sizeof(uint16_t));
        SaveParam(&param_manager[index]);
    }

    return 0;
}

int GetMaintenanceSectionParam(uint8_t section, uint8_t offset, uint16_t *val)
{
    uint16_t *data;

    if(section < PARAMTYPE_MAINTENANCE_SECTION1
       || section > PARAMTYPE_MAINTENANCE_SECTION8
       || offset >= MAINTENANCE_SECTION_PARAMETERS)
    {
        return -1;
    }

    data = param_manager[section].latest;
    *val = data[offset];
    return 0;
}

#ifndef USER_MODEL
static const product_project_map_st product_project_maps[] = 
{
    {"bs52s", "midjd.fridge.bf52s1"},
};

static const product_project_map_st *SearchProjectModel(uint8_t *project)
{
    uint8_t index;
    uint8_t num;

    num = sizeof(product_project_maps) / sizeof(product_project_map_st);

    for(index = 0; index < num; index++)
    {
        if(strcmp((const char *)project, (const char *)product_project_maps[index].project) == 0)
        {
            return &product_project_maps[index];
        }
    }
    return NULL;
}

static void UpdateProductProject(void)
{
    uint8_t size;
    uint8_t index;
    char ch;
    const product_project_map_st *ppm = NULL;

    if(CheckSnFormat(product_sn_manager.sn.data, PRODUCT_SN_SIZE) == false)
    {
        return;
    }

    for( index = 0; index < PRODUCT_PROJECT_BYTES; index++)
    {
        ch = product_sn_manager.sn.data[PRODUCT_PROJECT_OFFSET + index];
        if(ch >= 65 && ch <= 90)
        {
            product_sn_manager.project[index] = ch + 32;
        }
        else
        {
            product_sn_manager.project[index] = ch;
        }
    }
    ppm = SearchProjectModel(product_sn_manager.project);
    if(ppm == NULL)
    {
        return;
    }
    strcpy((char *)product_sn_manager.user_model, (const char*)ppm->user_model);

    product_sn_manager.exist = true;
}

int8_t WriteProductSn(uint8_t *sn, uint8_t size, bool sync)
{
    en_result_t ret;
    uint8_t retry = 3;
    if(size < PRODUCT_SN_SIZE || sn[0] == 0)
    {
        return -1;
    }

    if(CheckSnFormat(sn, PRODUCT_SN_SIZE) == false)
    {
        return -1;
    }

    product_sn_manager.exist = false;
    product_sn_manager.sn.magic = PRODUCT_SN_MAGIC;
    memcpy(product_sn_manager.sn.data, sn, PRODUCT_SN_SIZE);
    UpdateProductProject();
    product_sn_manager.sn.crc16 = Cal_CRC_MultipleData((uint8_t *)(&product_sn_manager.sn), offsetof(product_sn_st, crc16));
    if(sync)
    {
        do
        {
            FlashSectorErase(FACTORY_ADDRESS);
            ret = FlashWriteBytes(FACTORY_ADDRESS, (uint8_t *)(&product_sn_manager.sn), sizeof(product_sn_st));
        }while(retry-- > 0 && ret != 0);

        if(ret != 0)
        {
            return -1;
        }
    }
    return 0;
}

int8_t ReadProductSn(uint8_t *sn, uint8_t size)
{
    if(size < PRODUCT_SN_SIZE || product_sn_manager.exist == false)
    {
        return -1;
    }

    memcpy(sn, product_sn_manager.sn.data, PRODUCT_SN_SIZE);
    return 0;
}

int8_t ReadProductUserModel(uint8_t *user_model, uint8_t size)
{
    if(product_sn_manager.exist == false || size < PRODUCT_MODEL_SIZE)
    {
        return -1;
    }

    memcpy(user_model, product_sn_manager.user_model, PRODUCT_MODEL_SIZE);
    return 0;
}

int8_t ReadProductModel(uint8_t *model, uint8_t size)
{
    uint8_t * user_model = NULL;
    if(product_sn_manager.exist == false || size < PRODUCT_MODEL_BYTES)
    {
        return -1;
    }
    user_model = (uint8_t *)product_sn_manager.user_model;
    user_model += strlen(PRODUCT_MODEL_PREFIX);
    strncpy((char *)model, (const char *)user_model, PRODUCT_MODEL_BYTES);
    return 0;
}

void ClearProductSn(void)
{
    FlashSectorErase(FACTORY_ADDRESS);
    memset(&product_sn_manager, 0, sizeof(product_sn_manager_st));
}
#else
static void UpdateProductProject(void)
{
    uint8_t size;
    uint8_t index;
    char ch;

    if(CheckSnFormat(product_sn_manager.sn.data, PRODUCT_SN_SIZE) == false)
    {
        return;
    }

    for( index = 0; index < PRODUCT_PROJECT_BYTES; index++)
    {
        ch = product_sn_manager.sn.data[PRODUCT_PROJECT_OFFSET + index];
        if(ch >= 65 && ch <= 90)
        {
            product_sn_manager.project[index] = ch + 32;
        }
        else
        {
            product_sn_manager.project[index] = ch;
        }
    }
    product_sn_manager.exist = true;
}

int8_t WriteProductSn(uint8_t *sn, uint8_t size, bool sync)
{
    en_result_t ret;
    uint8_t retry = 3;
    if(size < PRODUCT_SN_SIZE || sn[0] == 0)
    {
        return -1;
    }

    if(CheckSnFormat(sn, PRODUCT_SN_SIZE) == false)
    {
        return -1;
    }

    product_sn_manager.exist = false;
    product_sn_manager.sn.magic = PRODUCT_SN_MAGIC;
    memcpy(product_sn_manager.sn.data, sn, PRODUCT_SN_SIZE);
    UpdateProductProject();
    product_sn_manager.sn.crc16 = Cal_CRC_MultipleData((uint8_t *)(&product_sn_manager.sn), offsetof(product_sn_st, crc16));
    if(sync)
    {
        do
        {
            FlashSectorErase(FACTORY_ADDRESS);
            ret = FlashWriteBytes(FACTORY_ADDRESS, (uint8_t *)(&product_sn_manager.sn), sizeof(product_sn_st));
        }while(retry-- > 0 && ret != 0);

        if(ret != 0)
        {
            return -1;
        }
    }
    return 0;
}

int8_t ReadProductSn(uint8_t *sn, uint8_t size)
{
    memset(sn, 0, PRODUCT_SN_SIZE);   
    if(size < PRODUCT_SN_SIZE || product_sn_manager.exist == false)
    {
        return -1;
    }

    memcpy(sn, product_sn_manager.sn.data, PRODUCT_SN_SIZE);
    return 0;
}

int8_t ReadProductUserModel(uint8_t *user_model, uint8_t size)
{
    if(size < PRODUCT_MODEL_SIZE)
    {
        return -1;
    }

    strncpy((char *)user_model, USER_MODEL, PRODUCT_MODEL_SIZE);
    return 0;
}

int8_t ReadProductModel(uint8_t *model, uint8_t size)
{
    uint8_t * user_model = (uint8_t *)USER_MODEL;
    if(size < PRODUCT_MODEL_BYTES)
    {
        return -1;
    }
    user_model += strlen(PRODUCT_MODEL_PREFIX);
    strncpy((char *)model, (const char *)user_model, PRODUCT_MODEL_BYTES);
    return 0;
}

void ClearProductSn(void)
{
    FlashSectorErase(FACTORY_ADDRESS);
    memset(&product_sn_manager, 0, sizeof(product_sn_manager_st));
}
#endif

bool CheckSnFormat(uint8_t *sn, uint8_t size)
{
    if(size < PRODUCT_SN_SIZE)
    {
        return false;
    }

    if(sn[PRODUCT_PROJECT_OFFSET - 1] != '/')
    {
        return false;
    }

    if(sn[PRODUCT_YEAR_OFFSET] < 48 || sn[PRODUCT_YEAR_OFFSET] > 57)
    {
        return false;
    }

    if((sn[PRODUCT_MONTH_OFFSET] <= 48 || sn[PRODUCT_YEAR_OFFSET] > 57) &&
       (sn[PRODUCT_MONTH_OFFSET] < 65 || sn[PRODUCT_YEAR_OFFSET] > 67))
    {
        return false;
    }

    if((sn[PRODUCT_DAY_OFFSET] <= 48 || sn[PRODUCT_DAY_OFFSET] > 57) &&
       (sn[PRODUCT_DAY_OFFSET] < 65 || sn[PRODUCT_DAY_OFFSET] > 90))
    {
        return false;
    }

    if((sn[PRODUCT_SEQ_THOUSAND_OFFSET] < 48 || sn[PRODUCT_SEQ_THOUSAND_OFFSET] > 57) &&
       (sn[PRODUCT_SEQ_THOUSAND_OFFSET] < 65 || sn[PRODUCT_SEQ_THOUSAND_OFFSET] > 90))
    {
        return false;
    }


    if(sn[PRODUCT_SEQ_HUNDRED_OFFSET] < 48 || sn[PRODUCT_SEQ_HUNDRED_OFFSET] > 57)
    {
        return false;
    }

    
    if(sn[PRODUCT_SEQ_TEN_OFFSET] < 48 || sn[PRODUCT_SEQ_TEN_OFFSET] > 57)
    {
        return false;
    }
    
    if(sn[PRODUCT_SEQ_ONE_OFFSET] < 48 || sn[PRODUCT_SEQ_ONE_OFFSET] > 57)
    {
        return false;
    }

    return true;
}

bool IsProductSnExist(void)
{
    return product_sn_manager.exist;
}

