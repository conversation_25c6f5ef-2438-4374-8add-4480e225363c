/*!
 * @file
 * @brief This module is heartbeat led.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "HeartbeatLed.h"

#define HeartbeatLed_POLL_INTERVAL_MS 100

static bool b_LedState = false;
static uint16_t u16_BlinkCount = 0;

static void Set_LedBlinkState(void)
{
    if(true == b_LedState)
    {
        b_LedState = false;
        IO_HEARTBEAT_LED_ENABLE;
    }
    else
    {
        b_LedState = true;
        IO_HEARTBEAT_LED_DISABLE;
    }
}

void Update_HeartbeatLed(void)
{
    if(++u16_BlinkCount > HeartbeatLed_POLL_INTERVAL_MS)
    {
        Set_LedBlinkState();
        u16_BlinkCount = 0;
    }
}

