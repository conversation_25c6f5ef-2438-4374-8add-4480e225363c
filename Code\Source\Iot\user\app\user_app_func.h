/**
 * <AUTHOR>
 * @date    2019
 * @par     Copyright (c):
 *
 *    Copyright 2019 MIoT,MI
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */


#ifndef __USER_APP_FUNC_H__
#define __USER_APP_FUNC_H__

/**
 * @brief  get time string from wifi module
 *
 * @param[in]  argv: miio_handle_t pointer
 *
 * @return
 *      - 0 : success
 *      - error code
 */
int app_func_get_time(char* pResult);
/**
 * @brief  get mac string from wifi module
 *
 * @param[in]  argv: miio_handle_t pointer
 *
 * @return
 *      - 0 : success
 *      - error code
 */
int app_func_get_mac(char* pResult);

int app_func_get_model(char* pResult);

/**
 * @brief  get version from wifi module
 *
 * @param[in]  argv: miio_handle_t pointer
 *
 * @return
 *      - 0 : success
 *      - error code
 */
int app_func_get_version(char* pResult);

/**
 * @brief  get wifi info from wifi module
 *
 * @param[in]  argv: miio_handle_t pointer
 *
 * @return
 *      - 0 : success
 *      - error code
 */
int app_func_getwifi(char* pResult);

/**
 * @brief  get platform info from wifi module
 *
 * @param[in]  argv: miio_handle_t pointer
 *
 * @return
 *      - 0 : success
 *      - error code
 */
int app_func_get_arch_platform(char* pResult);

/**
 * @brief  get net state from wifi module
 *
 * @param[in]  argv: miio_handle_t pointer
 *
 * @return
 *      - 0 : success
 *      - error code
 */
int app_func_get_net_state(char* pResult);
/**
 * @brief  reboot wifi module
 *
 * @param[in]  argv: miio_handle_t pointer
 *
 * @return
 *      - 0 : success
 *      - error code
 */
int app_func_reboot(char* pResult);
/**
 * @brief  restore wifi module(clear user info & wifi info)
 *
 * @param[in]  argv: miio_handle_t pointer
 *
 * @return
 *      - 0 : success
 *      - error code
 */
int app_func_restore(char* pResult);

/**
 * @brief  set wifi info to wifi module (ssid & passwd)
 *
 * @param[in]  argv: miio_handle_t pointer
 *
 * @return
 *      - 0 : success
 *      - error code
 */
int app_func_setwifi(char* pResult);

/**
 * @brief  set mcu version to wifi module
 *
 * @param[in]  argv: miio_handle_t pointer
 *
 * @return
 *      - 0 : success
 *      - error code
 */
int app_func_set_mcu_version(char* pResult);

/**
 * @brief  set factory mode to module
 *
 * @param[in]  argv: miio_handle_t pointer
 *
 * @return
 *      - 0 : success
 *      - error code
 */
int app_func_factory(char* pResult);

int app_func_sn(char* pResult);
int app_func_get_sn(char* pResult);
int app_func_model(char* pResult);
int app_func_ble_conf(char* pResult);
int app_func_getdid(char* pResult);
int app_func_maintence_upload(char *pResult);
int app_func_lypower_upload(char *pResult);
#endif /* __USER_APP_FUNC_H__ */
