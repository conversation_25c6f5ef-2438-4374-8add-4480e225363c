/*!
 * @file
 * @brief This file defines public constants, types and functions for the Core scheduler.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "Core_Scheduler.h"
#include "Core_TimerLibrary.h"
#include "Core_TimeBase.h"
#include "Adpt_Iwdg.h"
#include "CoreUser_Scheduler_Table.h"

#define CORE_SCHEDULER_NUMBER_SCHEDULED_FUNCTIONS \
    (sizeof(CoreUser_Scheduler_aScheduleTable) / sizeof(st_CoreSchedulerItem))

/*!
 * @brief This type holds all local variables for this module.
 * @param st_CoreTimerLibTimer  ast_SchedulerItemTimer[CORE_SCHEDULER_NUMBER_SCHEDULED_FUNCTIONS] :
 * The following array allocates a timer object for each scheduled item.
 * @param uint16_t u16_Seconds : The number of seconds
 */
typedef struct
{
    st_CoreTimerLibTimer ast_SchedulerItemTimer[CORE_SCHEDULER_NUMBER_SCHEDULED_FUNCTIONS];
} st_CoreSchedulerStatus;

static st_CoreSchedulerStatus st_Status;
static st_CoreTimerLibTimer st_BoardInitTimerSecond;

void Core_Scheduler_Init(void);
void Core_Scheduler_Execute(void);

void Core_Scheduler_Init(void)
{
    uint8_t u8_Index;

    Core_TimerLib_TimerInit(&st_BoardInitTimerSecond);

    for(u8_Index = 0; u8_Index < CORE_SCHEDULER_NUMBER_SCHEDULED_FUNCTIONS; u8_Index++)
    {
        Core_TimerLib_TimerInit(&st_Status.ast_SchedulerItemTimer[u8_Index]);
    }
}

void Core_Scheduler_Execute(void)
{
    uint8_t u8_Index;

    for(u8_Index = 0; u8_Index < CORE_SCHEDULER_NUMBER_SCHEDULED_FUNCTIONS; u8_Index++)
    {
        Core_TimerLib_TimerStart(&st_Status.ast_SchedulerItemTimer[u8_Index],
            CoreUser_Scheduler_aScheduleTable[u8_Index].u16_IntervalSeconds,
            CoreUser_Scheduler_aScheduleTable[u8_Index].u16_IntervalMilliSeconds);
    }

    while(1)
    {
        IWDG_Refesh();

        for(u8_Index = 0; u8_Index < CORE_SCHEDULER_NUMBER_SCHEDULED_FUNCTIONS;
            u8_Index++)
        {
            if(true == Core_TimerLib_IsTimerExpired(&st_Status.ast_SchedulerItemTimer[u8_Index]))
            {
                Core_TimerLib_TimerStart(
                    &st_Status.ast_SchedulerItemTimer[u8_Index],
                    CoreUser_Scheduler_aScheduleTable[u8_Index].u16_IntervalSeconds,
                    CoreUser_Scheduler_aScheduleTable[u8_Index].u16_IntervalMilliSeconds);

                CoreUser_Scheduler_aScheduleTable[u8_Index].pfScheduledFunction();
            }
        }
    }
}
