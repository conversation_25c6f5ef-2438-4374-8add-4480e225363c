/*!
 * @file
 * @brief This file defines public constants, types and functions for the data manager.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef DATA_MANAGER_H
#define DATA_MANAGER_H

#include <stdint.h>
#include <stdbool.h>
#include "Adpt_Usart.h"
#include "TestUsart.h"

// 新加：32位分割
#define Get_uint32_MHSB(u32_word) ((uint8_t)(((u32_word) >> 24) & 0x000000FF))
#define Get_uint32_SHSB(u32_word) ((uint8_t)(((u32_word) >> 16) & 0x000000FF))
#define Get_uint32_SLSB(u32_word) ((uint8_t)(((u32_word) >> 8) & 0x000000FF))
#define Get_uint32_MLSB(u32_word) ((uint8_t)((u32_word) & 0x000000FF))

#define Get_uint16_MSB(u16_word) ((uint8_t)(((u16_word) >> 8) & 0x00FF))
#define Get_uint16_LSB(u16_word) ((uint8_t)((u16_word) & 0x00FF))

#define U8_UART_BUS_FRAME_HEAD (uint8_t)0xA5
#define U8_UART_BUS_FRAME_PC_ADDR (uint8_t)0x01
#define U8_UART_BUS_FRAME_BOARD_ADDR (uint8_t)0xEE
#define U8_UART_BUS_FRAME_CMD (uint8_t)0xCC

#define U16_MAX_SHORTEN_TIME_MINUTES ((uint16_t)(18 * 60))
#define S16_MAX_MICRO_ADJUST_ON_OFF_TEMP ((int16_t)(3 * 10))
#define S16_MIN_MICRO_ADJUST_ON_OFF_TEMP ((int16_t)(-3 * 10))
#define U16_MICRO_ADJUST_RANGE (uint16_t)30U
#define GET_ARRAY_SIZE(ARRAY) (sizeof(ARRAY) / sizeof(ARRAY[0]))

typedef enum
{
    E_INQUIRE_NONE = 0,
    E_INQUIRE_REF_ON_TEMP,
    E_INQUIRE_REF_OFF_TEMP,
    E_INQUIRE_FRZ_ON_TEMP,
    E_INQUIRE_FRZ_OFF_TEMP,
    E_INQUIRE_VAR_ON_TEMP,
    E_INQUIRE_VAR_OFF_TEMP,
    E_INQUIRE_COMP_PWM,
    E_INQUIRE_FRZ_FAN,
    E_INQUIRE_REF_FAN,
    E_INQUIRE_VAR_FAN,
    E_INQUIRE_COOL_FAN,
    E_INQUIRE_ICEMAKER_TEST,
    E_INQUIRE_ICEMAKER_FORCE_LOAD,
    E_INQUIRE_ICEMAKER_FORCE_TIME,
    E_INQUIRE_ICEMAKER_FORCE_HEATER,
    E_INQUIRE_ICEMAKER_FORCE_WATERLOG,
    E_INQUIRE_ICEMAKER_FORCE_BHEATER,
    E_INQUIRE_ICEMAKER_FORCE_SENSOR_ONOFF,
    E_INQUIRE_MAX
} MicroAdjust_em;

typedef struct
{
    uint8_t u8_MasterComputerCmd1;
    uint8_t u8_MasterComputerCmd2;
    uint8_t (*p_HandleFunc)(UartTestParm_st *const p_uart_test_parm,
        uint8_t u8_length_count);
} MasterComputerCmd_st;

void Handle_MasterComputerFrame(UartTestParm_st *const p_uart_test_parm);

#endif
