/**
 *******************************************************************************
 * @file  opa.c
 * @brief This file provides - functions to manage the OPA.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-03-15       MADS            First version
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2022, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 *******************************************************************************
 */

/******************************************************************************
 * Include files
 ******************************************************************************/
#include "opa.h"

/**
 * @addtogroup HC32L186_DDL 驱动库
 * @{
 */

/**
 * @defgroup DDL_OPA OPA模块驱动库
 * @brief OPA Driver Library OPA模块驱动库
 * @{
 */

/******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/


/******************************************************************************
 * Global variable definitions (declared in header file with 'extern')
 ******************************************************************************/


/******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/

/******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/

/*****************************************************************************
 * Function implementation - global ('extern') and local ('static')
 *****************************************************************************/
/**
 * @defgroup OPA_Global_Functions OPA全局函数定义
 * @{
 */


/**
 * @brief  OPA 使能控制.
 * @param  [in] NewStatus:
 *              - TRUE: 使能OPA通用运算放大器模式
 *              - FALSE: 禁用OPA通用运算放大器模式
 * @retval None.
 */
void Opa_Cmd(boolean_t NewStatus)
{
    SetBit((uint32_t)(&(M0P_OPA->CR0)), 0, NewStatus);
}


/**
 * @brief  DAC输出跟随器使能控制.
 * @param  [in] NewStatus:
 *              - TRUE: 使能DAC输出跟随器
 *              - FALSE: 禁用DAC输出跟随器
 * @retval None.
 */
void Opa_CmdBuf(boolean_t NewStatus)
{
    SetBit((uint32_t)(&(M0P_OPA->CR0)), 2, NewStatus);
}

/**
 * @brief  OPA_OUTx 输出使能控制.
 * @param  [in] oenx: 输出通道OUTx @ref en_opa_oenx_t
 * @param  [in] NewState:
 *              - TRUE: OPA_OUTx与OPA输出信号连接
 *              - FALSE: OPA_OUTx与OPA输出信号断开
 * @retval None.
 */
void Opa_CmdOenx(en_opa_oenx_t oenx, boolean_t NewState)
{
    SetBit((uint32_t)(&(M0P_OPA->CR0)), oenx, NewState);
}
/**
 * @}
 */


/**
 * @}
 */

/**
 * @}
 */
/******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/

