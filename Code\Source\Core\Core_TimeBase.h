/*!
 * @file
 * @brief core time base.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef __CORE_TIMEBASE_H__
#define __CORE_TIMEBASE_H__

#include <stdint.h>

/*!
 * @brief 1ms tick  MaxValue <= 65
 */
#define COREUSER_TIMEBASE_NUM_TICKS_PER_MILLISECOND ((uint16_t)(1))

/*!
 * @brief Number of milliseconds in 1 second (Defined just for code readability)
 */
#define CORE_TIMEBASE_NUM_MILLISECONDS_PER_SECOND ((uint16_t)(1000))

/*!
 * @brief Number of seconds in 1 minute (Defined just for code readability)
 */
#define CORE_TIMEBASE_NUM_SECONDS_PER_MINUTE ((uint16_t)(60))

/*!
 * @brief This constant defines the number of system ticks that will elapse in one second.
 */
#define CORE_TIMEBASE_NUM_TICKS_PER_SECOND ((uint16_t)(COREUSER_TIMEBASE_NUM_TICKS_PER_MILLISECOND * CORE_TIMEBASE_NUM_MILLISECONDS_PER_SECOND))

/*!
 * @brief This constant defines the number of system ticks that will elapse in one minute.
 */
#define CORE_TIMEBASE_U32_NUM_TICKS_PER_MINUTE ((uint32_t)((uint32_t)(CORE_TIMEBASE_NUM_TICKS_PER_SECOND) * (uint32_t)(CORE_TIMEBASE_NUM_SECONDS_PER_MINUTE)))

/*!
 * @brief This global variable is a know violation of the "no global variables" rule
 *        because some modules (i.e. SpeedSensor) can not afford the overhead of using
 *        a function call to retrieve the system tick count.  Those modules that can
 *        afford the overhead should use the Core_Timebase_GetSystemTickCount() function.
 */
extern volatile uint16_t gu16SystemTickCounter_ISR;

/*!
 * @brief This function initializes the timebase module
 */
extern void Core_TimeBase_Init(void);

/*!
 * @brief This function is used to increment the Core Tick Count, it must be called by
 *        the user from a timer interrupt (preferably 62.5us).  This function allows
 *        the Core timers and scheduler to operate.
 */
extern void Core_TimeBase_Execute_ISR(void);

/*!
 * @brief This function is the preferred method for reading the system tick count.
 *         All modules that can handle the overhead of making the function
 *         call should use this function to read the system tick counter.
 * @retval Return the system tick counter
 */
extern uint16_t Core_TimeBase_GetSystemTickCounter(void);

/*!
 * @brief This function provides a simple mechanism to convert a numberof ticks into milliseconds.
 * @retval Return the converted milliseconds
 */
extern uint16_t Core_TimeBase_ConvertTicksToMS(void);

#endif
