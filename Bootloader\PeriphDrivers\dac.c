/**
 *******************************************************************************
 * @file  dac.c
 * @brief This file provides - functions to manage the DAC.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-03-15       MADS            First version
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2022, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 *******************************************************************************
 */

/******************************************************************************
 * Include files
 ******************************************************************************/
#include "dac.h"

/**
 * @addtogroup HC32L186_DDL 驱动库
 * @{
 */

/**
 * @defgroup DDL_DAC DAC模块驱动库
 * @brief DAC Driver Library DAC模块驱动库
 * @{
 */

/******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/

/******************************************************************************
 * Global variable definitions (declared in header file with 'extern')
 ******************************************************************************/

/******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/

/******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/

/*****************************************************************************
 * Function implementation - global ('extern') and local ('static')
 *****************************************************************************/
/**
 * @defgroup DAC_Global_Functions DAC全局函数定义
 * @{
 */


/**
 * @brief  使能相关通道的DMA DMA_CR0中的DMAEN0.
 * @param  [in] NewState: TRUE 或者 FALSE
 * @retval None.
 */
void Dac_DmaCmd(boolean_t NewState)
{
    SetBit((uint32_t)(&(M0P_DAC->CR0)), 12, NewState);
}

/**
 * @brief  配置DAC的DMA下溢中断， DMA_CR0中的DMAUDRIE0.
 * @param  [in] NewState: TRUE 或者 FALSE
 * @retval None.
 */
void Dac_DmaITCfg(boolean_t NewState)
{
    SetBit((uint32_t)(&(M0P_DAC->CR0)), 13, NewState);
}

/**
 * @brief  获取DAC的DMA下溢中断标志位状态， DMA_SR中的DMAUDR0.
 * @retval boolean_t:
 *              - TRUE: 中断标志置位
 *              - FALSE: 中断标志未置位
 */
boolean_t Dac_GetITStatus(void)
{
    return GetBit((uint32_t)(&(M0P_DAC->SR)), 13);
}

/**
 * @brief  配置DAC的使能与禁止， DMA_CR0中的EN0.
 * @param  [in] NewState: TRUE 或者 FALSE
 * @retval None.
 */
void Dac_Cmd(boolean_t NewState)
{
    SetBit((uint32_t)(&(M0P_DAC->CR0)), 0, NewState);
}

/**
 * @brief  软件触发寄存器，触发DAC转换 DMA_SWTRIGR中的SWTRIG0.
 * @retval None.
 */
void Dac_SoftwareTriggerCmd(void)
{
    SetBit((uint32_t)(&(M0P_DAC->SWTRIGR)), 0, 1);
}

/**
 * @brief  初始化DAC.
 * @param  [in] DAC_InitStruct: 用于初始化DAC的结构体 @ref stc_dac_cfg_t
 * @retval None.
 */
void Dac_Init(stc_dac_cfg_t *DAC_InitStruct)
{
    M0P_DAC->CR0_f.BOFF0 = DAC_InitStruct->boff_t;
    M0P_DAC->CR0_f.TEN0  = DAC_InitStruct->ten_t;
    M0P_DAC->CR0_f.TSEL0 = DAC_InitStruct->tsel_t;
    M0P_DAC->CR0_f.WAVE0 = DAC_InitStruct->wave_t;
    M0P_DAC->CR0_f.MAMP0 = DAC_InitStruct->mamp_t;
    M0P_DAC->CR0_f.SREF0 = DAC_InitStruct->sref_t;

    if (DAC_InitStruct->align == DacLeftAlign)
    {
        M0P_DAC->DHR12L0_f.DHR0 = DAC_InitStruct->dhr12;
    }
    else if (DAC_InitStruct->align == DacRightAlign)
    {
        M0P_DAC->DHR12R0_f.DHR0 = DAC_InitStruct->dhr12;
    }
    else
    {
        M0P_DAC->DHR8R0_f.DHR0 = DAC_InitStruct->dhr8;
    }
}

/**
 * @brief  向DAC的数据保持寄存器写数据.
 * @param  [in] DAC_Align: 对齐方式 @ref en_align_t
 * @param  [in] DAC_Bit: 数据位宽 @ref en_bitno_t
 * @param  [in] Data: DAC输出数据
 * @retval None.
 */
void Dac_SetChannelData(en_align_t DAC_Align, en_bitno_t DAC_Bit, uint16_t Data)
{
    if (DAC_Align == DacRightAlign)
    {
        if (DAC_Bit == DacBit8)
        {
            M0P_DAC->DHR8R0_f.DHR0 = (uint8_t)Data;
        }
        else if (DAC_Bit == DacBit12)
        {
            M0P_DAC->DHR12R0_f.DHR0 = Data;
        }
        else
        {
            return;
        }
    }
    else if (DAC_Align == DacLeftAlign)
    {
        if (DAC_Bit == DacBit8)
        {
            return;
        }
        else if (DAC_Bit == DacBit12)
        {
            M0P_DAC->DHR12L0_f.DHR0 = Data;
        }
        else
        {
            return;
        }
    }
    else
    {
        return;
    }
}

/**
 * @brief  获取DAC数据输出寄存器数据.
 * @retval uint16_t: DAC_DOR0的值
 */
uint16_t Dac_GetDataOutputValue(void)
{
    uint16_t tmp;

    tmp = M0P_DAC->DOR0_f.DOR0;
    return tmp & 0x0fff;
}


/**
 * @}
 */

/**
 * @}
 */

/**
 * @}
 */

/******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/


