/*!
 * @file
 * @brief core time base.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "Core_TimeBase.h"

volatile uint16_t gu16SystemTickCounter_ISR;

void Core_TimeBase_Init(void);

void Core_TimeBase_Execute_ISR(void);

uint16_t Core_TimeBase_GetSystemTickCounter(void);

uint16_t Core_TimeBase_ConvertTicksToMS(void);

void Core_TimeBase_Init(void)
{
    gu16SystemTickCounter_ISR = 0;
}

void Core_TimeBase_Execute_ISR(void)
{
    gu16SystemTickCounter_ISR++;
}

uint16_t Core_TimeBase_GetSystemTickCounter(void)
{
    return (gu16SystemTickCounter_ISR);
}

uint16_t Core_TimeBase_ConvertTicksToMS(void)
{
    uint16_t u16_NumberSeconds;
    u16_NumberSeconds = gu16SystemTickCounter_ISR /
        COREUSER_TIMEBASE_NUM_TICKS_PER_MILLISECOND;
    return (u16_NumberSeconds);
}
