/*!
 * @file
 * @brief Generic, unordered list
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef LIST_H
#define LIST_H

#include <stdint.h>
#include <stdbool.h>
#include "Core_Types.h"

#define LIST_ELEMENT_ADDRESS(_index) \
    ((void *)&(((uint8_t *)(pst_List->p_Buffer))[(pst_List->u8_ElementSize) * (_index)]))

typedef struct
{
    void *p_Buffer;
    const void *p_EmptyElement;
    uint16_t u16_NumberElements;
    uint8_t u8_ElementSize;
} ST_List;

/*!
 * @brief Initialize a List to an empty state.
 * @param ST_List * pst_List : The list
 * @param void * p_Buffer : Buffer used for storage of list elements
 * @param uint16_t u16_NumberElements : Number of elements that can be stored in the list
 *  (must match the size of the buffer)
 * @param uint8_t u8_ElementSize : Size of a single element in the list
 * @param const void * p_EmptyElement : Element to use for the default/empty state of a
 * location in the list
 * @retval true if all init parameters are valid, false otherwise.
 */
extern bool List_B_Init(ST_List *pst_List, void *p_Buffer, uint16_t u16_NumberElements, uint8_t u8_ElementSize, const void *p_EmptyElement);

/*!
 * @brief Inserts an element into a list if it is not already present in the list.
 * @param ST_List * pst_List : The list
 * @param const void * p_Element : Pointer to the element to be added to the list
 * @retval true if inserting action is valid, false otherwise.
 */
extern bool List_B_Insert(ST_List *pst_List, const void *p_Element);

/*!
 * @brief Removes an element from the list.  If for some reason an element is in the
 *  list more than one time, all occurrences will be removed. After element is removed,
 *  the empty element will remain in its place.
 * @param ST_List * pst_List : The list
 * @param const void * p_Element : Pointer to the element to be removed from the list
 * @retval true if removing action is valid, false otherwise.
 */
extern bool List_B_Remove(ST_List *pst_List, const void *p_Element);

/*!
 * @brief Determines whether an element is present in a list.
 * @param ST_List * pst_List : The list
 * @param const void * p_Element : Pointer to the element to be checked
 * @retval true if the list contains the checked element, false otherwise.
 */
extern bool List_B_Contains(ST_List *pst_List, const void *p_Element);

/*!
 * @brief Copy the specified index to the element
 * @param ST_List * pst_List : The list
 * @param const void * p_Element : Pointer to the element at the specified index will be copied.
 * @retval true if the copy action is valid, false otherwise.
 */
extern bool List_B_Access(ST_List *pst_List, uint16_t u16_Index, void *p_Element);

#endif
