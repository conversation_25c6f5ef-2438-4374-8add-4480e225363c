/*!
 * @file
 * @brief This file defines public constants, types and functions for the dc load drive module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef __IO_DEVICE_H__
#define __IO_DEVICE_H__

#include <stdint.h>
#include <stdbool.h>
#include "Adpt_GPIO.h"

void Set_FrzLeftLamp(bool state);
void Set_FrzRightLamp(bool state);
void Set_RefHeaterState(bool state);
void Set_IonGeneratorState(bool state);
bool Get_IonGeneratorState(void);
void Set_FrzIonGeneratorState(bool state);
bool Get_FrzIonGeneratorState(void);
uint16_t Get_IonGeneratorOnMinutes(void);
uint16_t Get_FrzIonGeneratorOnMinutes(void);
void Clear_IonGeneratorOnMinutes(void);
void Clear_FrzIonGeneratorOnMinutes(void);
void Set_DefrostHeaterState(bool state);
bool Get_DefrostHeaterState(void);
void Set_VarDamperHeaterState(bool state);
void Set_VarDamperHeaterState(bool state);
void Set_VerticalBeamHeaterState(bool state);
bool Get_VerticalBeamHeaterDeviceState(void);
#endif /* __IO_DEVICE_H__ */