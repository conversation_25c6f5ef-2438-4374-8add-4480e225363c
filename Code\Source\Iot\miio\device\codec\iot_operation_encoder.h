#ifndef IOT_OPERATION_ENCODER_H
#define IOT_OPERATION_ENCODER_H

#include "Iot_Spec.h"
#include "miio_define.h"
#include "user_config.h"
#include "util.h"

void iot_operation_value_encode(IotGeneralPropVal_t * pValue, char out[], size_t size);
int iot_event_operation_encode(TByte siid, TByte eiid, char out[], size_t size);
int iot_action_operation_encode(IotAction_result_t *opt,  char out[], size_t size);
int iot_changed_operation_encode(IotPropOper_result_t *opt, char out[], size_t size);
int iot_property_operation_encode(IotPropOper_result_t *pIotResult, char out[], size_t size, bool has_value);
int iot_operation_encode_end(char out[], size_t size);


#endif
