/*!
 * @file
 * @brief Configures the Usart.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include <string.h>
#include "Adpt_Usart.h"
#include "SbusUsart.h"
#include "lpuart.h"
#include "uart.h"
#include "InverterUsart.h"
#include "syslog.h"
#include "Driver_Flash.h"
#include "Adpt_GPIO.h"

volatile static uint8_t u8_lpUart0_RxData;
volatile static uint8_t u8_lpUart1_RxData;
volatile static uint8_t u8_Uart3_RxData;
uart_channel_st uartCh[UART_CH_MAX];

void LpUart0_SendOneData(uint8_t u8_data)
{
    LPUart_SendDataIt(M0P_LPUART0, u8_data);
}

// Uart Enable Interrupts
void LpUart0_EnalbeTxInterrupts(void)
{
    // Enable Tx interrupt
    LPUart_DisableFunc(M0P_LPUART0, LPUartRenFunc);
    LPUart_EnableIrq(M0P_LPUART0, LPUartTxEIrq);
}

void LpUart0_DisalbeTxInterrupts(void)
{
    // Disable Tx interrupt
    LPUart_DisableIrq(M0P_LPUART0, LPUartTxEIrq);
}

void LpUart0_EnalbeRxInterrupts(void)
{

    // Enable rx interrupt
    while (FALSE == LPUart_GetStatus(M0P_LPUART0, LPUartTxe)) {;}
    LPUart_ClrStatus(M0P_LPUART0, LPUartRC);
    LPUart_EnableFunc(M0P_LPUART0, LPUartRenFunc);
    LPUart_EnableIrq(M0P_LPUART0, LPUartRxIrq);
}

void LpUart0_DisalbeRxInterrupts(void)
{
    // Disable rx interrupt
    LPUart_DisableIrq(M0P_LPUART0, LPUartRxIrq);
}

void LpUart0_IRQHandler(void)
{
    // 错误清除处理，结合程序功能逻辑，采用合适的错误逻辑处理
    if(LPUart_GetStatus(M0P_LPUART0, LPUartFE))
    {
        LPUart_ClrStatus(M0P_LPUART0, LPUartFE);
    }

    if(M0P_LPUART0->SCON_f.TXEIE && LPUart_GetStatus(M0P_LPUART0, LPUartTxe))
    {
        // 用户发送数据处理
        Handle_UartSbusSendData();
    }

    if((LPUart_GetStatus(M0P_LPUART0, LPUartRC)) && (M0P_LPUART0->SCON_f.RCIE)) /// 接收数据
    {
        LPUart_ClrStatus(M0P_LPUART0, LPUartRC); ///< 清接收中断请求
                                                 // 用户接收数据处理
        u8_lpUart0_RxData = LPUart_ReceiveData(M0P_LPUART0); // 接收数据字节
        Handle_UartSbusReceData(u8_lpUart0_RxData);
    }
}

static void Board_InitLpUart0(void)
{
    stc_lpuart_cfg_t stcCfg;

    DDL_ZERO_STRUCT(stcCfg);

    ///< 外设模块时钟使能
    Sysctrl_SetPeripheralGate(SysctrlPeripheralLpUart0, TRUE);

    ///< LPUART 初始化
    stcCfg.enStopBit = LPUart1bit; ///< 1停止位
    stcCfg.enMmdorCk = LPUartDataOrAddr; /// 无校验
    stcCfg.stcBaud.enSclkSel = LPUartMskPclk; ///< 传输时钟源
    stcCfg.stcBaud.u32Sclk = Sysctrl_GetPClkFreq(); ///< PCLK获取
    stcCfg.stcBaud.enSclkDiv = LPUartMsk4Or8Div; ///< 采样分频
    stcCfg.stcBaud.u32Baud = 9600; ///< 波特率
    stcCfg.enRunMode = LPUartMskMode1; ///< 工作模式
    LPUart_Init(M0P_LPUART0, &stcCfg);

    ///< LPUART 中断使能
    LPUart_ClrStatus(M0P_LPUART0, LPUartRC); ///< 清接收中断请求
    LPUart_ClrStatus(M0P_LPUART0, LPUartTC); ///< 清发送中断请求
    LPUart_DisableIrq(M0P_LPUART0, LPUartRxIrq); ///< 禁止接收中断
    LPUart_DisableIrq(M0P_LPUART0, LPUartTxEIrq); ///< 禁止发送中断

    EnableNvic(LPUART0_IRQn, IrqLevel1, TRUE); ///< 系统中断使能
}

static void Board_DeinitLpUart0(void)
{
    EnableNvic(LPUART0_IRQn, IrqLevel2, FALSE);
    LPUart_ClrStatus(M0P_LPUART0, LPUartRC);
    LPUart_ClrStatus(M0P_LPUART0, LPUartTC);
    LPUart_DisableIrq(M0P_LPUART0, LPUartRxIrq);
    LPUart_DisableIrq(M0P_LPUART0, LPUartTxEIrq);
    Sysctrl_SetPeripheralGate(SysctrlPeripheralLpUart0, FALSE);
    return;
}
void LpUart1_SendOneData(uint8_t u8_data)
{
    LPUart_SendDataIt(M0P_LPUART1, u8_data);
}

// Uart Enable Interrupts
void LpUart1_EnalbeTxInterrupts(void)
{
    // Enable Tx interrupt
    LPUart_EnableIrq(M0P_LPUART1, LPUartTxEIrq);
}

void LpUart1_DisalbeTxInterrupts(void)
{
    // Disable Tx interrupt
    LPUart_DisableIrq(M0P_LPUART1, LPUartTxEIrq);
}

static void Board_InitLpUart1(void)
{
    stc_lpuart_cfg_t stcCfg;

    DDL_ZERO_STRUCT(stcCfg);

    ///< 外设模块时钟使能
    Sysctrl_SetPeripheralGate(SysctrlPeripheralLpUart1, TRUE);

    ///< LPUART 初始化
    stcCfg.enStopBit = LPUart1bit; ///< 1停止位
    stcCfg.enMmdorCk = LPUartDataOrAddr; /// 无校验
    stcCfg.stcBaud.enSclkSel = LPUartMskPclk; ///< 传输时钟源
    stcCfg.stcBaud.u32Sclk = Sysctrl_GetPClkFreq(); ///< PCLK获取
    stcCfg.stcBaud.enSclkDiv = LPUartMsk4Or8Div; ///< 采样分频
    stcCfg.stcBaud.u32Baud = 9600; ///< 波特率
    stcCfg.enRunMode = LPUartMskMode1; ///< 工作模式
    LPUart_Init(M0P_LPUART1, &stcCfg);

    ///< LPUART 中断使能
    LPUart_ClrStatus(M0P_LPUART1, LPUartRC); ///< 清接收中断请求
    LPUart_ClrStatus(M0P_LPUART1, LPUartTC); ///< 清发送中断请求
    LPUart_EnableIrq(M0P_LPUART1, LPUartRxIrq); ///< 使能接收中断
    LPUart_DisableIrq(M0P_LPUART1, LPUartTxEIrq); ///< 禁止发送中断

    EnableNvic(LPUART1_IRQn, IrqLevel1, TRUE); ///< 系统中断使能
}

void InverterUart_SendOneData(uint8_t u8_data)
{
    Uart_SendDataIt(M0P_UART1, u8_data);
}

void InverterUart_EnalbeTxInterrupts(void)
{
    Uart_EnableIrq(M0P_UART1, UartTxEIrq);
}

void InverterUart_DisalbeTxInterrupts(void)
{
    Uart_DisableIrq(M0P_UART1, UartTxEIrq);
}

void InverterUart_EnalbeRxInterrupts(void)
{
    Uart_ClrStatus(M0P_UART1, UartRC);
    Uart_EnableIrq(M0P_UART1, UartRxIrq);
}

void InverterUart_DisalbeRxInterrupts(void)
{
    Uart_DisableIrq(M0P_UART1, UartRxIrq);
}

void Board_InitInverterUart(void)
{
    stc_uart_cfg_t stcCfg;

    DDL_ZERO_STRUCT(stcCfg);
    ///< 开启外设时钟
    Sysctrl_SetPeripheralGate(SysctrlPeripheralUart1, TRUE); ///< 使能uart1模块时钟

    ///< UART Init
    stcCfg.enRunMode = UartMskMode1; ///< 模式1
    stcCfg.enStopBit = UartMsk1bit; ///< 1bit停止位
    stcCfg.enMmdorCk = UartMskDataOrAddr; ///< 无检验
    stcCfg.stcBaud.u32Baud = 9600; ///< 波特率9600
    stcCfg.stcBaud.enClkDiv = UartMsk8Or16Div; ///< 通道采样分频配置
    stcCfg.stcBaud.u32Pclk = Sysctrl_GetPClkFreq(); ///< 获得外设时钟（PCLK）频率值
    Uart_Init(M0P_UART1, &stcCfg); ///< 串口初始化

    ///< UART中断使能
    Uart_ClrStatus(M0P_UART1, UartRC); ///< 清接收请求
    Uart_ClrStatus(M0P_UART1, UartTC); ///< 清接收请求
    Uart_DisableIrq(M0P_UART1, UartRxIrq); ///< 使能串口接收中断
    Uart_DisableIrq(M0P_UART1, UartTxEIrq); ///< 禁止串口发送完成中断

    EnableNvic(UART1_3_IRQn, IrqLevel1, TRUE); ///< 系统中断使能
}

void Board_DeinitInverterUart(void)
{
    EnableNvic(UART1_3_IRQn, IrqLevel1, FALSE);
    Uart_DisableIrq(M0P_UART1, UartRxIrq);
    Uart_DisableIrq(M0P_UART1, UartTxEIrq);
    Uart_ClrStatus(M0P_UART1, UartRC);
    Uart_ClrStatus(M0P_UART1, UartTC); 
    Sysctrl_SetPeripheralGate(SysctrlPeripheralUart1, FALSE);
}


void Uart1_IRQHandler(void)
{
    uint8_t rx_data = 0;
    // 错误清除处理，结合程序功能逻辑，采用合适的错误逻辑处理
    if(Uart_GetStatus(M0P_UART1, UartFE))
    {
        Uart_ClrStatus(M0P_UART1, UartFE);
    }

    if(M0P_UART1->SCON_f.RCIE && Uart_GetStatus(M0P_UART1, UartRC)) // UART数据接收
    {
        Uart_ClrStatus(M0P_UART1, UartRC);
        rx_data = Uart_ReceiveData(M0P_UART1); // 接收数据字节
        Handle_UartInverterReceData(rx_data);
    }

    if(M0P_UART1->SCON_f.TXEIE && Uart_GetStatus(M0P_UART1, UartTxe)) // UART数据发送
    {
        // 用户发送数据处理
        Handle_UartInverterSendData();
    }
}

void Uart3_SendOneData(uint8_t u8_data)
{
    Uart_SendDataIt(M0P_UART3, u8_data);
}

void Uart3_EnalbeTxInterrupts(void)
{
    Uart_EnableIrq(M0P_UART3, UartTxEIrq);
}

void Uart3_DisalbeTxInterrupts(void)
{
    Uart_DisableIrq(M0P_UART3, UartTxEIrq);
}

void Uart3_EnalbeRxInterrupts(void)
{
    Uart_ClrStatus(M0P_UART3, UartRC);
    Uart_EnableIrq(M0P_UART3, UartRxIrq);
}

void Uart3_DisalbeRxInterrupts(void)
{
    Uart_DisableIrq(M0P_UART3, UartRxIrq);
}

static int Uart3_EnableRxInt(uart_channel_st *ch, uint8_t en)
{
    if(en)
    {
        Uart_EnableIrq(M0P_UART3, UartRxIrq);
    }
    else
    {
        Uart_DisableIrq(M0P_UART3, UartRxIrq);
    }
    return 0;
}

static int Uart3_EnableTxInt(uart_channel_st *ch, uint8_t en)
{
    if(en)
    {
        EnableNvic(UART1_3_IRQn, IrqLevel1, TRUE);
    }
    else
    {
        EnableNvic(UART1_3_IRQn, IrqLevel1, FALSE);
    }
    return 0;
}

static int Uart3_SendByte(uart_channel_st *ch)
{
    uint8_t index = 0;

    index = ch->tx_front;
    ch->tx_front++;
    ch->tx_front %= UART_QUEUE_SIZE;
    ch->tx_done = false;
    Uart_SendDataIt(M0P_UART3, ch->tx_queue[index]);
    return 0;
}

void Board_InitUart3(void)
{
    stc_uart_cfg_t stcCfg;
    uart_channel_st *uch = &uartCh[UART_CH_WIFI];

    DDL_ZERO_STRUCT(stcCfg);
    ///< 开启外设时钟
    Sysctrl_SetPeripheralGate(SysctrlPeripheralUart3, TRUE); ///< 使能uart1模块时钟

    ///< UART Init
    stcCfg.enRunMode = UartMskMode1; ///< 模式1
    stcCfg.enStopBit = UartMsk1bit; ///< 1bit停止位
    stcCfg.enMmdorCk = UartMskDataOrAddr; ///< 无检验
    stcCfg.stcBaud.u32Baud = 115200; ///< 波特率9600
    stcCfg.stcBaud.enClkDiv = UartMsk8Or16Div; ///< 通道采样分频配置
    stcCfg.stcBaud.u32Pclk = Sysctrl_GetPClkFreq(); ///< 获得外设时钟（PCLK）频率值
    Uart_Init(M0P_UART3, &stcCfg); ///< 串口初始化

    uch->uops.send = Uart3_SendByte;
    uch->uops.enablerxirq = Uart3_EnableRxInt;
    uch->uops.enabletxirq = Uart3_EnableTxInt;
    uch->tx_done = true;

    ///< UART中断使能
    Uart_ClrStatus(M0P_UART3, UartRC); ///< 清接收请求
    Uart_ClrStatus(M0P_UART3, UartTC); ///< 清接收请求
    Uart_EnableIrq(M0P_UART3, UartRxIrq); ///< 使能串口接收中断
    Uart_EnableIrq(M0P_UART3, UartTxIrq);
    Uart_DisableIrq(M0P_UART3, UartTxEIrq); ///< 禁止串口发送完成中断

    EnableNvic(UART1_3_IRQn, IrqLevel1, TRUE); ///< 系统中断使能
}

void Uart3_IRQHandler(void)
{
    uart_channel_st *uch = &uartCh[UART_CH_WIFI];

    if(Uart_GetStatus(M0P_UART3, UartFE))
    {
        Uart_ClrStatus(M0P_UART3, UartFE);
    }

    if(Uart_GetStatus(M0P_UART3, UartPE))
    {
        Uart_ClrStatus(M0P_UART3, UartPE);
    }

    if(Uart_GetStatus(M0P_UART3, UartRC))
    {
        Uart_ClrStatus(M0P_UART3, UartRC);

       if (!uart_rx_queue_is_full(uch))
       {
           uch->rx_queue [uch->rx_rear++] = Uart_ReceiveData(M0P_UART3);
           uch->rx_rear %= UART_QUEUE_SIZE;
       }
       else
       {
            Uart_ReceiveData(M0P_UART3);
       }
    }

    if(Uart_GetStatus(M0P_UART3, UartTC))
    {
        Uart_ClrStatus(M0P_UART3, UartTC);

        if (!uart_tx_queue_is_empty(uch))
        {
            uch->uops.send(uch);
        }
        else
        {
            uch->tx_done = true;
        }

    }
}

uint8_t uart_send_data(uint8_t ch, uint8_t *send_buffer, uint8_t length)
{
    uint8_t i;
    uart_channel_st *uch;

    if (ch < UART_CH_MAX)
    {
        uch = &uartCh[ch];
        uch->uops.enabletxirq(uch, FALSE);
        for (i = 0; i < length; i++)
        {
            if(uart_tx_queue_is_full(uch))
            {
                break;
            }
            else
            {
                uch->tx_queue[uch->tx_rear++] = *(send_buffer+i);
                uch->tx_rear %= UART_QUEUE_SIZE;
            }
        }
        if(uch->tx_done)
        {
            uch->uops.send(uch);
        }
        uch->uops.enabletxirq(uch, TRUE);
        
        return i;
    }
    return 0;
}


uint8_t uart_receive_data(uint8_t ch, uint8_t *recv_buf, uint8_t recv_len)
{
    uart_channel_st *uch;
    uint8_t len = 0;

    if(recv_buf == NULL || recv_len == 0 || ch >= UART_CH_MAX)
    {
        return 0;
    }
    uch = &uartCh[ch];
    uch->uops.enablerxirq(uch, FALSE);

    if(uart_rx_queue_is_empty(uch) == TRUE)
    {
        uch->uops.enablerxirq(uch, TRUE);
        return 0;
    }
    else
    {
        while(!uart_rx_queue_is_empty(uch) && recv_len--)
        {
            *recv_buf++ = uch->rx_queue [uch->rx_front++];
            uch->rx_front %= UART_QUEUE_SIZE;
            len++;
        }
    }
    uch->uops.enablerxirq(uch, TRUE);
    return len;
}

uint8_t uart_recv_buffer_is_empty(uint8_t ch)
{
    if (ch < UART_CH_MAX)
    {
        return uart_rx_queue_is_empty(&uartCh[ch]);
    }
    return FALSE;
}

int uart_send_byte(uint8_t ch, unsigned char u8data)
{

    return uart_send_data(ch, &u8data, 1);
}

int uart_recv_byte(uint8_t ch, unsigned char *p_u8data)
{
    return uart_receive_data(ch, p_u8data, 1);
}

static void Board_DeinitUart3(void)
{
    EnableNvic(UART1_3_IRQn, IrqLevel3, FALSE);
    Uart_ClrStatus(M0P_UART3, UartRC);
    Uart_ClrStatus(M0P_UART3, UartTC);
    Uart_DisableIrq(M0P_UART3, UartRxIrq);
    Uart_DisableIrq(M0P_UART3, UartTxIrq);
    Uart_DisableIrq(M0P_UART3, UartTxEIrq);
    Sysctrl_SetPeripheralGate(SysctrlPeripheralUart3, FALSE);
}


static void Board_DeinitTestUsart(void)
{
    EnableNvic(UART0_2_IRQn, IrqLevel3, FALSE);
    Uart_ClrStatus(M0P_UART0, UartRC);
    Uart_ClrStatus(M0P_UART0, UartTC);
    Uart_DisableIrq(M0P_UART0, UartRxIrq);
    Uart_DisableIrq(M0P_UART0, UartTxIrq);
    Uart_DisableIrq(M0P_UART0, UartTxEIrq);
    Sysctrl_SetPeripheralGate(SysctrlPeripheralUart0, FALSE);
}

static int Uart0_EnableRxInt(uart_channel_st *ch, uint8_t en)
{
    if(en)
    {
        Uart_EnableIrq(M0P_UART0, UartRxIrq);
    }
    else
    {
        Uart_DisableIrq(M0P_UART0, UartRxIrq);
    }
    return 0;
}

static int Uart0_EnableTxInt(uart_channel_st *ch, uint8_t en)
{
    if(en)
    {
        EnableNvic(UART0_2_IRQn, IrqLevel3, TRUE);
    }
    else
    {
        EnableNvic(UART0_2_IRQn, IrqLevel3, FALSE);
    }
    return 0;
}

static int Uart0_SendByte(uart_channel_st *ch)
{
    uint8_t index = 0;

    index = ch->tx_front;
    ch->tx_front++;
    ch->tx_front %= UART_QUEUE_SIZE;
    ch->tx_done = false;
    Uart_SendDataIt(M0P_UART0, ch->tx_queue[index]);
    return 0;
}

// Test UART中断函数
void Uart0_IRQHandler(void)
{
    uart_channel_st *uch = &uartCh[UART_CH_TESTUART];

    if(Uart_GetStatus(M0P_UART0, UartFE))
    {
        Uart_ClrStatus(M0P_UART0, UartFE);
    }

    if(Uart_GetStatus(M0P_UART0, UartPE))
    {
        Uart_ClrStatus(M0P_UART0, UartPE);
    }

    if(Uart_GetStatus(M0P_UART0, UartRC))
    {
        Uart_ClrStatus(M0P_UART0, UartRC);

       if (!uart_rx_queue_is_full(uch))
       {
           uch->rx_queue [uch->rx_rear++] = Uart_ReceiveData(M0P_UART0);
           uch->rx_rear %= UART_QUEUE_SIZE;
       }
       else
       {
            Uart_ReceiveData(M0P_UART0);
       }
    }

    if(Uart_GetStatus(M0P_UART0, UartTC))
    {
        Uart_ClrStatus(M0P_UART0, UartTC);

        if (!uart_tx_queue_is_empty(uch))
        {
            uch->uops.send(uch);
        }
        else
        {
            uch->tx_done = true;
        }
    }

}


void Board_InitTestUsart(void)
{
    stc_uart_cfg_t stcCfg;
    uart_channel_st *uch = &uartCh[UART_CH_TESTUART];
    Board_InitSwdGpio(); ///< 配置SWD为UART0
    DDL_ZERO_STRUCT(stcCfg);
    ///< 开启外设时钟
    Sysctrl_SetPeripheralGate(SysctrlPeripheralUart0, TRUE); ///< 使能uart0模块时钟
    ///< UART Init
    stcCfg.enRunMode = UartMskMode1; ///< 模式1
    stcCfg.enStopBit = UartMsk1bit; ///< 1bit停止位
    stcCfg.enMmdorCk = UartMskDataOrAddr; ///< 无检验
    stcCfg.stcBaud.u32Baud = 9600; ///< 波特率9600
    stcCfg.stcBaud.enClkDiv = UartMsk8Or16Div; ///< 通道采样分频配置
    stcCfg.stcBaud.u32Pclk = Sysctrl_GetPClkFreq(); ///< 获得外设时钟（PCLK）频率值
    Uart_Init(M0P_UART0, &stcCfg); ///< 串口初始化

    uch->uops.send = Uart0_SendByte;
    uch->uops.enablerxirq = Uart0_EnableRxInt;
    uch->uops.enabletxirq = Uart0_EnableTxInt;
    uch->tx_done = true;

    ///< UART中断使能
    Uart_ClrStatus(M0P_UART0, UartRC); ///< 清接收请求
    Uart_ClrStatus(M0P_UART0, UartTC); ///< 清接收请求
    Uart_EnableIrq(M0P_UART0, UartRxIrq); ///< 使能串口接收中断
    Uart_EnableIrq(M0P_UART0, UartTxIrq);
    Uart_DisableIrq(M0P_UART0, UartTxEIrq); ///< 禁止串口发送完成中断

    EnableNvic(UART0_2_IRQn, IrqLevel3, TRUE); ///< 系统中断使能
}


void Board_InitUsart(void)
{
    Board_InitLpUart0();
    Board_InitInverterUart();
    Board_InitUart3();
    debug("Board_InitUsart OK\n");
}

void Board_DeinitUsart(void)
{
    Board_DeinitLpUart0();
    Board_DeinitInverterUart();
    Board_DeinitUart3();
    Board_DeinitTestUsart();
}

