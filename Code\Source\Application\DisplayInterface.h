/*!
 * @file
 * @brief This file defines public constants, types and functions for the system manager.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef DISPLAY_INTERFACE_H
#define DISPLAY_INTERFACE_H

#include <stdint.h>
#include <stdbool.h>

enum
{
    eManual_Mode,
    eFuzzy_Mode,
    eTurboCool_Mode,
    eTurboFreeze_Mode,
    eHoliDay_Mode,
    eDeepFreeze_Mode,
    eMax_Mode
};
typedef uint8_t UserMode_t;

void Init_UserMode(void);
void Process_UserMode(void);
void Set_UserMode(UserMode_t mode);
void Set_RefDisable(uint8_t disable);
bool Get_RefDisable(void);
UserMode_t Get_UserMode(void);
bool IsRefTurboCool(void);
bool IsFrzDeepFreeze(void);
void Set_FrzDeepFreeze(uint8_t enable);
void Set_RefTurboCool(uint8_t enable);
uint16_t Get_TurboCoolTimeMinute(void);
uint16_t Get_TurboFreezeTimeMinute(void);
void Update_UserModeTempFrzSet(void);
void Update_UserModeTempRefSet(void);
bool IsFastIceMakerFrzFanNeedRun(void);
#endif
