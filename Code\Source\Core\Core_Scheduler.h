/*!
 * @file
 * @brief This file defines public constants, types and functions for the Core scheduler.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __CORE_SCHEDULER_H__
#define __CORE_SCHEDULER_H__

#include <stdint.h>
#include "Core_Types.h"

#define U16_BOARD_INITIALIZATION_POWERUP_TIMEOUT_SECOND ((uint16_t)10)

/*!
 * @brief This is the type definition for all scheduled functions.
 */
typedef void (*T_Core_Scheduler_Function)(void);

/*!
 * @brief This is the structure for each scheduler entry. Interval is the number of
 * scheduler ticks (milliseconds) in which this function is to be called. ScheduledFunction
 * is the address of the scheduled function. Each entry should have an Interval, and a pointer
 * to the function to be called.
 * @param uint16_t u16_IntervalSeconds : Millisecond portion of the interval in which the specified
 * function is to be called
 * @param uint16_t u16_IntervalMilliSeconds : Seconds portion of the interval in which the specified
 * function is to be called
 * @param T_Core_Scheduler_Function pfScheduledFunction : This is the pointer to the function that
 * is being scheduled.
 */
typedef struct
{
    uint16_t u16_IntervalSeconds;

    uint16_t u16_IntervalMilliSeconds;

    T_Core_Scheduler_Function pfScheduledFunction;
} st_CoreSchedulerItem;

/*!
 * @brief This function initializes the local variables for the scheduler.  Specifically,
 * the timer for each of the scheduled functions is initialized.
 */
extern void Core_Scheduler_Init(void);

/*!
 * @brief This is the entry point for the scheduler.  This function loops through the schedule
 * table checking the timer for each scheduled function for expiration.  When a timer for a
 * scheduled item is expired, the function pointer for that scheduled item (stored in schedule
 * table) is executed.
 */
extern void Core_Scheduler_Execute(void);

#endif
