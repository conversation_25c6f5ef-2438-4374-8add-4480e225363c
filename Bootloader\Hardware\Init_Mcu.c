/*!
 * @file
 * @brief Initialize MCU.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "Adpt_Clock.h"
#include "Adpt_GPIO.h"
#include "Adpt_Timebase.h"
#include "Adpt_Usart.h"
#include "Adpt_Flash.h"
#include "Adpt_Iwdg.h"
#include "Init_Mcu.h"
#include "reset.h"

bool Is_PowerOnReset(void)
{
    uint32_t u32_ResetFlag = 0;

    u32_ResetFlag = M0P_RESET->RESET_FLAG;
    if(u32_ResetFlag & (ResetFlagMskPor5V | ResetFlagMskPor1_5V))
    {
        return true;
    }
    return false;
}

void Init_Mcu(void)
{
    Board_InitClock();
    Board_InitPins();
    Board_InitSysTick();
    Board_InitUsart();
    Board_InitFlash();
    Board_InitIwdg();
}

void DeInit_Mcu(void)
{
    Board_DeinitIwdg();
    Board_DeinitSysTick();
    Board_DeinitUsart();
}
