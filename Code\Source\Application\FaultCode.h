/*!
 * @file
 * @brief This file defines public constants, types and functions for the fault code.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef FAULT_CODE_H
#define FAULT_CODE_H

#include <stdint.h>
#include <stdbool.h>
#include "Core_Types.h"

#define FACTORYFAN_ERROR_COUNT 180

enum
{
    eFCode_IotByte0 = 0,
    eFCode_IotByte1,
    eFCode_IotByte2,
    eFCode_IotByte3,
    eFCode_DoorSwitch,
    eFCode_Inverter,
    eFCode_SnBYTE0,
    eFCode_SnBYTE1,
    eFCode_ParamBYTE0,
    eFCode_ParamBYTE1,
    eFCode_Max
};
typedef uint8_t FaultCodeByteIndex_t;

enum
{
    FAULT_REF_SNR, // 冷藏传感器故障
    FAULT_REFVAR_SNR, // 变温传感器
    FAULT_VAR_TOP_SNR, // 液晶顶部传感器
    FAULT_VAR_BOTTOM_SNR, // 液晶底部传感器
    FAULT_VAR_BOTTOMX_SNR, // 液晶后底部传感器
    FAULT_FRZ_SNR, // 冷冻传感器故障
    FAULT_REFDEF_SNR, //冷藏化霜传感器故障
    FAULT_DEF_SNR, // 冷冻化霜传感器故障
    FAULT_ROOM_SNR, // 环境温度传感器故障
    FAULT_HUM_SNR, // 湿度传感器故障
    FAULT_REF_FAN,  // 冷藏风机故障
    FAULT_FRZ_FAN, // 冷冻风机故障
    FAULT_COOL_FAN, // 冷凝风机故障
    FAULT_DEF_FUNC, // 化霜故障
    FAULT_INVERTER_COMM, // 变频板通信故障
    FAULT_INVERTER, // 变频故障
    FAULT_DOOR_REF_LEFT, // 冷藏左门超时
    FAULT_DOOR_REF_RIGHT, // 冷藏右门超时
    FAULT_DOOR_FRZ_LEFT, // 冷冻左门超时
    FAULT_DOOR_FRZ_RIGHT, // 冷冻右门超时
    FAULT_ICEMAKER_COMM, // 制冰板通信故障
    FAULT_MAX
};
typedef uint8_t FaultType_t;

typedef struct
{
    uint8_t u8_FaultType;
    uint8_t u8_ByteNum;
    uint8_t u8_BitValue;
} DispFault_st;


/*!
 * @brief 1s period
 */
void Collect_FaultCode(void);
uint8_t Get_FaultCodeByte(uint8_t index);
uint8_t Get_FaultCodeNumber(void);

#endif
