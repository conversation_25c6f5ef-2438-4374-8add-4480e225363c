/*=====================================================================@@ms=*/
/*                                   IOTUSR                                 */
/*                                                                          */
/*=====================================================================@@mi=*/
/* author    : ZW                                                           */
/*=====================================================================@@me=*/
#ifndef IOTUSR_H
#define IOTUSR_H

/*=====================================================================@@gs=*/
/*                                 Includes                                 */
/*=====================================================================@@ge=*/
//#include "UserTypes.h"
//#include "Drivers\Include\Source.h"
#include "stdint.h"
#include <stdbool.h>
#include "base_types.h"

//#define RTT_LOG_DEBUG

#ifdef  RTT_LOG_DEBUG

#include "SEGGER_RTT.h"

#define log_printf         SEGGER_RTT_printf
#define log_WriteString    SEGGER_RTT_WriteString
#else
#define log_printf         log_printf_dump
#define log_WriteString    log_WriteString_dump
#endif


/*=====================================================================@@gb=*/

/*=====================================================================@@gs=*/
/*                             Type definitions                             */
/*=====================================================================@@ge=*/
typedef unsigned char  uint8;
typedef uint16_t   uint16;
typedef unsigned long uint32;

typedef signed char sint8;
typedef signed int  sint16;
typedef signed long sint32;

typedef signed char TBool;
typedef signed char TByte;
typedef int16_t     TShort;
typedef signed int TWord;
typedef signed long TLong;
typedef float TFSingle;
typedef char TChar;
typedef signed int TSWord;


/*=====================================================================@@gs=*/
/*                   External variable declarations (extern)                */
/*=====================================================================@@gb=*/


/*=====================================================================@@gs=*/
/*                     Function declarations (prototypes)                   */
/*=====================================================================@@ge=*/

/*=====================================================================@@gs=*/
/*             Macro definitions and manifest constants (#define)           */
/*=====================================================================@@ge=*/

/*=====================================================================@@gb=*/
#endif
/*=====================================================================@@mb=*/

