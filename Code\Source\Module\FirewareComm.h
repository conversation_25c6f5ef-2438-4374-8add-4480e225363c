#ifndef __FIREWARE_COMM_H__
#define __FIREWARE_COMM_H__

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include <stdio.h>
#include "Core_Types.h"

#define FIREWARE_SUCCESS (0)
#define FIREWARE_ERROR (-1)
#define FIREWARE_FRAME_HEAD ((uint8_t)0xA5)
#define FIREWARE_FRAME_END ((uint8_t)0x5A)
#define FIREWARE_FRAME_DATA_MAX ((uint8_t)128)
#define FIREWARE_FRAME_LEN_MAX ((uint8_t)137)
#define FIREWARE_FRAME_SUCCESS 0
#define FIREWARE_FRAME_TIMEOUT -1
#define FIREWARE_FRAME_SENDBUSY -2
#define FIREWARE_FRAME_SENDERROR -3
#define FIREWARE_FRAME_RESPONSE_ACK 1
#define FIREWARE_FRAME_RESPONSE_NACK 2
#define FIREWARE_FRAME_RETRY 5
#define FIREWARE_MAX_NUMS 8
#define FIREWARE_PROPERTY_STRING_LEN 18
#define FIREWARE_PROPERTY_NUM_TRANSFER 10
#define FIREWARE_PROPERTY_ERROR_MAX 5
#define FIREWARE_PROPERTY_DID_LEN 15
#define FIREWARE_PROPERTY_MAC_LEN 13
#define FIREWARE_PROPERTY_MODEL_LEN 32
#define FIREWARE_PARAM_SUCCESS (0)
#define FIREWARE_PARAM_END (1)
#define FIREWARE_PARAM_ERROR (-1)
#define FIREWARE_PARAM_TIMEOUT (-2)
#define FIREWARE_PARAM_PACKET_LEN 128

#define PROPERTY_DIR_IN      (1 << 0)
#define PROPERTY_DIR_OUT (1 << 1)
#define PROPERTY_ALLDIRITY_FILTER (1 << 2)
#define PROPERTY_DIR_INOUT (PROPERTY_DIR_IN | PROPERTY_DIR_OUT)

typedef void (*callbackParamResult)(uint8_t* value, int8_t ret);

// 帧格式
typedef enum
{
    FIREWARE_FRAME_STATE_HEAD = 0, // 报文头
    FIREWARE_FRAME_STATE_SRC, // 报文源地址
    FIREWARE_FRAME_STATE_DEST, // 报文目的地址
    FIREWARE_FRAME_STATE_FUNCBYTE1, // 报文功能号1
    FIREWARE_FRAME_STATE_FUNCBYTE2, // 报文功能号2
    FIREWARE_FRAME_STATE_FUNCBYTE3, // 报文功能号3
    FIREWARE_FRAME_STATE_LENGTH, // 报文长度
    FIREWARE_FRAME_STATE_DATA, // 正常报文数据
    FIREWARE_FRAME_STATE_OVER, // 完成
    INVERTER_FRAME_STATE_MAX,
} fireware_state_em;

typedef struct
{
    uint8_t head;
    uint8_t srcAddr;
    uint8_t destAddr;
    uint8_t fb1;
    uint8_t fb2;
    uint8_t fb3;
    uint8_t len;
    uint8_t data[FIREWARE_FRAME_DATA_MAX];
    uint8_t crch;
    uint8_t crcl;
    uint8_t end;
    bool wait_response;
} fireware_frame_st;

typedef enum
{
    FIREWARE_OTA_FUNC = 0xDD,
    INVERTER_APP_FUNC = 0xAA,
    FIREWARE_PROPERTY_FUNC = 0xBB,
    FIREWARE_PARAM_FUNC = 0xEE,
} fireware_func_e;

typedef enum
{
    PARAM_SUBFUNC_WRITE_SN = 0x00,
    PARAM_SUBFUNC_READ_SN = 0x01,
    PARAM_SUBFUNC_WRITE_PARAM = 0x2,
    PARAM_SUBFUNC_READ_PARAM = 0x3,
    PARAM_SUBFUNC_CHECK_PARAM_CRC = 0x4,
    PARAM_SUBFUNC_READ_PARAM_CRC = 0x5,
} param_subfunc_e;

typedef enum
{
    FIREWARE_OTA_QUERY_VER = 0x1,
    FIREWARE_OTA_RESET = 0x2,
    FIREWARE_OTA_JUMP_BOOT = 0x3,
    FIREWARE_OTA_LOAD_START = 0x4,
    FIREWARE_OTA_LOAD_END = 0x5,
    FIREWARE_OTA_CHECKSUM = 0x6,
    FIREWARE_OTA_JUMP_APP = 0x7,
    FIREWARE_OTA_LOAD_DATA = 0x8,
    FIREWARE_OTA_RUN_STATE = 0x9,
    FIREWARE_OTA_BOOT_VER = 0xa,
} fireware_ota_subfunc_e;

typedef enum
{
    FIREWARE_ADDR_NONE = 0x0,
    FIREWARE_MAIN_ADDR = 0x1,
    FIREWARE_DISPLAY_ADDR = 0x2,
    FIREWARE_PARTIALFRZ_ADDR = 0x3,
    FIREWARE_ICEMAKER_ADDR = 0x4,
    FIREWARE_INVERTER_ADDR = 0x5,
    FIREWARE_NFC_ADDR = 0x6,
} fireware_addr_e;

typedef struct
{
    uint16_t hwVersion;
    uint16_t appVersion;
    uint16_t appCrc;
} fireware_version_st;

typedef enum
{
    FIREWARE_RESPONSE_ACK = 1,
    FIREWARE_RESPONSE_NACK = 2,
    FIREWARE_RESPONSE_TIMEOUT = 3,
    FIREWARE_RESPONSE_ERROR = 4,
} fireware_response_e;

typedef enum
{
    PROPERTY_SUBFUNC_QUERY = 0x1,
    PROPERTY_SUBFUNC_IOT = 0x2,
    PROPERTY_SUBFUNC_QUERY_RESPONSE = 0x2,
    PROPERTY_SUBFUNC_RESPONSE = 0x3,
} property_subfunc_e;

typedef enum
{
    PROPERTY_VAL_STRING = 0x0,
    PROPERTY_VAL_UINT32 = 0x1,
    PROPERTY_VAL_UINT8 = 0x2,
    PROPERTY_VAL_INT8 = 0x3,
    PROPERTY_VAL_UINT16 = 0x4,
    PROPERTY_VAL_INT16 = 0x5,
    PROPERTY_VAL_MAC = 0x6,
    PROPERTY_VAL_DID = 0x7,
    PROPERTY_VAL_MODEL = 0x8,
} property_val_e;

typedef enum
{
    PROPERTY_RESPONSE_SUCCESS = 0,
    PROPERTY_RESPONSE_DOING,
    PROPERTY_RESPONSE_UNREADABLE,
    PROPERTY_RESPONSE_UNWRITEABLE,
    PROPERTY_RESPONSE_UNEXSIT,
    PROPERTY_RESPONSE_INERROR,
    PROPERTY_RESPONSE_PROPERTYERROR,
} property_reponse_e;

typedef struct
{
    property_val_e val;
    uint8_t sid;
    uint8_t pid;
    void *data;
    uint8_t flags;
    bool dirty;
    void (*dev_to_iot)(void *data);
    void (*iot_to_dev)(property_reponse_e result);
    bool (*update_property)(void);
    bool dirty1;
    uint8_t errcount;
} fireware_property_st;

void Edit_OtaControlFrame(fireware_frame_st *frame, fireware_addr_e dest, 
    fireware_ota_subfunc_e func);
uint16_t convert_from_bigendian16(uint16_t val);
uint32_t convert_from_bigendian32(uint32_t val);
uint32_t convert_to_bigendian32(uint32_t val);
uint16_t convert_to_bigendian16(uint16_t val);
void get_Property_Val(fireware_property_st *prop, uint8_t *data);
void set_Property_Val(fireware_property_st *prop, uint8_t *data);
uint8_t get_Property_Size(property_val_e val);
void Edit_Property_QueryFrame(fireware_addr_e dest, fireware_frame_st *frame);
void Edit_Property_ResponseFrame(fireware_addr_e dest, fireware_frame_st *frame,
                                uint8_t *rdata, uint8_t rlen);
void Edit_Property_Frame(fireware_addr_e dest, fireware_frame_st *frame,
                        fireware_property_st *propertys, uint8_t size);
fireware_property_st *get_Property_BySidPid(uint8_t sid, uint8_t pid,
                                            fireware_property_st *propertys, uint8_t size);
uint8_t *Parse_PropertyData(uint8_t *data, uint16_t len, uint16_t *rlen,
                            fireware_property_st *propertys, uint8_t size);
void Parse_PropertyResponse(uint8_t *data, uint16_t len,
                            fireware_property_st *propertys, uint8_t size);
#endif
