/*!
 * @file
 * @brief Manages all the state variables of the zone parameter.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "Parameter_TemperatureZone.h"
#include "SystemManager.h"
#include "FridgeRunner.h"
#include "ParameterManager.h"
#include "MaintenanceManager.h"
#include "Driver_AdSample.h"
#include "DisplayInterface.h"
#include "CoolingCycle.h"
#include "Driver_Flash.h"
#include "FridgeRunner.h"
#include "Sbus_Display.h"
#include "Sbus_IceMaker.h"
#include "Vartemp_State.h"
#include "Defrosting.h"
#include "CloudControl.h"
#include "Drive_Valve.h"

static uint8_t u8_RefVarSetTemp = (RefVarSet_t)REFVAR_LEVEL_OFFSET;
static uint8_t u8_RefSetTemp = (uint8_t)REF_LEVEL_5;
static uint8_t u8_FrzSetTemp = (uint8_t)FRZ_LEVEL_F18;
static bool b_param_synced = false;
static bool b_ref_param_fixed = false;
static bool b_frz_param_fixed = false;
bool b_ref_fan_advance = false;
bool b_frz_fan_advance = false;
bool b_var_fan_advance = false;
bool b_cond_fan_advance = false;
ZoneOnOffTemp_st ary_RefOnOffTempFix[RT_MAXSIZE][U8_REF_LEVEL_LENGTH];
ZoneOnOffTime_st ary_RefSnrErrorOnOffTimeFix[RT_MAXSIZE];
ZoneOnOffTemp_st ary_FrzOnOffTempFix[RT_MAXSIZE][U8_FRZ_LEVEL_LENGTH];
ZoneOnOffTime_st ary_FrzSnrErrorOnOffTimeFix[RT_MAXSIZE];
ZoneOnOffTemp_st ary_TurboFrzOnOffTempFix[RT_MAXSIZE];
Zone_st st_Zone;

static const ZoneOnOffTemp_st ary_RefOnOffTemp[][U8_REF_LEVEL_LENGTH] = {
    //  on temp   off temp
    {
        // RT <= 13
        { 500 + 35, 500 + 25 }, // REF_LEVEL_2
        { 500 + 40, 500 + 30 }, // REF_LEVEL_3
        { 500 + 55, 500 + 45 }, // REF_LEVEL_4
        { 500 + 65, 500 + 55 }, // REF_LEVEL_5
        { 500 + 80, 500 + 70 }, // REF_LEVEL_6
        { 500 + 90, 500 + 80 }, // REF_LEVEL_7
        { 500 + 95, 500 + 85 }, // REF_LEVEL_8
    },
    //  on temp   off temp
    {
        // 13 < RT <= 18
        { 500 + 35, 500 + 25 }, // REF_LEVEL_2
        { 500 + 40, 500 + 30 }, // REF_LEVEL_3
        { 500 + 55, 500 + 45 }, // REF_LEVEL_4
        { 500 + 65, 500 + 55 }, // REF_LEVEL_5
        { 500 + 80, 500 + 70 }, // REF_LEVEL_6
        { 500 + 90, 500 + 80 }, // REF_LEVEL_7
        { 500 + 95, 500 + 85 }, // REF_LEVEL_8
    },
    //  on temp   off temp
    {
        // 18 < RT <= 23
        { 500 + 35, 500 + 25 }, // REF_LEVEL_2
        { 500 + 40, 500 + 30 }, // REF_LEVEL_3
        { 500 + 55, 500 + 45 }, // REF_LEVEL_4
        { 500 + 65, 500 + 55 }, // REF_LEVEL_5
        { 500 + 80, 500 + 70 }, // REF_LEVEL_6
        { 500 + 90, 500 + 80 }, // REF_LEVEL_7
        { 500 + 95, 500 + 85 }, // REF_LEVEL_8
    },
    // on temp   off temp
    {
        // 23 < RT <= 28
        { 500 + 30, 500 + 20 }, // REF_LEVEL_2
        { 500 + 35, 500 + 25 }, // REF_LEVEL_3
        { 500 + 50, 500 + 40 }, // REF_LEVEL_4
        { 500 + 60, 500 + 50 }, // REF_LEVEL_5
        { 500 + 75, 500 + 65 }, // REF_LEVEL_6
        { 500 + 85, 500 + 75 }, // REF_LEVEL_7
        { 500 + 90, 500 + 80 }, // REF_LEVEL_8
    },
    // on temp   off temp
    {
        // 28 < RT <= 35
        { 500 + 30, 500 + 20 }, // REF_LEVEL_2
        { 500 + 35, 500 + 25 }, // REF_LEVEL_3
        { 500 + 50, 500 + 40 }, // REF_LEVEL_4
        { 500 + 60, 500 + 50 }, // REF_LEVEL_5
        { 500 + 75, 500 + 65 }, // REF_LEVEL_6
        { 500 + 85, 500 + 75 }, // REF_LEVEL_7
        { 500 + 90, 500 + 80 }, // REF_LEVEL_8
    },
    // on temp   off temp
    {
        // 35 < RT <= 40
        { 500 + 30, 500 + 20 }, // REF_LEVEL_2
        { 500 + 35, 500 + 25 }, // REF_LEVEL_3
        { 500 + 50, 500 + 40 }, // REF_LEVEL_4
        { 500 + 60, 500 + 50 }, // REF_LEVEL_5
        { 500 + 75, 500 + 65 }, // REF_LEVEL_6
        { 500 + 85, 500 + 75 }, // REF_LEVEL_7
        { 500 + 90, 500 + 80 }, // REF_LEVEL_8
    },
    // on temp   off temp
    {
        // 40 < RT
        { 500 + 30, 500 + 20 }, // REF_LEVEL_2
        { 500 + 35, 500 + 25 }, // REF_LEVEL_3
        { 500 + 50, 500 + 40 }, // REF_LEVEL_4
        { 500 + 60, 500 + 50 }, // REF_LEVEL_5
        { 500 + 75, 500 + 65 }, // REF_LEVEL_6
        { 500 + 85, 500 + 75 }, // REF_LEVEL_7
        { 500 + 90, 500 + 80 }, // REF_LEVEL_8
    },
};

static const ZoneOnOffTime_st ary_RefSnrErrorOnOffTime[] = {
    //  on time   off time
    { 60 * 10, 60 * 165 }, // RT <= 13
    { 60 * 15, 60 * 130 }, // 13 < RT <= 18
    { 60 * 21, 60 * 72 }, // 18 < RT <= 23
    { 60 * 21, 60 * 72 }, // 23 < RT <= 28
    { 60 * 21, 60 * 50 }, // 28 < RT <= 35
    { 60 * 22, 60 * 41 }, // 35 < RT <= 40
    { 60 * 26, 60 * 44 }, // 40 < RT
};

static const ZoneOnOffTime_st ary_VarSnrErrorOnOffTime[] = {
    //  on time   off time
    { 60 * 2, 60 * 13 }, // RT <= 13
    { 60 * 2, 60 * 11 }, // 13 < RT <= 18
    { 60 * 3, 60 * 16 }, // 18 < RT <= 23
    { 60 * 3, 60 * 16 }, // 23 < RT <= 28
    { 60 * 3, 60 * 15 }, // 28 < RT <= 35
    { 60 * 3, 60 * 14 }, // 35 < RT <= 40
    { 60 * 3, 60 * 11 }, // 40 < RT
};

static const ZoneOnOffTime_st ary_FrzSnrErrorOnOffTime[] = {
    //  on time   off time
    { 60 * 9, 60 * 19 }, // RT <= 13
    { 60 * 12, 60 * 30 }, // 13 < RT <= 18
    { 60 * 20, 60 * 40 }, // 18 < RT <= 23
    { 60 * 20, 60 * 40 }, // 23 < RT <= 28
    { 60 * 27, 60 * 46 }, // 28 < RT <= 35
    { 60 * 24, 60 * 38 }, // 35 < RT <= 40
    { 60 * 35, 60 * 52 }, // 40 < RT
};

static const ZoneOnOffTemp_st ary_FrzOnOffTemp[][U8_FRZ_LEVEL_LENGTH] = {
    //  on temp   off temp
    {
        // RT <= 13
        { 500 - 235, 500 - 255 }, // FRZ_LEVEL_F24
        { 500 - 225, 500 - 245 }, // FRZ_LEVEL_F23
        { 500 - 215, 500 - 235 }, // FRZ_LEVEL_F22
        { 500 - 205, 500 - 225 }, // FRZ_LEVEL_F21
        { 500 - 195, 500 - 215 }, // FRZ_LEVEL_F20
        { 500 - 180, 500 - 200 }, // FRZ_LEVEL_F19
        { 500 - 175, 500 - 195 }, // FRZ_LEVEL_F18
        { 500 - 165, 500 - 185 }, // FRZ_LEVEL_F17
        { 500 - 155, 500 - 175 }, // FRZ_LEVEL_F16
    },
    //  on temp   off temp
    {
        // 13 < RT <= 18
        { 500 - 235, 500 - 255 }, // FRZ_LEVEL_F24
        { 500 - 225, 500 - 245 }, // FRZ_LEVEL_F23
        { 500 - 215, 500 - 235 }, // FRZ_LEVEL_F22
        { 500 - 205, 500 - 225 }, // FRZ_LEVEL_F21
        { 500 - 195, 500 - 215 }, // FRZ_LEVEL_F20
        { 500 - 180, 500 - 200 }, // FRZ_LEVEL_F19
        { 500 - 175, 500 - 195 }, // FRZ_LEVEL_F18
        { 500 - 165, 500 - 185 }, // FRZ_LEVEL_F17
        { 500 - 155, 500 - 175 }, // FRZ_LEVEL_F16

    },
    //  on temp   off temp
    {
        // 18 < RT <= 23
        { 500 - 225, 500 - 245 }, // FRZ_LEVEL_F24
        { 500 - 215, 500 - 235 }, // FRZ_LEVEL_F23
        { 500 - 205, 500 - 225 }, // FRZ_LEVEL_F22
        { 500 - 195, 500 - 215 }, // FRZ_LEVEL_F21
        { 500 - 185, 500 - 205 }, // FRZ_LEVEL_F20
        { 500 - 180, 500 - 200 }, // FRZ_LEVEL_F19
        { 500 - 175, 500 - 195 }, // FRZ_LEVEL_F18
        { 500 - 165, 500 - 185 }, // FRZ_LEVEL_F17
        { 500 - 155, 500 - 175 }, // FRZ_LEVEL_F16
    },
    // on temp   off temp
    {
        // 23 < RT <= 28
        { 500 - 220, 500 - 250 }, // FRZ_LEVEL_F24
        { 500 - 210, 500 - 240 }, // FRZ_LEVEL_F23
        { 500 - 200, 500 - 230 }, // FRZ_LEVEL_F22
        { 500 - 190, 500 - 220 }, // FRZ_LEVEL_F21
        { 500 - 180, 500 - 210 }, // FRZ_LEVEL_F20
        { 500 - 175, 500 - 205 }, // FRZ_LEVEL_F19
        { 500 - 170, 500 - 200 }, // FRZ_LEVEL_F18
        { 500 - 160, 500 - 190 }, // FRZ_LEVEL_F17
        { 500 - 150, 500 - 180 }, // FRZ_LEVEL_F16
    },
    // on temp   off temp
    {
        // 28 < RT <= 35
        { 500 - 220, 500 - 250 }, // FRZ_LEVEL_F24
        { 500 - 210, 500 - 240 }, // FRZ_LEVEL_F23
        { 500 - 200, 500 - 230 }, // FRZ_LEVEL_F22
        { 500 - 190, 500 - 220 }, // FRZ_LEVEL_F21
        { 500 - 180, 500 - 210 }, // FRZ_LEVEL_F20
        { 500 - 175, 500 - 205 }, // FRZ_LEVEL_F19
        { 500 - 170, 500 - 200 }, // FRZ_LEVEL_F18
        { 500 - 160, 500 - 190 }, // FRZ_LEVEL_F17
        { 500 - 150, 500 - 180 }, // FRZ_LEVEL_F16
    },
    // on temp   off temp
    {
        // 35 < RT <= 40
        { 500 - 230, 500 - 260 }, // FRZ_LEVEL_F24
        { 500 - 210, 500 - 240 }, // FRZ_LEVEL_F23
        { 500 - 200, 500 - 230 }, // FRZ_LEVEL_F22
        { 500 - 190, 500 - 220 }, // FRZ_LEVEL_F21
        { 500 - 180, 500 - 210 }, // FRZ_LEVEL_F20
        { 500 - 175, 500 - 205 }, // FRZ_LEVEL_F19
        { 500 - 170, 500 - 200 }, // FRZ_LEVEL_F18
        { 500 - 160, 500 - 190 }, // FRZ_LEVEL_F17
        { 500 - 150, 500 - 180 }, // FRZ_LEVEL_F16
    },
    // on temp   off temp
    {
        // 40 < RT
        { 500 - 230, 500 - 260 }, // FRZ_LEVEL_F24
        { 500 - 220, 500 - 250 }, // FRZ_LEVEL_F23
        { 500 - 210, 500 - 240 }, // FRZ_LEVEL_F22
        { 500 - 200, 500 - 230 }, // FRZ_LEVEL_F21
        { 500 - 190, 500 - 220 }, // FRZ_LEVEL_F20
        { 500 - 185, 500 - 215 }, // FRZ_LEVEL_F19
        { 500 - 175, 500 - 205 }, // FRZ_LEVEL_F18
        { 500 - 165, 500 - 195 }, // FRZ_LEVEL_F17
        { 500 - 155, 500 - 185 }, // FRZ_LEVEL_F16
    },
};

static const ZoneOnOffTemp_st ary_TurboFrzOnOffTemp[] = {
    //  on temp   off temp
    { 500 - 305, 500 - 325 }, // RT <= 13
    { 500 - 305, 500 - 325 }, // 13 < RT <= 18
    { 500 - 305, 500 - 325 }, // 18 < RT <= 23
    { 500 - 300, 500 - 330 }, // 23 < RT <= 28
    { 500 - 300, 500 - 330 }, // 28 < RT <= 35
    { 500 - 300, 500 - 330 }, // 35 < RT <= 40
    { 500 - 300, 500 - 330 }, // 40 < RT
};

void Update_RefVarSetTemp(uint8_t u8_SetTemp)
{
    uint8_t set_temp;

    if(u8_SetTemp >= (RefVarSet_t)eRefVar_Max || u8_SetTemp  < REFVAR_LEVEL_OFFSET)
    {
        set_temp = (RefVarSet_t)REFVAR_LEVEL_OFFSET;
    }
    else
    {
        set_temp = u8_SetTemp;
    }
    u8_RefVarSetTemp = set_temp;
    UpdateVartempOffset();
    SetSysParam(SYSPARAM_INFANT_MODE, u8_RefVarSetTemp);
}

void Update_RefSetTempNotSave(uint8_t u8_SetTemp)
{
    uint8_t set_temp;
    uint8_t mode;
    if(u8_SetTemp <= U8_REF_LEVEL_MIN)
    {
        set_temp = U8_REF_LEVEL_MIN;
    }
    else if(u8_SetTemp > U8_REF_LEVEL_MAX)
    {
        set_temp = U8_REF_LEVEL_MAX;
    }
    else
    {
        set_temp = u8_SetTemp;
    }
    u8_RefSetTemp = set_temp;
}

void Update_RefSetTemp(uint8_t u8_SetTemp)
{
    uint8_t set_temp;
    uint8_t mode;
    if(u8_SetTemp <= U8_REF_LEVEL_MIN)
    {
        set_temp = U8_REF_LEVEL_MIN;
    }
    else if(u8_SetTemp > U8_REF_LEVEL_MAX)
    {
        set_temp = U8_REF_LEVEL_MAX;
    }
    else
    {
        set_temp = u8_SetTemp;
    }
    u8_RefSetTemp = set_temp;
    Clean_PeekValleyRefSet();
    if(false == IsRefTurboCool())
    {
        SetSysParam(SYSPARAM_REFTEMP, u8_RefSetTemp);
    }
}

void Update_FrzSetTempNotSave(uint8_t u8_SetTemp)
{
    uint8_t set_temp;
    uint8_t mode;
    if(u8_SetTemp <= U8_FRZ_LEVEL_MIN)
    {
        set_temp = U8_FRZ_LEVEL_MIN;
    }
    else if((u8_SetTemp > U8_FRZ_LEVEL_MIN) && (u8_SetTemp < U8_FRZ_ON_OFFLEVEL_MIN))
    {
        set_temp = U8_FRZ_ON_OFFLEVEL_MIN;
    }
    else if(u8_SetTemp > U8_FRZ_LEVEL_MAX)
    {
        set_temp = U8_FRZ_LEVEL_MAX;
    }
    else
    {
        set_temp = u8_SetTemp;
    }
    u8_FrzSetTemp = set_temp;
}


void Update_FrzSetTemp(uint8_t u8_SetTemp)
{
    uint8_t set_temp;
    uint8_t mode;
    if(u8_SetTemp <= U8_FRZ_LEVEL_MIN)
    {
        set_temp = U8_FRZ_LEVEL_MIN;
    }
    else if((u8_SetTemp > U8_FRZ_LEVEL_MIN) && (u8_SetTemp < U8_FRZ_ON_OFFLEVEL_MIN))
    {
        set_temp = U8_FRZ_ON_OFFLEVEL_MIN;
    }
    else if(u8_SetTemp > U8_FRZ_LEVEL_MAX)
    {
        set_temp = U8_FRZ_LEVEL_MAX;
    }
    else
    {
        set_temp = u8_SetTemp;
    }
    u8_FrzSetTemp = set_temp;
    Clean_PeekValleyFrzSet();
    if(false == IsFrzDeepFreeze() &&
       u8_SetTemp >= U8_FRZ_ON_OFFLEVEL_MIN)
    {
        SetSysParam(SYSPARAM_FRZTEMP, u8_FrzSetTemp);
    }
}

static uint8_t Get_RefOnOffSetTempIndex(RoomTempRange_t room_range)
{
    bool b_condensation_mode = Get_CondensationModeState();
    uint8_t ref_onoff_set_temp = U8_REF_LEVEL_MIN;
    uint8_t ref_temp_index = 0;

    if(u8_RefSetTemp >= U8_REF_LEVEL_MIN && u8_RefSetTemp <= U8_REF_LEVEL_MAX)
    {
        ref_onoff_set_temp = u8_RefSetTemp;
    }

    if(b_condensation_mode && 
       (u8_RefSetTemp >= REF_LEVEL_2) &&
       (u8_RefSetTemp <= REF_LEVEL_4))
    {
        ref_onoff_set_temp = REF_LEVEL_5;
    }

    if(Is_SmartGrid_ReduceLoadOn())
    {
        ref_onoff_set_temp = REF_LEVEL_8;
    }

    ref_temp_index = ref_onoff_set_temp - U8_REF_LEVEL_MIN;
    return (ref_temp_index);
}

static bool Fix_RefOnOffTemp(parameter_section_st *psection)
{
    uint8_t rt;
    uint16_t value;
    uint16_t params;
    uint8_t reflevel;
    uint16_t index = 0;
    uint8_t *data = psection->data;

    params = RT_MAXSIZE * U8_REF_LEVEL_LENGTH * 2 + RT_MAXSIZE * 2;
    if(params > psection->length - 3)
    {
        return false;
    }

    for(rt = RT_BELOW13; rt < RT_MAXSIZE; rt++)
    {
        for(reflevel = U8_REF_LEVEL_MIN; reflevel <= U8_REF_LEVEL_MAX; reflevel++)
        {
            value = data[index++] << 8;
            value |= data[index++];
            ary_RefOnOffTempFix[rt][reflevel - U8_REF_LEVEL_MIN].u16_OnTemp = value;
            value = data[index++] << 8;
            value |= data[index++];
            ary_RefOnOffTempFix[rt][reflevel - U8_REF_LEVEL_MIN].u16_OffTemp = value;
        }
    }

    for(rt = RT_BELOW13; rt < RT_MAXSIZE; rt++)
    {
        value = data[index++] << 8;
        value |= data[index++];
        ary_RefSnrErrorOnOffTimeFix[rt].u16_OnTime = value * 60;
        value = data[index++] << 8;
        value |= data[index++];
        ary_RefSnrErrorOnOffTimeFix[rt].u16_OffTime = value * 60;
    }
    return true;
}

static bool Fix_FrzOnOffTemp(parameter_section_st *psection)
{
    uint8_t rt;
    uint16_t value;
    uint16_t params;
    uint8_t frzlevel;
    uint16_t index = 0;
    uint8_t *data = psection->data;

    params = RT_MAXSIZE * U8_FRZ_LEVEL_LENGTH * 2 + RT_MAXSIZE * 2 * 2;
    if(params > psection->length - 3)
    {
        return false;
    }

    for(rt = RT_BELOW13; rt < RT_MAXSIZE; rt++)
    {
        for(frzlevel = U8_FRZ_ON_OFFLEVEL_MIN; frzlevel <= U8_FRZ_LEVEL_MAX; frzlevel++)
        {
            value = data[index++] << 8;
            value |= data[index++];
            ary_FrzOnOffTempFix[rt][frzlevel - U8_FRZ_ON_OFFLEVEL_MIN].u16_OnTemp = value;
            value = data[index++] << 8;
            value |= data[index++];
            ary_FrzOnOffTempFix[rt][frzlevel - U8_FRZ_ON_OFFLEVEL_MIN].u16_OffTemp = value;
        }
    }

    for(rt = RT_BELOW13; rt < RT_MAXSIZE; rt++)
    {
        value = data[index++] << 8;
        value |= data[index++];
        ary_FrzSnrErrorOnOffTimeFix[rt].u16_OnTime = value * 60;
        value = data[index++] << 8;
        value |= data[index++];
        ary_FrzSnrErrorOnOffTimeFix[rt].u16_OffTime = value * 60;
    }

    for(rt = RT_BELOW13; rt < RT_MAXSIZE; rt++)
    {
        value = data[index++] << 8;
        value |= data[index++];
        ary_TurboFrzOnOffTempFix[rt].u16_OnTemp = value;
        value = data[index++] << 8;
        value |= data[index++];
        ary_TurboFrzOnOffTempFix[rt].u16_OffTemp = value;
    }
    return true;
}

static void JudgeFanAdvance(void)
{
    if(b_var_fan_advance == false &&
       st_Zone.b_RefVarSnrError == false &&
       st_Zone.u16_RefVarSnrTemp > st_Zone.u16_RefVarOnTemp + VAR_SNR_FAN_ADVANCE_TEMP_OFFSET)
    {
        b_var_fan_advance = true;
    }
    else if(b_var_fan_advance == true &&
         st_Zone.b_RefVarSnrError == false &&
         st_Zone.u16_RefVarSnrTemp < st_Zone.u16_RefVarOffTemp)
    {
        b_var_fan_advance = false;
    }
    else if(st_Zone.b_RefVarSnrError == true)
    {
        b_var_fan_advance = false;
    }

    if(eFridge_Running != Get_FridgeState() ||
       eRunning_CoolingCycle != Get_RunningState() ||
       eCoolingCapacity_Normal != Get_CoolingCapacityState())
    {
        b_ref_fan_advance = false;
        b_frz_fan_advance = false;
        b_cond_fan_advance = false;
        return;
    }

    if(b_ref_fan_advance == false &&
       st_Zone.b_RefSnrError == false &&
       st_Zone.u16_RefSnrTemp > st_Zone.u16_RefOnTemp + REF_SNR_FAN_ADVANCE_TEMP_OFFSET)
    {
        b_ref_fan_advance = true;
    }
    else if(b_ref_fan_advance == true &&
            st_Zone.b_RefSnrError == false &&
            st_Zone.u16_RefSnrTemp < st_Zone.u16_RefOffTemp)
    {
        b_ref_fan_advance = false;
    }
    else if(st_Zone.b_RefSnrError == true)
    {
        b_ref_fan_advance = false;
    }

    if(b_frz_fan_advance == false &&
       st_Zone.b_FrzSnrError == false &&
       st_Zone.u16_FrzSnrTemp > st_Zone.u16_FrzOnTemp + FRZ_SNR_FAN_ADVANCE_TEMP_OFFSET)
    {
        b_frz_fan_advance = true;
    }
    else if(b_frz_fan_advance == true &&
            st_Zone.b_FrzSnrError == false &&
            st_Zone.u16_FrzSnrTemp < st_Zone.u16_FrzOffTemp)
    {
        b_frz_fan_advance = false;
    }
    else if(st_Zone.b_FrzSnrError == true)
    {
        b_frz_fan_advance = false;
    }

    b_cond_fan_advance = false;
    if(b_ref_fan_advance || b_frz_fan_advance)
    {
        b_cond_fan_advance = true;
    }
}

void Update_TempParameter(void)
{
    parameter_section_st *psection = NULL;
    RoomTempRange_t room_range = Get_RoomTempRange();
    uint8_t icemaker_mode = eIceMaker_Stop_Mode;
    uint8_t ref_temp_index = 0;
    uint8_t frz_temp_index = 0;
    bool b_ref_disable = Get_RefDisable();
    CoolingEntryMode_t entry_mode = Get_CoolingEntryMode();
    uint16_t cycle_number = Get_CoolingCycleNumber();
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    bool b_condensation_mode = Get_CondensationModeState();
    CoolingCompState_t comp_state = Get_CoolingCompState();	
    bool b_power_on_delay = Get_PowerOnDelayRefCoolingState();
    const ZoneOnOffTime_st *ref_snrerror_tab = ary_RefSnrErrorOnOffTime;
    const ZoneOnOffTemp_st (*ref_onoff_tab)[U8_REF_LEVEL_LENGTH] = ary_RefOnOffTemp;
    const ZoneOnOffTime_st *frz_snrerror_tab = ary_FrzSnrErrorOnOffTime;
    const ZoneOnOffTemp_st (*frz_onoff_tab)[U8_FRZ_LEVEL_LENGTH] = ary_FrzOnOffTemp;
    const ZoneOnOffTemp_st *tubro_frz_tab = ary_TurboFrzOnOffTemp;
    bool b_strong_cool = Get_Strong_Cool();
    uint16_t adjust;

    if(b_param_synced == false && IsParameterManagerReady())
    {
        psection = GetParameterSection(PARAMETER_SECTION_REF);
        if(NULL != psection && Fix_RefOnOffTemp(psection))
        {
            b_ref_param_fixed = true;
        }

        psection = GetParameterSection(PARAMETER_SECTION_FRZ);
        if(NULL != psection && Fix_FrzOnOffTemp(psection))
        {
            b_frz_param_fixed = true;
        }
        b_param_synced = true;
    }

    if(b_ref_param_fixed)
    {
        ref_snrerror_tab = ary_RefSnrErrorOnOffTimeFix;
        ref_onoff_tab = ary_RefOnOffTempFix;
    }

    if(b_frz_param_fixed)
    {
        frz_snrerror_tab = ary_FrzSnrErrorOnOffTimeFix;
        frz_onoff_tab = ary_FrzOnOffTempFix;
        tubro_frz_tab = ary_TurboFrzOnOffTempFix;
    }

    GetSysParam(SYSPARAM_ICEMAKER_FUNC, &icemaker_mode);
    ref_temp_index = Get_RefOnOffSetTempIndex(room_range);
    if(u8_FrzSetTemp >= U8_FRZ_ON_OFFLEVEL_MIN)
    {
        frz_temp_index = u8_FrzSetTemp - U8_FRZ_ON_OFFLEVEL_MIN;
    }

    st_Zone.b_RefSnrError = Get_SensorError((SensorType_t)SENSOR_REF);
    st_Zone.b_FrzSnrError = Get_SensorError((SensorType_t)SENSOR_FRZ);

    st_Zone.b_RefVarSnrError = false;
    if((true == Get_SensorError(SENSOR_ICEMAKER_TOP)) ||
       (true == Get_SensorError(SENSOR_ICEMAKER_BOTTOM)) ||
       (true == Get_SensorError(SENSOR_ICEMAKER_BOTTOMX)) ||
       (true == Get_IceMakerCommErr()))
    {
        st_Zone.b_RefVarSnrError = true;
    }

    if(true == st_Zone.b_RefVarSnrError)
    {
        st_Zone.u16_VarSnrErrTime++;
        st_Zone.u16_VarOnTime = ary_VarSnrErrorOnOffTime[room_range].u16_OnTime;
        st_Zone.u16_VarOffTime = ary_VarSnrErrorOnOffTime[room_range].u16_OffTime;

        if(st_Zone.u16_VarSnrErrTime <= st_Zone.u16_VarOffTime)
        {
            st_Zone.u16_RefVarSnrTemp = U16_ZONE_TEMP_MIN;
        }
        else if(st_Zone.u16_VarSnrErrTime <= (st_Zone.u16_VarOffTime + st_Zone.u16_VarOnTime))
        {
            st_Zone.u16_RefVarSnrTemp = U16_ZONE_TEMP_MAX;
        }
        else
        {
            st_Zone.u16_VarSnrErrTime = 0;
        }
    }
    else
    {
        st_Zone.u16_VarSnrErrTime = 0;
        st_Zone.u16_RefVarSnrTemp = Get_SensorValue((SensorType_t)SENSOR_ICEMAKER_TOP);
    }

    if(true == st_Zone.b_RefSnrError)
    {
        st_Zone.u16_RefSnrErrTime++;
        st_Zone.u16_RefOnTime = ref_snrerror_tab[room_range].u16_OnTime;
        st_Zone.u16_RefOffTime = ref_snrerror_tab[room_range].u16_OffTime;

        if(st_Zone.u16_RefSnrErrTime <= st_Zone.u16_RefOffTime)
        {
            st_Zone.u16_RefSnrTemp = U16_ZONE_TEMP_MIN;
        }
        else if(st_Zone.u16_RefSnrErrTime <= (st_Zone.u16_RefOffTime + st_Zone.u16_RefOnTime))
        {
            st_Zone.u16_RefSnrTemp = U16_ZONE_TEMP_MAX;
        }
        else
        {
            st_Zone.u16_RefSnrErrTime = 0;
        }
    }
    else
    {
        st_Zone.u16_RefSnrErrTime = 0;
        st_Zone.u16_RefSnrTemp = Get_SensorValue((SensorType_t)SENSOR_REF);
    }

    if(true == st_Zone.b_FrzSnrError)
    {
        st_Zone.u16_FrzSnrErrTime++;
        st_Zone.u16_FrzOnTime = frz_snrerror_tab[room_range].u16_OnTime;
        st_Zone.u16_FrzOffTime = frz_snrerror_tab[room_range].u16_OffTime;

        if(st_Zone.u16_FrzSnrErrTime <= st_Zone.u16_FrzOffTime)
        {
            st_Zone.u16_FrzSnrTemp = U16_ZONE_TEMP_MIN;
        }
        else if(st_Zone.u16_FrzSnrErrTime <= (st_Zone.u16_FrzOffTime + st_Zone.u16_FrzOnTime))
        {
            st_Zone.u16_FrzSnrTemp = U16_ZONE_TEMP_MAX;
        }
        else
        {
            st_Zone.u16_FrzSnrErrTime = 0;
        }
    }
    else
    {
        st_Zone.u16_FrzSnrErrTime = 0;
        st_Zone.u16_FrzSnrTemp = Get_SensorValue((SensorType_t)SENSOR_FRZ);
    }

    st_Zone.u16_RefOnTemp = ref_onoff_tab[room_range][ref_temp_index].u16_OnTemp;
    st_Zone.u16_RefOffTemp = ref_onoff_tab[room_range][ref_temp_index].u16_OffTemp;
    if((true == b_energy_mode) && (RT_BELOW35 == room_range))
    {
        st_Zone.u16_RefOnTemp -= U16_ENERGY_MODE_REF_SNR_ON_TEMP_OFFSET;
        st_Zone.u16_RefOffTemp -= U16_ENERGY_MODE_REF_SNR_OFF_TEMP_OFFSET;
    }

    if(Get_Deforst_First_On_Flag())
    {
        st_Zone.u16_RefOnTemp += U16_FIRST_DEFORST_REF_SNR_ON_TEMP_OFFSET;
        st_Zone.u16_RefOffTemp -= U16_FIRST_DEFORST_REF_SNR_OFF_TEMP_OFFSET;      
    }

    if(Is_SmartGrid_DelayLoadOn() > 0)
    {
        st_Zone.u16_RefOnTemp += SMARTGRID_DELAYLOAD_ONOFF_OFFSET;
        st_Zone.u16_RefOffTemp  += SMARTGRID_DELAYLOAD_ONOFF_OFFSET; 
    }
    
    if(st_Zone.s8_RefOnInchValue != 0)
    {
        st_Zone.u16_RefOnTemp = (uint16_t)((int16_t)st_Zone.u16_RefOnTemp + (int16_t)st_Zone.s8_RefOnInchValue);
    }
    else if(IsParameterManagerReady())
    {
        adjust = 0;
        GetMaintenanceParamVal(MAINTENANCE_PARAMETER_REFON_ADJUST, &adjust);
        st_Zone.u16_RefOnTemp = (uint16_t)((int16_t)st_Zone.u16_RefOnTemp + (int16_t)adjust);
    }

    if(st_Zone.s8_RefOffInchValue != 0)
    {
        st_Zone.u16_RefOffTemp = (uint16_t)((int16_t)st_Zone.u16_RefOffTemp + (int16_t)st_Zone.s8_RefOffInchValue);
    }
    else if(IsParameterManagerReady())
    {
        adjust = 0;
        GetMaintenanceParamVal(MAINTENANCE_PARAMETER_REFOFF_ADJUST, &adjust);
        st_Zone.u16_RefOffTemp = (uint16_t)((int16_t)st_Zone.u16_RefOffTemp + (int16_t)adjust);
    }

    st_Zone.u16_RefOnPlusTemp = st_Zone.u16_RefOnTemp + U16_REF_ON_PLUS_TEMP_OFFSET;

    if(u8_RefVarSetTemp == eRefVar_Ref)
    {
        st_Zone.u16_RefVarOnTemp = CON_4P0_DEGREE;
        st_Zone.u16_RefVarOffTemp = CON_3P0_DEGREE;
    }
    else
    {
        st_Zone.u16_RefVarOnTemp = Get_Var_On();
        st_Zone.u16_RefVarOffTemp = Get_Var_Off();
    }

    if(st_Zone.s8_RefVarOnInchValue != 0)
    {
        st_Zone.u16_RefVarOnTemp = (uint16_t)((int16_t)st_Zone.u16_RefVarOnTemp + (int16_t)st_Zone.s8_RefVarOnInchValue);
    }
    else if(IsParameterManagerReady())
    {
        adjust = 0;
        GetMaintenanceParamVal(MAINTENANCE_PARAMETER_VARON_ADJUST, &adjust);
        st_Zone.u16_RefVarOnTemp = (uint16_t)((int16_t)st_Zone.u16_RefVarOnTemp + (int16_t)adjust);
    }

    if(st_Zone.s8_RefVarOffInchValue != 0)
    {
        st_Zone.u16_RefVarOffTemp = (uint16_t)((int16_t)st_Zone.u16_RefVarOffTemp + (int16_t)st_Zone.s8_RefVarOffInchValue);
    }
    else if(IsParameterManagerReady())
    {
        adjust = 0;
        GetMaintenanceParamVal(MAINTENANCE_PARAMETER_VAROFF_ADJUST, &adjust);
        st_Zone.u16_RefVarOffTemp = (uint16_t)((int16_t)st_Zone.u16_RefVarOffTemp + (int16_t)adjust);
    }

    if(eIceMaker_Quick_Mode == icemaker_mode)
    {
        st_Zone.u16_FrzOnTemp = tubro_frz_tab[room_range].u16_OnTemp - U16_ICEMAKER_QUICK_MODE_ONOFF_OFFSET;
        st_Zone.u16_FrzOffTemp = tubro_frz_tab[room_range].u16_OffTemp - U16_ICEMAKER_QUICK_MODE_ONOFF_OFFSET;  
    }
    else if(IsFrzDeepFreeze() && b_condensation_mode == false)
    {
        st_Zone.u16_FrzOnTemp = tubro_frz_tab[room_range].u16_OnTemp;
        st_Zone.u16_FrzOffTemp = tubro_frz_tab[room_range].u16_OffTemp;
    }
    else
    {
        st_Zone.u16_FrzOnTemp = frz_onoff_tab[room_range][frz_temp_index].u16_OnTemp;
        st_Zone.u16_FrzOffTemp = frz_onoff_tab[room_range][frz_temp_index].u16_OffTemp;
    }

    if((true == b_energy_mode) && (RT_BELOW35 == room_range))
    {
        st_Zone.u16_FrzOnTemp -= U16_ENERGY_MODE_FRZ_SNR_ON_TEMP_OFFSET;
    }

    if(Is_SmartGrid_DelayLoadOn() > 0)
    {
        st_Zone.u16_FrzOnTemp += SMARTGRID_DELAYLOAD_ONOFF_OFFSET;
        st_Zone.u16_FrzOffTemp  += SMARTGRID_DELAYLOAD_ONOFF_OFFSET; 
    }

    if(st_Zone.s8_FrzOnInchValue != 0)
    {
        st_Zone.u16_FrzOnTemp = (uint16_t)((int16_t)st_Zone.u16_FrzOnTemp + (int16_t)st_Zone.s8_FrzOnInchValue);
    }
    else if(IsParameterManagerReady())
    {
        adjust = 0;
        GetMaintenanceParamVal(MAINTENANCE_PARAMETER_FRZON_ADJUST, &adjust);
        st_Zone.u16_FrzOnTemp = (uint16_t)((int16_t)st_Zone.u16_FrzOnTemp + (int16_t)adjust);
    }

    if(st_Zone.s8_FrzOffInchValue != 0)
    {
        st_Zone.u16_FrzOffTemp = (uint16_t)((int16_t)st_Zone.u16_FrzOffTemp + (int16_t)st_Zone.s8_FrzOffInchValue);
    }
    else if(IsParameterManagerReady())
    {
        adjust = 0;
        GetMaintenanceParamVal(MAINTENANCE_PARAMETER_FRZOFF_ADJUST, &adjust);
        st_Zone.u16_FrzOffTemp = (uint16_t)((int16_t)st_Zone.u16_FrzOffTemp + (int16_t)adjust);
    }

    if(true == b_condensation_mode)
    {
        st_Zone.u16_RefOnTemp += U16_CONDENSATION_MODE_REF_SNR_ON_TEMP_OFFSET;
        st_Zone.u16_RefOffTemp += U16_CONDENSATION_MODE_REF_SNR_OFF_TEMP_OFFSET;
        st_Zone.u16_RefOnPlusTemp += U16_CONDENSATION_MODE_REF_SNR_ON_TEMP_OFFSET;

        st_Zone.u16_FrzOnTemp += U16_CONDENSATION_MODE_FRZ_SNR_ON_TEMP_OFFSET;
        st_Zone.u16_FrzOffTemp += U16_CONDENSATION_MODE_FRZ_SNR_OFF_TEMP_OFFSET;
    }

    if(true == b_strong_cool)
    {
        st_Zone.u16_RefOffTemp -= STRONG_COOL_REF_SNR_OFF_TEMP_OFFSET;
    }

    if((true == st_Zone.b_RefSnrError) ||
        (st_Zone.u16_RefSnrTemp <= st_Zone.u16_RefOffTemp)
        || comp_state != eCooling_CompOn
        || b_ref_disable == true
        || b_power_on_delay == true)
    {
        st_Zone.u16_RefOverLoadTimeMinute = Get_MinuteCount();
    }

    JudgeFanAdvance();
}

bool CoolingCycle_GetRefVarCoolingState(void)
{
    bool b_door_refvar_cool = false;
    bool b_power_on_delay = Get_PowerOnDelayVarCoolingState();

    if(true == b_power_on_delay)
    {
        st_Zone.b_RefVarCoolingState = false;
        ClearLinYunPreserveCool();
    }
    else
    {
        if(st_Zone.u16_RefVarSnrTemp >= st_Zone.u16_RefVarOnTemp)
        {
            st_Zone.b_RefVarCoolingState = true;
            ClearLinYunPreserveCool();
        }

        if(st_Zone.b_RefVarCoolingState == false &&
           GetLinYunPreserveCool() > 0)
        {
            b_door_refvar_cool = true;
        }

        if(st_Zone.u16_RefVarSnrTemp <= st_Zone.u16_RefVarOffTemp)
        {
            st_Zone.b_RefVarCoolingState = false;
            ClearLinYunPreserveCool();
        }
    }

    return (st_Zone.b_RefVarCoolingState || b_door_refvar_cool);
}

ZoneCoolingState_t CompOff_GetZoneCoolingState(void)
{
    bool is_sensor_error;
    st_Zone.b_ComOnFrzReachedOffTempOnce = false;
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    uint16_t room_temp = Get_SensorValue(SENSOR_ROOM);
    bool b_power_on_delay = Get_PowerOnDelayRefCoolingState();
    bool b_ref_disable = Get_RefDisable();
    bool is_ref_forst = Get_RefFrostReduceMode();
    uint8_t linyun_power = Get_LinYunPowerSave();
    linyun_power_param_st *lyparam = Get_LinYunPowerParam();

    if(Is_SmartGrid_ReduceLoadOn() && 
       Is_SmartGrid_ReduceLoadCool() == 0)
    {
        st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
        return st_Zone.zoneCoolingState;
    }

    is_sensor_error = ((Is_RefFrostReduceExpired() || Get_SensorError(SENSOR_REF)) && Get_SensorError(SENSOR_REF_DEFROST));
    if(linyun_power != 0)
    {
        if(lyparam->freq > 0)
        {
            if(lyparam->valve == Valve_FrzON_RefOFF)
            {
                st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Frz;             
            }
            else if(lyparam->valve == Valve_FrzOFF_RefON) 
            {
                st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Ref;  
            }
            else
            {
                st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;  
            }
        }
        else
        {
            st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;  
        }
        return st_Zone.zoneCoolingState;
    }

    if(st_Zone.u16_FrzSnrTemp >= st_Zone.u16_FrzOnTemp)
    {
        if(true == b_power_on_delay || b_ref_disable == true)
        {
            st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Frz;
            st_Zone.b_RefCooling = false;
        }
        else if(st_Zone.u16_RefSnrTemp >= st_Zone.u16_RefOffTemp && (is_sensor_error || !is_ref_forst))
        {
            st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_RefFrz;
            st_Zone.b_RefCooling = true;
        }
        else
        {
            st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Frz;
            st_Zone.b_RefCooling = false;
        }
    }
    else
    {
        if(true == b_energy_mode || true == b_power_on_delay
           || b_ref_disable == true)
        {
            st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
            st_Zone.b_RefCooling = false;
        }
        else if(st_Zone.u16_RefSnrTemp >= st_Zone.u16_RefOnPlusTemp && (is_sensor_error || !is_ref_forst))
        {
            st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Ref;
            st_Zone.b_RefCooling = true;
        }
        else if(st_Zone.u16_RefSnrTemp <= st_Zone.u16_RefOffTemp)
        {
            st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
            st_Zone.b_RefCooling = false;
        }
        else
        {
            ; // Stay
        }
    }

    return (st_Zone.zoneCoolingState);
}

ZoneCoolingState_t CompOn_GetZoneCoolingState(void)
{
    bool is_sensor_error;
    bool deforts_cool = false;
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    bool b_power_on_delay = Get_PowerOnDelayRefCoolingState();
    bool b_ref_disable = Get_RefDisable();
    bool b_valve_stay = Get_ValveStayRefForstState();
    bool is_ref_forst = Get_RefFrostReduceMode();
    uint8_t linyun_power = Get_LinYunPowerSave();
    linyun_power_param_st *lyparam = Get_LinYunPowerParam();
    uint16_t compstillon = Get_CompStillOnTimeMinute();

    is_sensor_error = ((Is_RefFrostReduceExpired() || Get_SensorError(SENSOR_REF)) && Get_SensorError(SENSOR_REF_DEFROST));
    if(compstillon > SMARTGRID_REDUCELOAD_COMPON_MINUTES && Is_SmartGrid_ReduceLoadOn())
    {
        Disable_SmartGrid_ReduceLoadCool();
        st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
        return st_Zone.zoneCoolingState;
    }
 
    if(linyun_power != 0)
    {
        if(compstillon > LINYUN_POWER_EXIT_COMPON_MINUTES)
        {
            Disable_LinYunPowerSave();
            st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle; 
        }
        else if(lyparam->freq > 0)
        {
            if(lyparam->valve == Valve_FrzON_RefOFF)
            {
                st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Frz;             
            }
            else if(lyparam->valve == Valve_FrzOFF_RefON)
            {
                st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Ref;  
            }
            else
            {
                st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;  
            }
        }
        else
        {
            st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;  
        }
        return st_Zone.zoneCoolingState;
    }

    if(Get_CompOnFrzCoolSeconds() > 0 && 
       Get_CompOnFrzCoolSeconds() < U16_FIRST_DEFORST_FRZ_COOL_SECONDS &&
       Get_Deforst_First_On_Flag())
    {
        if(st_Zone.u16_FrzSnrTemp > st_Zone.u16_FrzOffTemp)
        {
            deforts_cool = true;
        }
    }

    if(true == b_power_on_delay || b_ref_disable == true || b_valve_stay == true || deforts_cool == true)
    {
        st_Zone.b_RefCooling = false;
    }
    else if(b_energy_mode &&
        st_Zone.u16_RefSnrTemp > (st_Zone.u16_RefOnTemp + U16_ENERGY_MODE_REF_ON_PLUS_TEMP_OFFSET))
    {
        st_Zone.b_RefCooling = true;
    }
    else if(b_energy_mode == false && st_Zone.u16_RefSnrTemp > st_Zone.u16_RefOnTemp)
    {
        st_Zone.b_RefCooling = true;
    }

    if(st_Zone.u16_RefSnrTemp <= st_Zone.u16_RefOffTemp)
    {
        st_Zone.b_RefCooling = false;
    }

    if(st_Zone.b_RefCooling && (is_sensor_error || !is_ref_forst))
    {
        st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Ref;
    }
    else if(st_Zone.u16_FrzSnrTemp > st_Zone.u16_FrzOffTemp)
    {
        st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Frz;
    }
    else
    {
        st_Zone.zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
    }

    return (st_Zone.zoneCoolingState);
}

bool CompOn_PullDownState(void)
{
   if(st_Zone.u16_RefSnrTemp <= st_Zone.u16_RefOffTemp ||
      st_Zone.u16_FrzSnrTemp <= st_Zone.u16_FrzOffTemp)
    {
        return true;
    } 
    return false;
}

ZoneCoolingState_t PreCooling_GetFirstCoolingState(void)
{
    ZoneOnOffTemp_st refonoff;
    uint16_t pre_cooling_frz_off_temp = 0;
    bool b_ref_disable = Get_RefDisable();
    RoomTempRange_t room_range = Get_RoomTempRange();

    refonoff.u16_OnTemp =  ary_RefOnOffTemp[room_range][0].u16_OnTemp;
    refonoff.u16_OffTemp =  ary_RefOnOffTemp[room_range][0].u16_OffTemp;

    pre_cooling_frz_off_temp = st_Zone.u16_FrzOffTemp - PRE_COOLING_FRZ_SNR_OFF_TEMP_OFFSET;

    if(b_ref_disable == true)
    {
        st_Zone.b_RefCooling = false;
    }
    else if(st_Zone.u16_RefSnrTemp >= refonoff.u16_OnTemp)
    {
        st_Zone.b_RefCooling = true;
    }

    if(st_Zone.u16_RefSnrTemp < refonoff.u16_OffTemp)
    {
        st_Zone.b_RefCooling = false;
    }

    if(st_Zone.b_RefCooling)
    {
        st_Zone.zonePreCoolingState = (ZoneCoolingState_t)eZoneCooling_Ref;
    }
    else if(st_Zone.u16_FrzSnrTemp < pre_cooling_frz_off_temp)
    {
        st_Zone.zonePreCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
    }
    else
    {
        st_Zone.zonePreCoolingState = (ZoneCoolingState_t)eZoneCooling_Frz;
    }

    return (st_Zone.zonePreCoolingState);
}

ZoneCoolingState_t PreCooling_GetSecondCoolingState(void)
{
    uint16_t pre_cooling_frz_off_temp = 0;

    pre_cooling_frz_off_temp = st_Zone.u16_FrzOffTemp - PRE_COOLING_FRZ_SNR_OFF_TEMP_OFFSET;

    if(st_Zone.u16_FrzSnrTemp >= pre_cooling_frz_off_temp)
    {
        st_Zone.zonePreCoolingState = (ZoneCoolingState_t)eZoneCooling_Frz;
    }
    else
    {
        st_Zone.zonePreCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
    }
    return (st_Zone.zonePreCoolingState);
}

bool Get_TempPullDownState(void)
{
    uint16_t u16_RefVarSnrTemp = Get_SensorValue((SensorType_t)SENSOR_ICEMAKER_TOP);
    bool b_RefVarSnrError = Get_SensorError((SensorType_t)SENSOR_ICEMAKER_TOP) || Get_IceMakerCommErr();
    bool pull_down_state = false;

    if((st_Zone.u16_RefSnrTemp > U16_PULL_DOWN_REF_TEMP) && (false == st_Zone.b_RefSnrError) &&
       ((u16_RefVarSnrTemp > U16_PULL_DOWN_REFVAR_TEMP) && (false == b_RefVarSnrError)) &&
        (st_Zone.u16_FrzSnrTemp > U16_PULL_DOWN_FRZ_TEMP) && (false == st_Zone.b_FrzSnrError))
    {
        pull_down_state = true;
    }
    return (pull_down_state);
}

bool Get_FrzFanOnState(void)
{
    if(st_Zone.u16_FrzSnrTemp >= U16_FRZ_ON_TEMP)
    {
        return true;
    }
    return false;
}

bool Get_FrzFanOffState(void)
{
    if(st_Zone.u16_FrzSnrTemp < U16_FRZ_OFF_TEMP)
    {
        return true;
    }
    return false;
}

bool Get_RefTempHighLoadState(void)
{
    bool ref_high_load_flag = false;
    uint8_t linyun_power = Get_LinYunPowerSave();
    uint16_t offset = HIGH_LOAD_REF_SNR_ON_PLUS_TEMP_OFFSET;

    if(linyun_power > 0)
    {
        offset = LINYUN_POWER_HIGH_LOAD_REF_SNR_ON_PLUS_TEMP_OFFSET;
    }

    if((false == st_Zone.b_RefSnrError) &&
        (st_Zone.u16_RefSnrTemp >= (st_Zone.u16_RefOnTemp + offset)))
    {
        ref_high_load_flag = true;
    }

    return (ref_high_load_flag);
}

bool Get_FrzTempLessZero(void)
{
    bool frz_less_zero = false;

    if((false == st_Zone.b_FrzSnrError) &&
        (st_Zone.u16_FrzSnrTemp < U16_HIGH_LOAD_FRZ_TEMP))
    {
        frz_less_zero = true;
    }

    return (frz_less_zero);
}

bool Get_VarTempHighLoadState(void)
{
    bool var_high_load_flag = false;
    uint8_t linyun_power = Get_LinYunPowerSave();
    uint16_t offset = HIGH_LOAD_VAR_SNR_ON_PLUS_TEMP_OFFSET;

    if(linyun_power > 0)
    {
        offset = LINYUN_POWER_HIGH_LOAD_VAR_SNR_ON_PLUS_TEMP_OFFSET;
    }

    if((false == st_Zone.b_RefVarSnrError) &&
        (st_Zone.u16_RefVarSnrTemp >= (st_Zone.u16_RefVarOnTemp + offset)))
    {
        var_high_load_flag = true;
    }

    return (var_high_load_flag);
}

bool Get_FrzTempHighLoadState(void)
{
    bool frz_high_load_flag = false;
    uint8_t linyun_power = Get_LinYunPowerSave();
    uint16_t offset = HIGH_LOAD_FRZ_SNR_ON_PLUS_TEMP_OFFSET;

    if(linyun_power > 0)
    {
        offset = LINYUN_POWER_HIGH_LOAD_FRZ_SNR_ON_PLUS_TEMP_OFFSET;
    }
    
    if((false == st_Zone.b_FrzSnrError) &&
        (st_Zone.u16_FrzSnrTemp >= (st_Zone.u16_FrzOnTemp + offset)))
    {
        frz_high_load_flag = true;
    }

    return (frz_high_load_flag);
}

bool Get_RefTempOverOnDefrost(void)
{
    bool b_state = false;
    bool b_power_on_delay = Get_PowerOnDelayRefCoolingState();

    if(Get_RefDisable() == true || b_power_on_delay == true)
    {
        return false;
    }

    if(Get_MinuteElapsedTime(st_Zone.u16_RefOverLoadTimeMinute) >= U16_OVERLOAD_DEFROST_REF_ON_TEMP_TIME_MIN)
    {
        b_state = true;
    }
    return (b_state);
}

bool Get_RefTempOverLoadDefrost(void)
{
    bool b_state = false;
    bool b_power_on_delay = Get_PowerOnDelayRefCoolingState();

    if(Get_RefDisable() == true || b_power_on_delay == true)
    {
        return false;
    }

    if((false == st_Zone.b_RefSnrError) &&
        (st_Zone.u16_RefSnrTemp >= st_Zone.u16_RefOnTemp + U16_OVERLOAD_DEFROST_REF_ON_TEMP_OFFSET) &&
        st_Zone.u16_RefSnrTemp > U16_OVERLOAD_DEFROST_REF_TEMP)
    {
        b_state = true;
    }
    return (b_state);
}

bool Get_FrzTempOverLoadDefrost(void)
{
    bool b_state = false;

    if((false == st_Zone.b_FrzSnrError) &&
        (st_Zone.u16_FrzSnrTemp > U16_OVERLOAD_DEFROST_FRZ_TEMP))
    {
        b_state = true;
    }
    return (b_state);
}

ZoneCoolingState_t Get_ZoneCoolingState(void)
{
    return (st_Zone.zoneCoolingState);
}

void Reset_ZoneCoolingState(void)
{
    st_Zone.zoneCoolingState = eZoneCooling_Idle;
}

uint8_t Get_RefSetTemp(void)
{
    return (u8_RefSetTemp);
}

uint8_t Get_RefVarSetTemp(void)
{
    return (u8_RefVarSetTemp);
}

uint8_t Get_FrzSetTemp(void)
{
    uint8_t icemaker_mode = eIceMaker_Stop_Mode;
    GetSysParam(SYSPARAM_ICEMAKER_FUNC, &icemaker_mode);
    if(eIceMaker_Quick_Mode == icemaker_mode)
    {
        return U8_FRZ_LEVEL_MIN;
    }
    return (u8_FrzSetTemp);
}

uint16_t Get_RefZoneOnTemp(void)
{
    return (st_Zone.u16_RefOnTemp);
}

uint16_t Get_RefZoneOffTemp(void)
{
    return (st_Zone.u16_RefOffTemp);
}

uint16_t Get_RefOnTempMicroAdjustParm(void)
{
    return (uint16_t)(st_Zone.s8_RefOnInchValue);
}

uint16_t Get_RefOffTempMicroAdjustParm(void)
{
    return (uint16_t)(st_Zone.s8_RefOffInchValue);
}

void Set_RefOnTempMicroAdjustParm(uint16_t u16_parm)
{
    st_Zone.s8_RefOnInchValue = (int8_t)u16_parm;
}

void Set_RefOffTempMicroAdjustParm(uint16_t u16_parm)
{
    st_Zone.s8_RefOffInchValue = (int8_t)u16_parm;
}

uint16_t Get_RefVarZoneOnTemp(void)
{
    return (st_Zone.u16_RefVarOnTemp);
}

uint16_t Get_RefVarZoneOffTemp(void)
{
    return (st_Zone.u16_RefVarOffTemp);
}

uint16_t Get_RefVarOnTempMicroAdjustParm(void)
{
    return (uint16_t)(st_Zone.s8_RefVarOnInchValue);
}

uint16_t Get_RefVarOffTempMicroAdjustParm(void)
{
    return (uint16_t)(st_Zone.s8_RefVarOffInchValue);
}

void Set_RefVarOnTempMicroAdjustParm(uint16_t u16_parm)
{
    st_Zone.s8_RefVarOnInchValue = (int8_t)u16_parm;
}

void Set_RefVarOffTempMicroAdjustParm(uint16_t u16_parm)
{
    st_Zone.s8_RefVarOffInchValue = (int8_t)u16_parm;
}

uint16_t Get_DefPreCoolingRefZoneOffTemp(uint8_t refSetTemp)
{
    uint16_t u16_off_temp;
    RoomTempRange_t room_range = Get_RoomTempRange();
    uint8_t ref_temp_index = 0;

    if(refSetTemp >= U8_REF_LEVEL_MIN)
    {
        ref_temp_index = refSetTemp - U8_REF_LEVEL_MIN;
    }

    u16_off_temp = ary_RefOnOffTemp[room_range][ref_temp_index].u16_OffTemp;
    return (u16_off_temp);
}

uint16_t Get_FrzZoneOnTemp(void)
{
    return (st_Zone.u16_FrzOnTemp);
}

uint16_t Get_FrzZoneOffTemp(void)
{
    return (st_Zone.u16_FrzOffTemp);
}

uint16_t Get_FrzOnTempMicroAdjustParm(void)
{
    return (uint16_t)(st_Zone.s8_FrzOnInchValue);
}

uint16_t Get_FrzOffTempMicroAdjustParm(void)
{
    return (uint16_t)(st_Zone.s8_FrzOffInchValue);
}

void Set_FrzOnTempMicroAdjustParm(uint16_t u16_parm)
{
    st_Zone.s8_FrzOnInchValue = (int8_t)u16_parm;
}

void Set_FrzOffTempMicroAdjustParm(uint16_t u16_parm)
{
    st_Zone.s8_FrzOffInchValue = (int8_t)u16_parm;
}

bool IsRefFanNeedAdvance(void)
{
    return b_ref_fan_advance;
}

bool IsFrzFanNeedAdvance(void)
{
    return b_frz_fan_advance;    
}

bool IsCondFanNeedAdvance(void)
{
    return b_cond_fan_advance;    
}

bool IsVarFanNeedAdvance(void)
{
    return b_var_fan_advance;    
}

bool IsLinYunTempBelowOn(void)
{
    int16_t val;
    uint16_t frz_off_temp;
    bool b_condensation = Get_CondensationModeState();

    val = Get_FrzSetTemp() - 30;
    if(b_condensation && val < -16)
    {
        val += 1;
    }
    frz_off_temp = val * 10 + CON_0P0_DEGREE;
    if((false == st_Zone.b_FrzSnrError) &&
        (st_Zone.u16_FrzSnrTemp <= frz_off_temp))
    {
        return true;
    }
    return false;
}


