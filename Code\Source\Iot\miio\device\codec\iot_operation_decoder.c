#include "iot_operation_decoder.h"
#include "miio_define.h"
#include "miio_api.h"

int iot_operation_value_decode(const char *pbuf, IotGeneralPropVal_t* pValue)
{
	static char arg_value[PROP_STRING_MAX_LENGTH + 2 + 1];

    do
    {
        int arg_size = miio_strlen(pbuf);
		
        memset(arg_value, 0, PROP_STRING_MAX_LENGTH + 2 + 1);
        memset(pValue, 0, sizeof(IotGeneralPropVal_t));
        strncpy(arg_value, pbuf, MIN(arg_size, PROP_STRING_MAX_LENGTH));

        if( *arg_value == '\"'){
            //arg_value[arg_size-1] = '\0'; //remove \" on tail
            strncpy(pValue->sValue, arg_value, PROP_STRING_MAX_LENGTH);
			pValue->propType = IOT_TYPE_STRING;
        } else if( !strncmp(arg_value, "true", strlen("true"))){
            pValue->lValue = 1;
			pValue->propType = IOT_TYPE_BOOL;
        } else if( !strncmp(arg_value, "false", strlen("false"))){
            pValue->lValue = 0;
			pValue->propType = IOT_TYPE_BOOL;			
        } else {
            // judge if integer value
            if(NULL == strchr(arg_value, '.')) {
    	        pValue->lValue = miio_atoi(arg_value);
				pValue->propType = IOT_TYPE_LONG;				
            } else {
	            pValue->fValue = atof(arg_value);            
				pValue->propType = IOT_TYPE_FLOAT;								
            }
        }

    } while (0);


    return MIIO_OK;
}




