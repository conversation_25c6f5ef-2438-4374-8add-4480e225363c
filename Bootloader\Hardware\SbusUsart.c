/*!
 * @file
 * @brief Initialize MCU.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include <string.h>
#include "Adpt_Usart.h"
#include "SbusUsart.h"
#include "UartComm.h"
#include "FirewareComm.h"

static UartComm_st st_UartSbus;
static fireware_comm_st dispaly_fw;
static fireware_comm_st icemaker_fw;
static fireware_comm_st nfc_fw;

static void SbusEnableTx(bool enable)
{
    if(enable)
    {
        return LpUart0_EnalbeTxInterrupts();
    }
    return LpUart0_DisalbeTxInterrupts();
}

static void SbusEnableRx(bool enable)
{
    if(enable)
    {
        return LpUart0_EnalbeRxInterrupts();
    }
    return LpUart0_DisalbeRxInterrupts();
}

void Init_UartSbus(void)
{
    UartCommInit(&st_UartSbus, U8_SBUS_SEND_TIMEOUT_MILLISECOND,
                 U16_SBUS_RECV_TIMEOUT_MILLISECOND, U16_SBUS_RECE_DATA_ERROR_TIME_WITH_100MS);
    st_UartSbus.uart_ops.enableRx = SbusEnableRx;
    st_UartSbus.uart_ops.enableTx = SbusEnableTx;
    st_UartSbus.uart_ops.sendOneData = LpUart0_SendOneData;
    dispaly_fw.fwAddr = FIREWARE_DISPLAY_ADDR;
    dispaly_fw.ops.transmit = UartCommTransmit;
    dispaly_fw.ops.receive = UartCommReceive;
    dispaly_fw.enable = false;
    dispaly_fw.crcretry = U8_SBUS_DISPLAY_CRC_RETRY;
    dispaly_fw.private = &st_UartSbus;
    FirewareCommRegister(&dispaly_fw);
    icemaker_fw.fwAddr = FIREWARE_ICEMAKER_ADDR;
    icemaker_fw.ops.transmit = UartCommTransmit;
    icemaker_fw.ops.receive = UartCommReceive;
    icemaker_fw.enable = false;
    icemaker_fw.private = &st_UartSbus;
    FirewareCommRegister(&icemaker_fw);
    nfc_fw.fwAddr = FIREWARE_NFC_ADDR;
    nfc_fw.ops.transmit = UartCommTransmit;
    nfc_fw.ops.receive = UartCommReceive;
    nfc_fw.enable = false;
    nfc_fw.crcretry = U8_SBUS_DISPLAY_CRC_RETRY;
    nfc_fw.private = &st_UartSbus;
    FirewareCommRegister(&nfc_fw);
}

void Handle_UartSbusSendData(void)
{
    Handle_UartCommSendData(&st_UartSbus);
}

void Handle_UartSbusReceData(const uint8_t u8_rece_data)
{
    Handle_UartCommReceData(&st_UartSbus, u8_rece_data);
}

void Handle_UartSbusOverTime(void)
{
    Handle_UartCommOverTime(&st_UartSbus);
}

