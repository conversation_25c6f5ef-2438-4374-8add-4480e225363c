/*!
 * @file
 * @brief This module is cal crc16.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "Crc16_CCITT_FALSE.h"

static const uint16_t ary_CRC16_Table0[16] = {
    0x0000U, 0xC0C1U, 0xC181U, 0x0140U, 0xC301U, 0x03C0U, 0x0280U, 0xC241U, 0xC601U, 0x06C0U, 0x0780U, 0xC741U, 0x0500U, 0xC5C1U, 0xC481U, 0x0440U
};

static const uint16_t ary_CRC16_Table1[16] = {
    0x0000U, 0xCC01U, 0xD801U, 0x1400U, 0xF001U, 0x3C00U, 0x2800U, 0xE401U, 0xA001U, 0x6C00U, 0x7800U, 0xB401U, 0x5000U, 0x9C01U, 0x8801U, 0x4400U
};

uint16_t Cal_CRC_SingleData(uint16_t u16_crcvalue, uint8_t u8_data)
{
    uint8_t u8_databyte = 0;
    uint8_t u8_low_halfbyte_index = 0;
    uint8_t u8_high_halfbyte_index = 0;
    uint16_t u16_crcvalue_table0 = 0;
    uint16_t u16_crcvalue_table1 = 0;
    uint16_t u16_crcvalue_highbyte = 0;
    u8_databyte = (uint8_t)(u16_crcvalue & 0x00ff);
    u8_databyte ^= u8_data;
    u8_low_halfbyte_index = u8_databyte & 0x0f;
    u8_high_halfbyte_index = (u8_databyte >> 4) & 0x0f;
    u16_crcvalue_table0 = ary_CRC16_Table0[u8_low_halfbyte_index];
    u16_crcvalue_table1 = ary_CRC16_Table1[u8_high_halfbyte_index];
    u16_crcvalue_highbyte = (u16_crcvalue >> 8) & 0x00ff;
    u16_crcvalue =
        u16_crcvalue_table0 ^ u16_crcvalue_table1 ^ u16_crcvalue_highbyte;
    return (u16_crcvalue);
}

uint16_t Cal_CRC_MultipleData(uint8_t *p_databuff, uint32_t datalength)
{
    uint32_t index = 0;
    uint16_t u16_crcvalue = U16_CRC_INITIAL_VALUE;

    if(datalength > 0)
    {
        for(index = 0; index < datalength; index++)
        {
            u16_crcvalue = Cal_CRC_SingleData(u16_crcvalue, p_databuff[index]);
        }
    }

    return (u16_crcvalue);
}
