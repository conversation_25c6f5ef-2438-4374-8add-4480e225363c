/*!
 * @file
 * @brief This file defines public constants, types and functions for the clock adapter.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef _Adpt_FLASH_H_
#define _Adpt_FLASH_H_

#include "flash.h"

#define FLASH_CONFIG_FREQ_4MHZ 1
#define FLASH_CONFIG_FREQ_8MHZ 2
#define FLASH_CONFIG_FREQ_16MHZ 4
#define FLASH_CONFIG_FREQ_24MHZ 6
#define FLASH_CONFIG_FREQ_32MHZ 8
#define FLASH_CONFIG_FREQ_48MHZ 12

#define FLASH_SECTOR_SIZE 0x200ul //一个sector的尺寸
#define FLASH_BASE ((uint32_t)0x00000000) // flash基地址
#define FLASH_SIZE (256u * FLASH_SECTOR_SIZE) // flash尺寸

void Board_InitFlash(void);
uint16_t FlashSectorNumber(uint32_t u32Size);
void FlashReadBytes(uint32_t u32Addr, uint8_t *pu8Read<PERSON>uff, uint32_t u32ByteLength);
en_result_t FlashWriteBytes(uint32_t u32Addr, uint8_t pu8Data[], uint32_t u32Len);
en_result_t FlashWriteHalfWords(uint32_t u32Addr, uint16_t pu16Data[], uint32_t u32Len);
en_result_t FlashWriteWords(uint32_t u32Addr, uint32_t pu32Data[], uint32_t u32Len);
en_result_t FlashSectorErase(uint32_t u32SectorAddr);
#endif
