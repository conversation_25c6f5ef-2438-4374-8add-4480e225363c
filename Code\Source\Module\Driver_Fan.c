/*!
 * @file
 * @brief This module covers the complete fan functionality.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "Driver_Fan.h"
#include "Adpt_GPIO.h"
#include "Adpt_PWM.h"
#include "SystemTimerModule.h"

#define FAN_SETSPEEDMETHOD_SPEED 0 // 调速方式为转速
#define FAN_SETSPEEDMETHOD_PWM 1 // 调速方式为pwm
#define FAN_SETSPEEDMETHOD_VOLTAGE 2 // 电压调速(只适用于三线风机)
#define FOUR_WIRE_FAN 0 // 风机为四线风机
#define THREE_WIRE_FAN 1 // 风机为三线风机

#define FAN_PER_ROUND_PULSE_ID0 2
#define FAN_TYPE_ID0 THREE_WIRE_FAN
#define FAN_ADJUSTV_METHOD_ID0 FAN_SETSPEEDMETHOD_PWM
#define PWM_CYCLE_ID0 TIME3_PERIOD_VALUE

#define FAN_PER_ROUND_PULSE_ID1 2
#define FAN_TYPE_ID1 THREE_WIRE_FAN
#define FAN_ADJUSTV_METHOD_ID1 FAN_SETSPEEDMETHOD_PWM
#define PWM_CYCLE_ID1 TIME4_PERIOD_VALUE//#define PWM_CYCLE_ID1 TIME3_PERIOD_VALUE

#define FAN_PER_ROUND_PULSE_ID2 2
#define FAN_TYPE_ID2 FOUR_WIRE_FAN
#define FAN_ADJUSTV_METHOD_ID2 FAN_SETSPEEDMETHOD_PWM
#define PWM_CYCLE_ID2 TIME6_PERIOD_VALUE//#define PWM_CYCLE_ID2 TIME4_PERIOD_VALUE

#define FAN_MAX_SPEED_RPM 2000

static uint16_t u16_FanSecond = 0;
Fan_st ARY_Fan[FAN_MAXNUMBER];
static const uint16_t Ary_FanRpm[RPM_MAX] = {
    0, 700, 820, 930, 1030, 1140, 1220, 1330, 1420, 1510, 1600, 1660, 1750, 1830, 1900, 1960
};

static void Get_FanRealSpeed(Fan_st *p_st_fan);
static void Judge_FanErr(Fan_st *p_st_fan);
static void Set_DcFanSpeed(Fan_st *p_st_fan);
static void FanPwmPercentToPwmRegValue(Fan_st *p_st_fan);
static void Storage_FbPuelse(Fan_st *p_st_fan);
static void Set_FanPwmPercentRunNoDelay(Fan_st *p_st_fan, uint8_t u8_pwmPercent);
static void Driver_Fan(Fan_st *p_st_fan);
static bool Get_DcFanIsError(Fan_st *p_st_fan);
static bool Get_DcFanIsContinueError(Fan_st *p_st_fan);
static void Count_FanFbPlusEdge(Fan_st *p_st_fan);
static uint16_t Get_Fan_TimerU16Second(void);

static void Ctrl_FanError(Fan_st *p_st_fan);
static void Ctrl_FanRun(Fan_st *p_st_fan);
static void Ctrl_FanStop(Fan_st *p_st_fan);

static void Init_Fan(void);

static void Ctrl_FanPower_ID0(bool b_IsOn);
static void Set_FanPwmRegDutyValue_ID0(uint16_t u16_pwmRegValue);
uint8_t Read_FeedbackIO_ID0(void);

static void Ctrl_FanPower_ID0(bool b_IsOn)
{
}

static void Set_FanPwmRegDutyValue_ID0(uint16_t u16_pwmRegValue)
{
    uint16_t u16_pulse;

    if(u16_pwmRegValue > ARY_Fan[FAN_ID0].u16_PwmRegCycleValue)
    {
        u16_pwmRegValue = ARY_Fan[FAN_ID0].u16_PwmRegCycleValue;
    }

    u16_pulse = ARY_Fan[FAN_ID0].u16_PwmRegCycleValue - u16_pwmRegValue;
    SET_FAN_DUTY_ID0(u16_pulse);
}

uint8_t Read_FeedbackIO_ID0(void)
{
    uint8_t u8_level = LEVEL_LOW;

    if(IO_FRZ_FAN_FB_IN)
    {
        u8_level = LEVEL_HIGH;
    }

    return u8_level;
}

static void Ctrl_FanPower_ID1(bool b_IsOn)
{

}

static void Set_FanPwmRegDutyValue_ID1(uint16_t u16_pwmRegValue)
{
    uint16_t u16_pulse;

    if(u16_pwmRegValue > ARY_Fan[FAN_ID1].u16_PwmRegCycleValue)
    {
        u16_pwmRegValue = ARY_Fan[FAN_ID1].u16_PwmRegCycleValue;
    }

    u16_pulse = ARY_Fan[FAN_ID1].u16_PwmRegCycleValue - u16_pwmRegValue;
    SET_FAN_DUTY_ID1(u16_pulse);
}

uint8_t Read_FeedbackIO_ID1(void)
{
    uint8_t u8_level = LEVEL_LOW;

    if(IO_REF_FAN_FB_IN)
    {
        u8_level = LEVEL_HIGH;
    }

    return u8_level;
}

static void Ctrl_FanPower_ID2(bool b_IsOn)
{

}

static void Set_FanPwmRegDutyValue_ID2(uint16_t u16_pwmRegValue)
{
    uint16_t u16_pulse;

    if(u16_pwmRegValue > ARY_Fan[FAN_ID2].u16_PwmRegCycleValue)
    {
        u16_pwmRegValue = ARY_Fan[FAN_ID2].u16_PwmRegCycleValue;
    }

    u16_pulse = ARY_Fan[FAN_ID2].u16_PwmRegCycleValue - u16_pwmRegValue;
    SET_FAN_DUTY_ID2(u16_pulse);
}

uint8_t Read_FeedbackIO_ID2(void)
{
    uint8_t u8_level = LEVEL_LOW;

    if(IO_COND_FAN_FB_IN)
    {
        u8_level = LEVEL_HIGH;
    }

    return u8_level;
}

static void Init_Fan(void)
{
    ARY_Fan[FAN_ID0].u8_OneRoundPlus = FAN_PER_ROUND_PULSE_ID0;
    ARY_Fan[FAN_ID0].u16_PwmRegCycleValue = PWM_CYCLE_ID0;
    ARY_Fan[FAN_ID0].u16_PwmRegDutyDownLimitValue = PWM_CYCLE_ID0 / 10;
    ARY_Fan[FAN_ID0].u8_SetSpeedMethod = FAN_ADJUSTV_METHOD_ID0;
    ARY_Fan[FAN_ID0].u8_CurPwmPercent = U8_FAN_PWM_PERCENT_INIT_VALUE;
    ARY_Fan[FAN_ID0].u8_FanType = FAN_TYPE_ID0;
    ARY_Fan[FAN_ID0].Ctrl_Power = Ctrl_FanPower_ID0;
    ARY_Fan[FAN_ID0].Set_FanPwmRegValue = Set_FanPwmRegDutyValue_ID0;
    ARY_Fan[FAN_ID0].Read_FeedbackIO = Read_FeedbackIO_ID0;
    ARY_Fan[FAN_ID0].b_IsInit = true;

    ARY_Fan[FAN_ID1].u8_OneRoundPlus = FAN_PER_ROUND_PULSE_ID1;
    ARY_Fan[FAN_ID1].u16_PwmRegCycleValue = PWM_CYCLE_ID1;
    ARY_Fan[FAN_ID1].u16_PwmRegDutyDownLimitValue = PWM_CYCLE_ID1 / 10;
    ARY_Fan[FAN_ID1].u8_SetSpeedMethod = FAN_ADJUSTV_METHOD_ID1;
    ARY_Fan[FAN_ID1].u8_CurPwmPercent = U8_FAN_PWM_PERCENT_INIT_VALUE;
    ARY_Fan[FAN_ID1].u8_FanType = FAN_TYPE_ID1;
    ARY_Fan[FAN_ID1].Ctrl_Power = Ctrl_FanPower_ID1;
    ARY_Fan[FAN_ID1].Set_FanPwmRegValue = Set_FanPwmRegDutyValue_ID1;
    ARY_Fan[FAN_ID1].Read_FeedbackIO = Read_FeedbackIO_ID1;
    ARY_Fan[FAN_ID1].b_IsInit = true;

    ARY_Fan[FAN_ID2].u8_OneRoundPlus = FAN_PER_ROUND_PULSE_ID2;
    ARY_Fan[FAN_ID2].u16_PwmRegCycleValue = PWM_CYCLE_ID2;
    ARY_Fan[FAN_ID2].u16_PwmRegDutyDownLimitValue = PWM_CYCLE_ID2 / 10;
    ARY_Fan[FAN_ID2].u8_SetSpeedMethod = FAN_ADJUSTV_METHOD_ID2;
    ARY_Fan[FAN_ID2].u8_CurPwmPercent = U8_FAN_PWM_PERCENT_INIT_VALUE;
    ARY_Fan[FAN_ID2].u8_FanType = FAN_TYPE_ID2;
    ARY_Fan[FAN_ID2].Ctrl_Power = Ctrl_FanPower_ID2;
    ARY_Fan[FAN_ID2].Set_FanPwmRegValue = Set_FanPwmRegDutyValue_ID2;
    ARY_Fan[FAN_ID2].Read_FeedbackIO = Read_FeedbackIO_ID2;
    ARY_Fan[FAN_ID2].b_IsInit = true;
}

void Set_FanSpeed(Fan_em em_fan, uint16_t u16_speed)
{
    if(u16_speed >= FAN_MAX_SPEED_RPM)
    {
        u16_speed = FAN_MAX_SPEED_RPM;
    }

    if(u16_speed)
    {
        ARY_Fan[em_fan].b_IsOn = true;
    }
    else
    {
        ARY_Fan[em_fan].b_IsOn = false;
    }

    ARY_Fan[em_fan].st_FanSpeed.u16_SetSpeed = u16_speed;
}

void Set_FanVoltage(Fan_em em_fan, uint16_t u16_voltagePara)
{
    uint16_t u16_voltage = u16_voltagePara * 100;

    if(u16_voltage > 12000) // 12V
    {
        u16_voltage = 12000;
    }

    if(u16_voltage)
    {
        ARY_Fan[em_fan].b_IsOn = true;
    }
    else
    {
        ARY_Fan[em_fan].b_IsOn = false;
    }

    ARY_Fan[em_fan].st_FanSpeed.u16_SetVoltage = u16_voltage;
}

void Set_FrzFanDuty(uint8_t duty)
{
    Set_FanPwmPercent(FRZ_FAN, duty);
}

void Set_RefFanDuty(uint8_t duty)
{
    Set_FanPwmPercent(REF_FAN, duty);
}

void Set_CoolFanSpeed(uint8_t u8_speedIndex)
{
    if(u8_speedIndex >= RPM_MAX)
    {
        u8_speedIndex = RPM_100DUTY;
    }
    Set_FanSpeed(COOL_FAN, Ary_FanRpm[u8_speedIndex]);
}

void Set_CoolFanDuty(uint8_t duty)
{
    Set_FanPwmPercent(COOL_FAN, duty);
}

void Set_FanPwmPercent(Fan_em em_fan, uint8_t u8_pwmPercent)
{
    if(u8_pwmPercent)
    {
        ARY_Fan[em_fan].b_IsOn = true;
    }
    else
    {
        ARY_Fan[em_fan].b_IsOn = false;
    }

    if(u8_pwmPercent > 100)
    {
        u8_pwmPercent = 100;
    }

    ARY_Fan[em_fan].u8_AppSetPwmPercent = u8_pwmPercent;
}

void Force_FanState(uint8_t em_fanType, bool b_testState)
{
    if(false == ARY_Fan[em_fanType].b_IsInit)
    {
        Init_Fan();
    }
    if(b_testState == true)
    {
        // Set_FanPwmPercentRunNoDelay(&ARY_Fan[em_fanType], U8_FAN_DETECT_PWM_VALUE);
        ARY_Fan[em_fanType].Ctrl_Power(true);
        ARY_Fan[em_fanType].Set_FanPwmRegValue(ARY_Fan[em_fanType].u16_PwmRegCycleValue);
    }
    else
    {
        // Set_FanPwmPercentRunNoDelay(&ARY_Fan[em_fanType], 0);
        ARY_Fan[em_fanType].Ctrl_Power(false);
        ARY_Fan[em_fanType].Set_FanPwmRegValue(0);
    }
}

bool Is_FanError(Fan_em em_fanType)
{
    return Get_DcFanIsError(&ARY_Fan[em_fanType]);
}

bool Is_FanContinueError(Fan_em em_fanType)
{
    return Get_DcFanIsContinueError(&ARY_Fan[em_fanType]);
}

static uint16_t Get_Fan_TimerU16Second(void)
{
    return u16_FanSecond;
}

void Driver_AllFan(void)
{
    uint8_t u8_fanId = 0;

    for(u8_fanId = 0; u8_fanId < FAN_MAXNUMBER; u8_fanId++)
    {
        if(false == ARY_Fan[u8_fanId].b_IsInit)
        {
            Init_Fan();
        }
        else
        {
            if((uint16_t)(u16_FanSecond - ARY_Fan[u8_fanId].u16_GetFbTimerSec) > 1)
            {
                Storage_FbPuelse(&ARY_Fan[u8_fanId]);
                ARY_Fan[u8_fanId].u16_GetFbTimerSec = u16_FanSecond;
            }
            Driver_Fan(&ARY_Fan[u8_fanId]);
        }
    }
    u16_FanSecond++;
}

void Count_AllFanFbPulse(void)
{
    uint8_t u8_fanId = 0;

    for(u8_fanId = 0; u8_fanId < FAN_MAXNUMBER; u8_fanId++)
    {
        if(true == ARY_Fan[u8_fanId].b_IsInit)
        {
            Count_FanFbPlusEdge(&ARY_Fan[u8_fanId]);
        }
    }
}

static void FanPwmPercentToPwmRegValue(Fan_st *p_st_fan)
{
    uint16_t u16_pwm = 0;

    if(p_st_fan->u8_CurPwmPercent != p_st_fan->u8_SetPwmPercent)
    {
        p_st_fan->u8_CurPwmPercent = p_st_fan->u8_SetPwmPercent;
        u16_pwm = ((uint16_t)(p_st_fan->u8_CurPwmPercent) * p_st_fan->u16_PwmRegCycleValue) / 100;

        if(u16_pwm > p_st_fan->u16_PwmRegCycleValue)
        {
            u16_pwm = p_st_fan->u16_PwmRegCycleValue;
        }

        p_st_fan->u16_PwmRegDutyValue = u16_pwm;
    }
}

void Get_FanRealSpeed(Fan_st *p_st_fan)
{
    uint16_t u16_nowsec = Get_Fan_TimerU16Second();

    if((uint16_t)(u16_nowsec - p_st_fan->st_FanSpeed.u16_LVTimerSec) >= U16_COMPUTER_FANV_LONGINTERVAL)
    {
        if(true == p_st_fan->st_FanSpeed.b_IsLGetFbValueEd)
        {
            if(CON_PULSE_FOUR == p_st_fan->u8_OneRoundPlus)
            {
                p_st_fan->st_FanSpeed.u16_JudgeErrorSpeed = //((10scount)*60s/10s/4pulse/2Edge)
                    p_st_fan->st_FanSpeed.u16_LFbPulseNumberSumBak * U16_FANV_LPARAM / U16_FANV_FOURPULSE_PARAM;
            }
            else //( CON_PULSE_TWO == p_st_fan->u8_OneRoundPlus)
            {
                p_st_fan->st_FanSpeed.u16_JudgeErrorSpeed = //((10scount)*(60s/10s)/(2pulse/2Edge))
                    p_st_fan->st_FanSpeed.u16_LFbPulseNumberSumBak * U16_FANV_LPARAM / U16_FANV_TWOPULSE_PARAM;
            }

            p_st_fan->st_FanSpeed.b_IsLComputerEd = true;
            p_st_fan->st_FanSpeed.b_IsLGetFbValueEd = false;
            p_st_fan->st_FanSpeed.u16_LVTimerSec = u16_nowsec;
        }
    }

    if((uint16_t)(u16_nowsec - p_st_fan->st_FanSpeed.u16_SVTimerSec) >= U16_COMPUTER_FANV__SHORTINTERVAL)
    {
        if(CON_PULSE_FOUR == p_st_fan->u8_OneRoundPlus)
        {
            p_st_fan->st_FanSpeed.u16_CurSpeed = //((2scount)*(60s/10s)/(2pulse/2Edge))
                p_st_fan->st_FanSpeed.u16_SFbPulseNumberBak * U16_FANV_SPARAM / U16_FANV_FOURPULSE_PARAM;
        }
        else //( CON_PULSE_TWO == p_st_fan->u8_OneRoundPlus)
        {
            p_st_fan->st_FanSpeed.u16_CurSpeed = //((2scount)*(60s/10s)/(2pulse/2Edge))
                p_st_fan->st_FanSpeed.u16_SFbPulseNumberBak * U16_FANV_SPARAM / U16_FANV_TWOPULSE_PARAM;
        }

        p_st_fan->st_FanSpeed.u16_SVTimerSec = u16_nowsec;
    }
}

void Set_DcFanVoltage(Fan_st *p_st_fan)
{
    uint16_t u16_nowsec = Get_Fan_TimerU16Second();
    bool b_isRealVHigherSet;
    uint16_t u16_offsetVoltage = 0;
    uint16_t u16_realVoltage = 0;

    if(0 == p_st_fan->st_FanSpeed.u16_SetVoltage)
    {
        p_st_fan->st_FanSpeed.u8_Trend = FAN_TREND_STOP;
        p_st_fan->u16_PwmRegDutyValue = 0;
    }
    else
    {
        if((uint16_t)(u16_nowsec - p_st_fan->st_FanSpeed.u16_TimerSec) >= U16_FAN_ADJUST_V_INTERVAL)
        {
            p_st_fan->st_FanSpeed.u16_TimerSec = u16_nowsec;
            u16_realVoltage = ADToVoltage(p_st_fan->st_FanSpeed.Get_FanVoltage()); // ADToVoltage(st_ADSampleInfo.u16_CoolFanAd);

            if(u16_realVoltage > p_st_fan->st_FanSpeed.u16_SetVoltage)
            {
                b_isRealVHigherSet = true;
                u16_offsetVoltage = u16_realVoltage - p_st_fan->st_FanSpeed.u16_SetVoltage;
            }
            else
            {
                b_isRealVHigherSet = false;
                u16_offsetVoltage = p_st_fan->st_FanSpeed.u16_SetVoltage - u16_realVoltage;
            }

            if(u16_offsetVoltage > U16_FAN_START_ADJUST_VOLTAGE_LIMIT)
            {
                if(true == b_isRealVHigherSet)
                {
                    p_st_fan->st_FanSpeed.u8_Trend = FAN_TREND_DOWN;
                }
                else
                {
                    p_st_fan->st_FanSpeed.u8_Trend = FAN_TREND_UP;
                }
            }

            if(FAN_TREND_UP == p_st_fan->st_FanSpeed.u8_Trend)
            {
                if(true == b_isRealVHigherSet)
                {
                    if(++p_st_fan->st_FanSpeed.u8_ShockCount > U8_FAN_SHOCK_COUNT)
                    {
                        if(p_st_fan->st_FanSpeed.u16_PreOffsetVoltage < u16_offsetVoltage)
                        {
                            if(p_st_fan->u16_PwmRegDutyValue > p_st_fan->u16_PwmRegDutyDownLimitValue)
                            {
                                p_st_fan->u16_PwmRegDutyValue--;
                            }
                        }

                        p_st_fan->st_FanSpeed.u8_Trend = FAN_TREND_STOP;
                    }
                    else
                    {
                        if(p_st_fan->u16_PwmRegDutyValue > p_st_fan->u16_PwmRegDutyDownLimitValue)
                        {
                            p_st_fan->u16_PwmRegDutyValue--;
                            p_st_fan->st_FanSpeed.u8_Trend = FAN_TREND_DOWN;
                        }
                        else
                        {
                            p_st_fan->st_FanSpeed.u8_Trend = FAN_TREND_STOP;
                        }
                    }
                }
                else
                {
                    p_st_fan->st_FanSpeed.u8_ShockCount = 0;

                    if(u16_offsetVoltage > U16_FAN_DEMARCATION_VOLATGE)
                    {
                        p_st_fan->u16_PwmRegDutyValue += U16_FAN_ADJUST_V_REG_CHANGE_VALUE;
                    }
                    else
                    {
                        p_st_fan->u16_PwmRegDutyValue++;
                    }
                }
            }
            else if(FAN_TREND_DOWN == p_st_fan->st_FanSpeed.u8_Trend)
            {
                if(false == b_isRealVHigherSet)
                {
                    if(++p_st_fan->st_FanSpeed.u8_ShockCount > U8_FAN_SHOCK_COUNT)
                    {
                        if(p_st_fan->st_FanSpeed.u16_PreOffsetVoltage < u16_offsetVoltage)
                        {
                            p_st_fan->u16_PwmRegDutyValue++;
                        }

                        p_st_fan->st_FanSpeed.u8_Trend = FAN_TREND_STOP;
                    }
                    else
                    {
                        p_st_fan->u16_PwmRegDutyValue++;
                        p_st_fan->st_FanSpeed.u8_Trend = FAN_TREND_UP;
                    }
                }
                else
                {
                    p_st_fan->st_FanSpeed.u8_ShockCount = 0;

                    if(p_st_fan->u16_PwmRegDutyValue > p_st_fan->u16_PwmRegDutyDownLimitValue)
                    {
                        if(u16_offsetVoltage > U16_FAN_DEMARCATION_VOLATGE)
                        {
                            p_st_fan->u16_PwmRegDutyValue -= U16_FAN_ADJUST_V_REG_CHANGE_VALUE;
                        }
                        else
                        {
                            p_st_fan->u16_PwmRegDutyValue--;
                        }
                    }
                    else
                    {
                        p_st_fan->st_FanSpeed.u8_Trend = FAN_TREND_STOP;
                    }
                }
            }

            p_st_fan->st_FanSpeed.u16_PreOffsetVoltage = u16_offsetVoltage;
        }
        if(p_st_fan->u16_PwmRegDutyValue > p_st_fan->u16_PwmRegCycleValue)
        {
            p_st_fan->u16_PwmRegDutyValue = p_st_fan->u16_PwmRegCycleValue;
        }
    }
}

void Set_DcFanSpeed(Fan_st *p_st_fan)
{
    uint16_t u16_realSpeed = p_st_fan->st_FanSpeed.u16_CurSpeed;
    uint16_t u16_nowsec = Get_Fan_TimerU16Second();
    uint16_t u16_offsetValue = 0;

    if(0 == p_st_fan->st_FanSpeed.u16_SetSpeed)
    {
        p_st_fan->u16_PwmRegDutyValue = 0;
    }
    else
    {
        if((uint16_t)(u16_nowsec - p_st_fan->st_FanSpeed.u16_TimerSec) >= U16_FAN_ADJUST_V_INTERVAL)
        {
            p_st_fan->st_FanSpeed.u16_TimerSec = u16_nowsec;

            if(u16_realSpeed <= p_st_fan->st_FanSpeed.u16_SetSpeed)
            {
                u16_offsetValue = p_st_fan->st_FanSpeed.u16_SetSpeed - u16_realSpeed;
                p_st_fan->st_FanSpeed.b_IsCurFasterSet = false;
            }
            else
            {
                u16_offsetValue = u16_realSpeed - p_st_fan->st_FanSpeed.u16_SetSpeed;
                p_st_fan->st_FanSpeed.b_IsCurFasterSet = true;
            }

            if(u16_offsetValue > U16_FAN_ADJUST_V_RANG)
            {
                p_st_fan->st_FanSpeed.u8_AdjVStep = STEP_ZERO;
                p_st_fan->st_FanSpeed.b_IsBeginCurFasterSet = p_st_fan->st_FanSpeed.b_IsCurFasterSet;
            }

            if(STEP_ZERO == p_st_fan->st_FanSpeed.u8_AdjVStep)
            {
                if(p_st_fan->st_FanSpeed.b_IsBeginCurFasterSet != p_st_fan->st_FanSpeed.b_IsCurFasterSet)
                {
                    p_st_fan->st_FanSpeed.u8_AdjVStep++;
                }
                else
                {
                    if(false == p_st_fan->st_FanSpeed.b_IsCurFasterSet)
                    {
                        if(u16_offsetValue > U16_FAN_ADJUST_V_FAST_LIMIT)
                        {
                            p_st_fan->u16_PwmRegDutyValue += U16_FAN_ADJUST_V_REG_CHANGE_VALUE;
                        }
                        else
                        {
                            p_st_fan->u16_PwmRegDutyValue++;
                        }
                    }
                    else
                    {
                        if(p_st_fan->u16_PwmRegDutyValue > p_st_fan->u16_PwmRegDutyDownLimitValue)
                        {
                            if(u16_offsetValue > U16_FAN_ADJUST_V_FAST_LIMIT)
                            {
                                p_st_fan->u16_PwmRegDutyValue -= U16_FAN_ADJUST_V_REG_CHANGE_VALUE;
                            }
                            else
                            {
                                p_st_fan->u16_PwmRegDutyValue--;
                            }
                        }
                    }
                }
            }
            else if(STEP_ONE == p_st_fan->st_FanSpeed.u8_AdjVStep)
            {
                if(u16_offsetValue <= U16_FAN_ADJUST_V_STEADY_LIMIT)
                {
                    p_st_fan->st_FanSpeed.u8_AdjVStep = STEP_FINISH;
                }
                else
                {
                    if(false == p_st_fan->st_FanSpeed.b_IsCurFasterSet)
                    {
                        p_st_fan->u16_PwmRegDutyValue++;
                    }
                    else
                    {
                        if(p_st_fan->u16_PwmRegDutyValue > p_st_fan->u16_PwmRegDutyDownLimitValue)
                        {
                            p_st_fan->u16_PwmRegDutyValue--;
                        }
                    }
                }
            }
        }
        if(p_st_fan->u16_PwmRegDutyValue > p_st_fan->u16_PwmRegCycleValue)
        {
            p_st_fan->u16_PwmRegDutyValue = p_st_fan->u16_PwmRegCycleValue;
        }
    }
}

void Judge_FanErr(Fan_st *p_st_fan)
{
    if(true == p_st_fan->st_FanSpeed.b_IsLComputerEd)
    {
        p_st_fan->st_FanSpeed.b_IsLComputerEd = false;

        if(p_st_fan->st_FanSpeed.u16_JudgeErrorSpeed < U16_SPEED_ERROR_LIMITVALUE)
        {
            p_st_fan->b_IsError = true;
        }
        else
        {
            p_st_fan->b_IsError = false;
            p_st_fan->b_IsContinueError = false;
        }
    }
}

void Storage_FbPuelse(Fan_st *p_st_fan)
{
    p_st_fan->st_FanSpeed.u16_SFbPulseNumberBak = p_st_fan->st_FanSpeed.u16_SFbPulseNumber;
    p_st_fan->st_FanSpeed.u16_SFbPulseNumber = 0;

    if(++p_st_fan->st_FanSpeed.u8_LCount >= U8_FBPULSE_SUM_COUNT_UPLIMIT)
    {
        p_st_fan->st_FanSpeed.u8_LCount = 0;
        p_st_fan->st_FanSpeed.u16_LFbPulseNumberSum += p_st_fan->st_FanSpeed.u16_SFbPulseNumberBak;
        p_st_fan->st_FanSpeed.u16_LFbPulseNumberSumBak = p_st_fan->st_FanSpeed.u16_LFbPulseNumberSum;
        p_st_fan->st_FanSpeed.u16_LFbPulseNumberSum = 0;
        p_st_fan->st_FanSpeed.b_IsLGetFbValueEd = true;
    }
    else
    {
        p_st_fan->st_FanSpeed.u16_LFbPulseNumberSum += p_st_fan->st_FanSpeed.u16_SFbPulseNumberBak;
    }
}

void Count_FanFbPlusEdge(Fan_st *p_st_fan)
{
    if(LEVEL_LOW == p_st_fan->Read_FeedbackIO())
    {
        p_st_fan->u8_FbPulseState = LEVEL_LOW;
    }
    else
    {
        p_st_fan->u8_FbPulseState = LEVEL_HIGH;
    }

    if(p_st_fan->u8_FbPulseState != p_st_fan->u8_PreFbPulseState)
    {
        p_st_fan->u8_PreFbPulseState = p_st_fan->u8_FbPulseState;
        p_st_fan->st_FanSpeed.u16_SFbPulseNumber++;
    }
}

void Set_FanPwmPercentRunNoDelay(Fan_st *p_st_fan, uint8_t u8_pwmPercent)
{
    if(u8_pwmPercent)
    {
        p_st_fan->Ctrl_Power(true);
        p_st_fan->u8_SetPwmPercent = u8_pwmPercent;
        FanPwmPercentToPwmRegValue(p_st_fan);
        p_st_fan->Set_FanPwmRegValue(p_st_fan->u16_PwmRegDutyValue);
    }
    else
    {
        p_st_fan->u8_SetPwmPercent = 0;
        FanPwmPercentToPwmRegValue(p_st_fan);
        p_st_fan->Set_FanPwmRegValue(p_st_fan->u16_PwmRegDutyValue);
        p_st_fan->Ctrl_Power(false);
        p_st_fan->em_RunProcess = FANPOWOFF;
    }
}

void Ctrl_FanError(Fan_st *p_st_fan)
{
    uint16_t u16_nowsec = Get_Fan_TimerU16Second();

    if(STEP_ZERO == p_st_fan->u8_ErrorStep)
    {
        if(FANPOWOFF == p_st_fan->em_RunProcess)
        {
            if(THREE_WIRE_FAN == p_st_fan->u8_FanType)
            {
                p_st_fan->u16_StepInterValSec = U16_THREEWIRE_FAN_ERROR_POWEROFF_TIME;
            }
            else
            {
                p_st_fan->u16_StepInterValSec = U16_FAN_ERROR_POWEROFF_TIME;
            }

            p_st_fan->u16_StepTimerSec = u16_nowsec;
            p_st_fan->u8_ErrorStep++;
        }
        else
        {
            Ctrl_FanStop(p_st_fan);
        }
    }
    else if(STEP_ONE == p_st_fan->u8_ErrorStep)
    {
        if((uint16_t)(u16_nowsec - p_st_fan->u16_StepTimerSec) > p_st_fan->u16_StepInterValSec)
        {
            p_st_fan->u8_ErrorStep++;
        }
    }
    else if(STEP_TWO == p_st_fan->u8_ErrorStep)
    {
        Ctrl_FanRun(p_st_fan);

        if(FANRUN == p_st_fan->em_RunProcess)
        {
            if(true == p_st_fan->b_IsError)
            {
                if(++p_st_fan->u8_ErrorStartCount >= U8_FAN_ERROR_START_MAXCOUNT)
                {
                    p_st_fan->u8_ErrorStep++;
                    p_st_fan->b_IsContinueError = true;
                }
                else
                {
                    p_st_fan->u8_ErrorStep = STEP_ZERO;
                }
            }
        }
    }
    else if(STEP_THREE == p_st_fan->u8_ErrorStep)
    {
        Ctrl_FanStop(p_st_fan);

        if(FANPOWOFF == p_st_fan->em_RunProcess)
        {
            p_st_fan->u16_StepTimerSec = u16_nowsec;
            p_st_fan->u8_ErrorStep++;
        }
    }
    else if(STEP_FOUR == p_st_fan->u8_ErrorStep)
    {
        if((uint16_t)(u16_nowsec - p_st_fan->u16_StepTimerSec) > U16_FAN_ERROR_TENCYCLE_INTERVAL)
        {
            p_st_fan->u8_ErrorStartCount = 0;
            p_st_fan->u8_ErrorStep = STEP_ZERO;
        }
    }
    else
    {
        p_st_fan->u8_ErrorStep = STEP_ZERO;
    }
}

void Adjust_DcFanSpeed(Fan_st *p_st_fan)
{
    if(FAN_SETSPEEDMETHOD_SPEED == p_st_fan->u8_SetSpeedMethod)
    {
        Set_DcFanSpeed(p_st_fan);
    }
    else if(FAN_SETSPEEDMETHOD_VOLTAGE == p_st_fan->u8_SetSpeedMethod && THREE_WIRE_FAN == p_st_fan->u8_FanType)
    {
        Set_DcFanVoltage(p_st_fan);
    }
    else if(FAN_SETSPEEDMETHOD_PWM == p_st_fan->u8_SetSpeedMethod)
    {
        p_st_fan->u8_SetPwmPercent = p_st_fan->u8_AppSetPwmPercent;
        FanPwmPercentToPwmRegValue(p_st_fan);
    }
    else
    {
        FanPwmPercentToPwmRegValue(p_st_fan);
    }
}

void Ctrl_FanRun(Fan_st *p_st_fan)
{
    uint16_t u16_nowsec = Get_Fan_TimerU16Second();

    switch(p_st_fan->em_RunProcess)
    {
        case FANPOWOFF:
        case FANSTOPPWM:
            if(THREE_WIRE_FAN == p_st_fan->u8_FanType)
            {
                p_st_fan->u16_StepInterValSec = 0;
                p_st_fan->u8_SetPwmPercent = U8_FAN_START_PWM_PERCENT_THREE_WIRE;
            }
            else
            {
                p_st_fan->Ctrl_Power(true);
                // p_st_fan->u16_StepInterValSec = U16_FAN_START_PWM_DELAY;
                p_st_fan->u16_StepInterValSec = 0;
                p_st_fan->u8_SetPwmPercent = 0;
            }

            p_st_fan->u16_StepTimerSec = u16_nowsec;
            p_st_fan->em_RunProcess = FANPOWERON;
            FanPwmPercentToPwmRegValue(p_st_fan);
            break;

        case FANPOWERON:
            if((uint16_t)(u16_nowsec - p_st_fan->u16_StepTimerSec) >= p_st_fan->u16_StepInterValSec)
            {
                p_st_fan->u16_StepTimerSec = u16_nowsec;
                if(true == p_st_fan->b_IsError)
                {
                    if(THREE_WIRE_FAN == p_st_fan->u8_FanType)
                    {
                        p_st_fan->u8_SetPwmPercent = U8_FAN_ERROR_START_PWM_PERCENT_THREE_WIRE;
                    }
                    else
                    {
                        p_st_fan->u8_SetPwmPercent = U8_FAN_ERROR_START_PWM_PERCENT;
                    }
                }
                else
                {
                    if(THREE_WIRE_FAN == p_st_fan->u8_FanType)
                    {
                        p_st_fan->u8_SetPwmPercent = U8_FAN_START_PWM_PERCENT_THREE_WIRE;
                    }
                    else
                    {
                        p_st_fan->u8_SetPwmPercent = U8_FAN_START_PWM_PERCENT;
                    }
                }

                p_st_fan->em_RunProcess = FANSTARTPWM;
                p_st_fan->st_FanSpeed.u16_LFbPulseNumberSum = 0;
                p_st_fan->st_FanSpeed.u8_LCount = 0;
                p_st_fan->st_FanSpeed.b_IsLComputerEd = false;
                p_st_fan->st_FanSpeed.b_IsLGetFbValueEd = false;
            }

            break;

        case FANSTARTPWM:
            if((uint16_t)(u16_nowsec - p_st_fan->u16_StepTimerSec) >= U16_FAN_START_PWM_KEEPTIME)
            {
                if(true == p_st_fan->st_FanSpeed.b_IsLComputerEd)
                {
                    Judge_FanErr(p_st_fan);

                    if(THREE_WIRE_FAN == p_st_fan->u8_FanType)
                    {
                        p_st_fan->u8_AdjustVDelay = U8_FAN_ADJUST_V_DELAY;
                    }
                    else
                    {
                        p_st_fan->u8_AdjustVDelay = 0;
                    }

                    p_st_fan->u16_StepTimerSec = u16_nowsec;
                    p_st_fan->em_RunProcess = FANRUN;
                }
            }

            FanPwmPercentToPwmRegValue(p_st_fan);
            p_st_fan->u16_FanRunStartMinute = Get_MinuteCount();
            p_st_fan->u16_FanRunTotalSuspend = p_st_fan->u16_FanRunTotalMinutes;
            break;

        case FANRUN:
            if(p_st_fan->u8_AdjustVDelay)
            {
                if((uint16_t)(u16_nowsec - p_st_fan->u16_StepTimerSec) > 0)
                {
                    p_st_fan->u8_AdjustVDelay--;
                    p_st_fan->u16_StepTimerSec = u16_nowsec;
                }
            }
            else
            {
                Adjust_DcFanSpeed(p_st_fan);
            }

            p_st_fan->u16_FanRunTotalMinutes = p_st_fan->u16_FanRunTotalSuspend + 
            Get_MinuteElapsedTime(p_st_fan->u16_FanRunStartMinute);
            Judge_FanErr(p_st_fan);
            break;

        default:
            p_st_fan->em_RunProcess = FANPOWOFF;
            break;
    }
}

void Ctrl_FanStop(Fan_st *p_st_fan)
{
    uint16_t u16_nowsec = Get_Fan_TimerU16Second();

    switch(p_st_fan->em_RunProcess)
    {
        case FANPOWERON:
        case FANSTARTPWM:
        case FANRUN:
            if(THREE_WIRE_FAN == p_st_fan->u8_FanType)
            {
                p_st_fan->u16_StepInterValSec = 0;
            }
            else
            {
                p_st_fan->u16_StepInterValSec = U16_FAN_STOP_POW_DELAY;
            }

            p_st_fan->u16_StepTimerSec = u16_nowsec;
            p_st_fan->u8_SetPwmPercent = 0;
            p_st_fan->em_RunProcess = FANSTOPPWM;
            break;

        case FANSTOPPWM:
            if((uint16_t)(u16_nowsec - p_st_fan->u16_StepTimerSec) >= p_st_fan->u16_StepInterValSec)
            {
                p_st_fan->u16_StepTimerSec = u16_nowsec;
                p_st_fan->Ctrl_Power(false);
                p_st_fan->em_RunProcess = FANPOWOFF;
            }

            break;

        case FANPOWOFF:
            p_st_fan->u8_SetPwmPercent = 0;
            p_st_fan->Ctrl_Power(false);
            break;

        default:
            p_st_fan->u8_SetPwmPercent = 0;
            p_st_fan->Ctrl_Power(false);
            p_st_fan->em_RunProcess = FANPOWOFF;
            break;
    }

    FanPwmPercentToPwmRegValue(p_st_fan);
}

void Driver_Fan(Fan_st *p_st_fan)
{
    uint16_t u16_nowsec = Get_Fan_TimerU16Second();
    Get_FanRealSpeed(p_st_fan);

    switch(p_st_fan->em_FanState)
    {
        case FAN_STATE_ERROR:
            if(true == p_st_fan->b_IsOn)
            {
                if(true == p_st_fan->b_IsError)
                {
                    Ctrl_FanError(p_st_fan);
                }
                else
                {
                    p_st_fan->u8_ErrorStep = STEP_ZERO;
                    p_st_fan->u8_ErrorStartCount = 0;
                    p_st_fan->em_FanState = FAN_STATE_RUN;
                }
            }
            else
            {
                p_st_fan->u8_ErrorStep = STEP_ZERO;
                p_st_fan->u8_ErrorStartCount = 0;
                p_st_fan->em_FanState = FAN_STATE_STOP;
            }
            break;
        case FAN_STATE_RUN:
            if(false == p_st_fan->b_IsOn)
            {
                p_st_fan->em_FanState = FAN_STATE_STOP;
            }
            else if(true == p_st_fan->b_IsError)
            {
                p_st_fan->em_FanState = FAN_STATE_ERROR;
            }
            Ctrl_FanRun(p_st_fan);
            break;
        case FAN_STATE_STOP:
            if(true == p_st_fan->b_IsOn)
            {
                if(true == p_st_fan->b_IsError)
                {
                    p_st_fan->em_FanState = FAN_STATE_ERROR;
                }
                else
                {
                    p_st_fan->em_FanState = FAN_STATE_RUN;
                }
            }

            Ctrl_FanStop(p_st_fan);
            break;
        default:
            p_st_fan->u8_ErrorStep = STEP_ZERO;
            p_st_fan->em_FanState = FAN_STATE_STOP;
            break;
    }

    p_st_fan->Set_FanPwmRegValue(p_st_fan->u16_PwmRegDutyValue);
}

uint16_t Get_FanParameter(Fan_em em_fanType)
{
    return (ARY_Fan[em_fanType].st_FanSpeed.u16_CurSpeed);
}

uint16_t Get_FanDuty(Fan_em em_fanType)
{
    return (ARY_Fan[em_fanType].u8_CurPwmPercent);
}

uint16_t Get_FanTotalRunMinutes(Fan_em em_fanType)
{
    return (ARY_Fan[em_fanType].u16_FanRunTotalMinutes);
}

void Clear_FanTotalRunMinutes(Fan_em em_fanType)
{
    ARY_Fan[em_fanType].u16_FanRunTotalSuspend = ARY_Fan[em_fanType].u16_FanRunTotalMinutes % U16_FAN_ION_ENABLE_TOTAL_MINUTES;
    ARY_Fan[em_fanType].u16_FanRunTotalMinutes = ARY_Fan[em_fanType].u16_FanRunTotalSuspend;
    ARY_Fan[em_fanType].u16_FanRunStartMinute = Get_MinuteCount();
}

bool Get_DcFanIsError(Fan_st *p_st_fan)
{
    return p_st_fan->b_IsError;
}

bool Get_DcFanIsContinueError(Fan_st *p_st_fan)
{
    return p_st_fan->b_IsContinueError;
}
