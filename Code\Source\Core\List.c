/*!
 * @file
 * @brief Generic, unordered list
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "List.h"
#include <string.h>

bool List_B_Init(ST_List *pst_List, void *p_Buffer, uint16_t u16_NumberElements, uint8_t u8_ElementSize, const void *p_EmptyElement);
bool List_B_Insert(ST_List *pst_List, const void *p_Element);
bool List_B_Remove(ST_List *pst_List, const void *p_Element);
bool List_B_Contains(ST_List *pst_List, const void *p_Element);
bool List_B_Access(ST_List *pst_List, uint16_t u16_Index, void *p_Element);

bool List_B_Init(ST_List *pst_List, void *p_Buffer, uint16_t u16_NumberElements, uint8_t u8_ElementSize, const void *p_EmptyElement)
{
    bool b_ReturnValue = false;
    uint16_t u16_Index = 0;

    if(((ST_List *)(NULL) != pst_List) && ((void *)(NULL) != p_Buffer) &&
        (u16_NumberElements > 0) && (u8_ElementSize > 0) &&
        ((void *)(NULL) != p_EmptyElement))
    {
        pst_List->p_Buffer = p_Buffer;
        pst_List->p_EmptyElement = p_EmptyElement;
        pst_List->u16_NumberElements = u16_NumberElements;
        pst_List->u8_ElementSize = u8_ElementSize;

        for(u16_Index = 0; u16_Index < pst_List->u16_NumberElements; u16_Index++)
        {
            (void)memcpy(LIST_ELEMENT_ADDRESS(u16_Index),
                pst_List->p_EmptyElement,
                pst_List->u8_ElementSize);
        }

        b_ReturnValue = true;
    }

    return (b_ReturnValue);
}

bool List_B_Insert(ST_List *pst_List, const void *p_Element)
{
    bool b_ReturnValue = false;
    uint16_t u16_Index = 0;

    for(u16_Index = 0; u16_Index < pst_List->u16_NumberElements; u16_Index++)
    {
        if((0 == memcmp(LIST_ELEMENT_ADDRESS(u16_Index), pst_List->p_EmptyElement, pst_List->u8_ElementSize)) ||
            (0 == memcmp(LIST_ELEMENT_ADDRESS(u16_Index), p_Element, pst_List->u8_ElementSize)))
        {
            (void)memcpy(LIST_ELEMENT_ADDRESS(u16_Index), p_Element, pst_List->u8_ElementSize);
            b_ReturnValue = true;
            break;
        }
    }

    return (b_ReturnValue);
}

bool List_B_Remove(ST_List *pst_List, const void *p_Element)
{
    bool b_ReturnValue = false;
    uint16_t u16_Index = 0;

    for(u16_Index = 0; u16_Index < pst_List->u16_NumberElements; u16_Index++)
    {
        if(0 == memcmp(LIST_ELEMENT_ADDRESS(u16_Index), p_Element, pst_List->u8_ElementSize))
        {
            (void)memcpy(LIST_ELEMENT_ADDRESS(u16_Index),
                pst_List->p_EmptyElement,
                pst_List->u8_ElementSize);
            b_ReturnValue = true;
        }
    }

    return (b_ReturnValue);
}

bool List_B_Contains(ST_List *pst_List, const void *p_Element)
{
    bool b_ElementFound = false;
    uint16_t u16_Index = 0;

    for(u16_Index = 0;
        (u16_Index < pst_List->u16_NumberElements) &&
        (false == b_ElementFound);
        u16_Index++)
    {
        if(0 == memcmp(LIST_ELEMENT_ADDRESS(u16_Index), p_Element, pst_List->u8_ElementSize))
        {
            b_ElementFound = true;
        }
    }

    return (b_ElementFound);
}

bool List_B_Access(ST_List *pst_List, uint16_t u16_Index, void *p_Element)
{
    bool b_ReturnValue = false;

    if(((ST_List *)(NULL) != pst_List) && ((void *)(NULL) != p_Element) &&
        (u16_Index < pst_List->u16_NumberElements))
    {
        (void)memcpy(p_Element, LIST_ELEMENT_ADDRESS(u16_Index), pst_List->u8_ElementSize);
        b_ReturnValue = true;
    }

    return (b_ReturnValue);
}
